const UserNotification = require('../models/user-notification');
const Notification = require('../models/notification');
const User = require('../models/user');
let nodemailer;
try {
  nodemailer = require('nodemailer');
} catch (error) {
  console.warn('Nodemailer not installed - email notifications will be disabled');
}

/**
 * Notification Delivery Service
 * Handles the actual delivery of notifications through various channels
 */
class NotificationDeliveryService {
  constructor() {
    this.emailTransporter = null;
    this.initializeEmailTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeEmailTransporter() {
    try {
      // Only initialize email transporter if nodemailer is available and email configuration is provided
      if (nodemailer && typeof nodemailer.createTransporter === 'function' && process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
        this.emailTransporter = nodemailer.createTransporter({
          host: process.env.EMAIL_HOST,
          port: process.env.EMAIL_PORT || 587,
          secure: process.env.EMAIL_SECURE === 'true',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD
          }
        });
        console.log('Email transporter initialized successfully');
      } else {
        console.log('Email configuration not provided - email notifications will be disabled');
        this.emailTransporter = null;
      }
    } catch (error) {
      console.error('Error initializing email transporter:', error);
      this.emailTransporter = null;
    }
  }

  /**
   * Deliver notification to all recipients
   * @param {String} notificationId - Notification ID
   * @returns {Promise<Object>} Delivery results
   */
  async deliverNotification(notificationId) {
    try {
      const notification = await Notification.findById(notificationId);
      if (!notification) {
        throw new Error('Notification not found');
      }

      // Get all user notifications for this notification
      const userNotifications = await UserNotification.find({
        notificationId: notificationId,
        status: 'PENDING'
      }).populate('userId');

      const deliveryResults = {
        total: userNotifications.length,
        successful: 0,
        failed: 0,
        results: []
      };

      // Process each user notification
      for (const userNotification of userNotifications) {
        try {
          const result = await this.deliverToUser(notification, userNotification);
          deliveryResults.results.push(result);
          
          if (result.success) {
            deliveryResults.successful++;
          } else {
            deliveryResults.failed++;
          }
        } catch (error) {
          console.error(`Error delivering to user ${userNotification.userId._id}:`, error);
          deliveryResults.failed++;
          deliveryResults.results.push({
            userId: userNotification.userId._id,
            success: false,
            error: error.message
          });
        }
      }

      // Update notification delivery status
      await this.updateNotificationDeliveryStatus(notification, deliveryResults);

      return deliveryResults;
    } catch (error) {
      console.error('Error delivering notification:', error);
      throw error;
    }
  }

  /**
   * Deliver notification to a specific user
   * @param {Object} notification - Notification object
   * @param {Object} userNotification - User notification object
   * @returns {Promise<Object>} Delivery result
   */
  async deliverToUser(notification, userNotification) {
    const user = userNotification.userId;
    const deliveryMethods = notification.deliveryMethods;
    const results = {
      userId: user._id,
      success: true,
      methods: {}
    };

    try {
      // Get user's effective preferences
      const preferences = await userNotification.getEffectivePreferences();

      // Deliver via in-app notification
      if (deliveryMethods.inApp && preferences.inApp) {
        try {
          await this.deliverInApp(notification, userNotification, user);
          results.methods.inApp = { success: true, deliveredAt: new Date() };
          await userNotification.updateDeliveryStatus('inApp', 'DELIVERED');
        } catch (error) {
          console.error('In-app delivery failed:', error);
          results.methods.inApp = { success: false, error: error.message };
          await userNotification.updateDeliveryStatus('inApp', 'FAILED', error.message);
          results.success = false;
        }
      } else {
        await userNotification.updateDeliveryStatus('inApp', 'SKIPPED');
      }

      // Deliver via email
      if (deliveryMethods.email && preferences.email && user.email) {
        try {
          await this.deliverEmail(notification, userNotification, user);
          results.methods.email = { success: true, deliveredAt: new Date() };
          await userNotification.updateDeliveryStatus('email', 'DELIVERED', null, { emailAddress: user.email });
        } catch (error) {
          console.error('Email delivery failed:', error);
          results.methods.email = { success: false, error: error.message };
          await userNotification.updateDeliveryStatus('email', 'FAILED', error.message, { emailAddress: user.email });
          results.success = false;
        }
      } else {
        await userNotification.updateDeliveryStatus('email', 'SKIPPED');
      }

      // Deliver via SMS
      if (deliveryMethods.sms && preferences.sms && user.phone) {
        try {
          await this.deliverSMS(notification, userNotification, user);
          results.methods.sms = { success: true, deliveredAt: new Date() };
          await userNotification.updateDeliveryStatus('sms', 'DELIVERED', null, { phoneNumber: user.phone });
        } catch (error) {
          console.error('SMS delivery failed:', error);
          results.methods.sms = { success: false, error: error.message };
          await userNotification.updateDeliveryStatus('sms', 'FAILED', error.message, { phoneNumber: user.phone });
          results.success = false;
        }
      } else {
        await userNotification.updateDeliveryStatus('sms', 'SKIPPED');
      }

      // Deliver via push notification
      if (deliveryMethods.push && preferences.push) {
        try {
          await this.deliverPush(notification, userNotification, user);
          results.methods.push = { success: true, deliveredAt: new Date() };
          await userNotification.updateDeliveryStatus('push', 'DELIVERED');
        } catch (error) {
          console.error('Push delivery failed:', error);
          results.methods.push = { success: false, error: error.message };
          await userNotification.updateDeliveryStatus('push', 'FAILED', error.message);
          results.success = false;
        }
      } else {
        await userNotification.updateDeliveryStatus('push', 'SKIPPED');
      }

      // Update overall user notification status
      const overallStatus = userNotification.overallDeliveryStatus;
      userNotification.status = overallStatus;
      await userNotification.save();

    } catch (error) {
      console.error(`Error in deliverToUser for user ${user._id}:`, error);
      results.success = false;
      results.error = error.message;
      
      userNotification.status = 'FAILED';
      await userNotification.save();
    }

    return results;
  }

  /**
   * Deliver in-app notification
   * @param {Object} notification - Notification object
   * @param {Object} userNotification - User notification object
   * @param {Object} user - User object
   * @returns {Promise<void>}
   */
  async deliverInApp(notification, userNotification, user) {
    // In-app notifications are delivered by creating the UserNotification record
    // The frontend will poll or use WebSocket to get these notifications
    
    // Set expiry if specified
    if (notification.templateId) {
      const template = await require('../models/notification-template').findById(notification.templateId);
      if (template && template.defaultSettings.expiryHours) {
        const expiryDate = new Date();
        expiryDate.setHours(expiryDate.getHours() + template.defaultSettings.expiryHours);
        userNotification.expiresAt = expiryDate;
        await userNotification.save();
      }
    }

    // Trigger WebSocket notification if available
    try {
      const WebSocketService = require('./websocket-service');
      await WebSocketService.sendToUser(user._id.toString(), {
        type: 'NEW_NOTIFICATION',
        data: {
          id: userNotification._id,
          notificationId: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          actions: notification.actions,
          createdAt: userNotification.createdAt
        }
      });
    } catch (error) {
      console.warn('WebSocket notification failed, but in-app delivery succeeded:', error.message);
    }
  }

  /**
   * Deliver email notification
   * @param {Object} notification - Notification object
   * @param {Object} userNotification - User notification object
   * @param {Object} user - User object
   * @returns {Promise<void>}
   */
  async deliverEmail(notification, userNotification, user) {
    if (!this.emailTransporter) {
      throw new Error('Email transporter not configured');
    }

    // Get email content from notification or rendered template
    let emailContent = {
      subject: notification.title,
      html: this.generateEmailHTML(notification, user),
      text: notification.message
    };

    // Use rendered content if available
    if (notification.renderedContent && notification.renderedContent.email) {
      emailContent = {
        subject: notification.renderedContent.email.subject,
        html: notification.renderedContent.email.htmlBody,
        text: notification.renderedContent.email.textBody
      };
    }

    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: user.email,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text
    };

    // Add attachments if available
    if (notification.renderedContent && notification.renderedContent.email && notification.renderedContent.email.attachments) {
      mailOptions.attachments = notification.renderedContent.email.attachments;
    }

    await this.emailTransporter.sendMail(mailOptions);
  }

  /**
   * Generate HTML email content
   * @param {Object} notification - Notification object
   * @param {Object} user - User object
   * @returns {String} HTML content
   */
  generateEmailHTML(notification, user) {
    const userName = user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}` 
      : user.username;

    let actionsHTML = '';
    if (notification.actions && notification.actions.length > 0) {
      const actionButtons = notification.actions.map(action => {
        const buttonStyle = this.getButtonStyle(action.style);
        return `<a href="${action.url}" style="${buttonStyle}">${action.label}</a>`;
      }).join(' ');
      
      actionsHTML = `
        <div style="margin: 20px 0; text-align: center;">
          ${actionButtons}
        </div>
      `;
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${notification.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h1 style="color: #1976d2; margin: 0; font-size: 24px;">${notification.title}</h1>
        </div>
        
        <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0;">
          <p>Dear ${userName},</p>
          
          <div style="margin: 20px 0;">
            ${notification.message.replace(/\n/g, '<br>')}
          </div>
          
          ${actionsHTML}
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666;">
            <p>This is an automated notification from the Funding Screening Application.</p>
            <p>If you have any questions, please contact your system administrator.</p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #999;">
          <p>&copy; 2025 Funding Screening Application. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Get button style for email actions
   * @param {String} style - Button style
   * @returns {String} CSS style
   */
  getButtonStyle(style) {
    const baseStyle = 'display: inline-block; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; margin: 5px;';
    
    switch (style) {
      case 'PRIMARY':
        return baseStyle + 'background-color: #1976d2; color: white;';
      case 'SUCCESS':
        return baseStyle + 'background-color: #4caf50; color: white;';
      case 'WARNING':
        return baseStyle + 'background-color: #ff9800; color: white;';
      case 'DANGER':
        return baseStyle + 'background-color: #f44336; color: white;';
      case 'SECONDARY':
      default:
        return baseStyle + 'background-color: #6c757d; color: white;';
    }
  }

  /**
   * Deliver SMS notification
   * @param {Object} notification - Notification object
   * @param {Object} userNotification - User notification object
   * @param {Object} user - User object
   * @returns {Promise<void>}
   */
  async deliverSMS(notification, userNotification, user) {
    // SMS delivery would integrate with SMS service provider (Twilio, etc.)
    // For now, we'll simulate the delivery
    
    let smsMessage = notification.message;
    
    // Use rendered SMS content if available
    if (notification.renderedContent && notification.renderedContent.sms) {
      smsMessage = notification.renderedContent.sms.message;
    }

    // Truncate message to SMS length limit
    if (smsMessage.length > 160) {
      smsMessage = smsMessage.substring(0, 157) + '...';
    }

    // Simulate SMS delivery
    console.log(`SMS would be sent to ${user.phone}: ${smsMessage}`);
    
    // In production, integrate with SMS provider:
    /*
    const smsProvider = require('your-sms-provider');
    await smsProvider.send({
      to: user.phone,
      message: smsMessage
    });
    */
  }

  /**
   * Deliver push notification
   * @param {Object} notification - Notification object
   * @param {Object} userNotification - User notification object
   * @param {Object} user - User object
   * @returns {Promise<void>}
   */
  async deliverPush(notification, userNotification, user) {
    // Push notification delivery would integrate with push service (Firebase, etc.)
    // For now, we'll simulate the delivery
    
    let pushContent = {
      title: notification.title,
      body: notification.message
    };

    // Use rendered push content if available
    if (notification.renderedContent && notification.renderedContent.push) {
      pushContent = notification.renderedContent.push;
    }

    // Simulate push delivery
    console.log(`Push notification would be sent to user ${user._id}:`, pushContent);
    
    // In production, integrate with push provider:
    /*
    const pushProvider = require('your-push-provider');
    await pushProvider.send({
      userId: user._id,
      title: pushContent.title,
      body: pushContent.body,
      icon: pushContent.icon,
      clickAction: pushContent.clickAction
    });
    */
  }

  /**
   * Update notification delivery status
   * @param {Object} notification - Notification object
   * @param {Object} deliveryResults - Delivery results
   * @returns {Promise<void>}
   */
  async updateNotificationDeliveryStatus(notification, deliveryResults) {
    try {
      const updateData = {
        'analytics.deliveredCount': deliveryResults.successful,
        'analytics.lastAnalyticsUpdate': new Date()
      };

      // Update delivery status based on results
      if (deliveryResults.successful === deliveryResults.total) {
        updateData['deliveryStatus.inApp.status'] = 'DELIVERED';
        updateData['deliveryStatus.email.status'] = 'DELIVERED';
        updateData['deliveryStatus.sms.status'] = 'DELIVERED';
        updateData['deliveryStatus.push.status'] = 'DELIVERED';
      } else if (deliveryResults.successful > 0) {
        updateData['deliveryStatus.inApp.status'] = 'DELIVERED';
        updateData['deliveryStatus.email.status'] = 'DELIVERED';
        updateData['deliveryStatus.sms.status'] = 'DELIVERED';
        updateData['deliveryStatus.push.status'] = 'DELIVERED';
      } else {
        updateData['deliveryStatus.inApp.status'] = 'FAILED';
        updateData['deliveryStatus.email.status'] = 'FAILED';
        updateData['deliveryStatus.sms.status'] = 'FAILED';
        updateData['deliveryStatus.push.status'] = 'FAILED';
      }

      await Notification.findByIdAndUpdate(notification._id, { $set: updateData });
    } catch (error) {
      console.error('Error updating notification delivery status:', error);
    }
  }

  /**
   * Retry failed deliveries
   * @param {String} notificationId - Notification ID (optional)
   * @returns {Promise<Array>} Retry results
   */
  async retryFailedDeliveries(notificationId = null) {
    try {
      const query = { status: 'FAILED' };
      if (notificationId) {
        query.notificationId = notificationId;
      }

      const failedUserNotifications = await UserNotification.find(query)
        .populate('notificationId')
        .populate('userId');

      const retryResults = [];

      for (const userNotification of failedUserNotifications) {
        if (userNotification.shouldRetry()) {
          try {
            await userNotification.incrementRetry();
            const result = await this.deliverToUser(userNotification.notificationId, userNotification);
            retryResults.push({
              userNotificationId: userNotification._id,
              success: result.success,
              attempt: userNotification.retryCount
            });
          } catch (error) {
            console.error(`Retry failed for user notification ${userNotification._id}:`, error);
            retryResults.push({
              userNotificationId: userNotification._id,
              success: false,
              error: error.message,
              attempt: userNotification.retryCount
            });
          }
        }
      }

      return retryResults;
    } catch (error) {
      console.error('Error retrying failed deliveries:', error);
      throw error;
    }
  }
}

module.exports = new NotificationDeliveryService();