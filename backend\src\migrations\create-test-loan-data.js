const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

// Import models
const LoanOffer = require('../models/loan-offer');
const Loan = require('../models/loan');

async function createTestLoanData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if test data already exists
    const existingOffer = await LoanOffer.findOne({ offerId: 'OFFER-202406-0001' });
    const existingLoan = await Loan.findOne({ applicationId: 'APP-2024-001' });
    
    if (existingOffer && existingLoan) {
      console.log('Test data already exists. Skipping creation.');
      console.log('Existing offer:', existingOffer.offerId);
      console.log('Existing loan:', existingLoan.loanNumber);
      return;
    }

    let testOffer = existingOffer;
    if (!testOffer) {
      // Create a test loan offer first
      testOffer = new LoanOffer({
      offerId: 'OFFER-202406-0001', // Provide offerId explicitly
      applicationId: 'APP-2024-001',
      productId: 'PROD-001',
      offeredAmount: 500000,
      approvedAmount: 500000,
      interestRate: 12.5,
      term: 12,
      termUnit: 'MONTHS',
      repaymentFrequency: 'MONTHLY',
      monthlyPayment: 44489.58, // Provide required fields
      totalRepayment: 533875,
      totalInterest: 33875,
      fees: {
        originationFee: 5000,
        processingFee: 2500
      },
      collateralRequired: false,
      status: 'ACCEPTED',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      acceptedDate: new Date(),
      riskAssessment: {
        creditScore: 720,
        riskRating: 'LOW',
        riskFactors: [
          { factor: 'Credit Score', score: 85, impact: 'Positive' },
          { factor: 'Business Age', score: 75, impact: 'Positive' },
          { factor: 'Revenue Stability', score: 80, impact: 'Positive' }
        ]
      }
    });
    
      // Calculate repayment schedule
      testOffer.calculateRepaymentSchedule();
      await testOffer.save();

      console.log('Created test loan offer:', testOffer.offerId);
    }

    if (!existingLoan) {
      // Create a test loan based on the offer
      const testLoan = new Loan({
        loanNumber: 'LN-2024-000001', // Provide loanNumber explicitly
        offerId: testOffer.offerId,
        applicationId: testOffer.applicationId,
        productId: testOffer.productId,
        principalAmount: testOffer.approvedAmount || testOffer.offeredAmount,
        disbursedAmount: testOffer.approvedAmount || testOffer.offeredAmount,
        interestRate: testOffer.interestRate,
        term: testOffer.term,
        termUnit: testOffer.termUnit,
        status: 'ACTIVE',
        disbursementDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
        firstPaymentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        maturityDate: new Date(Date.now() + 300 * 24 * 60 * 60 * 1000), // 300 days from now
        outstandingPrincipal: 458333.34,
        outstandingInterest: 4791.67,
        outstandingFees: 0,
        totalOutstanding: 463125.01,
        totalPaidPrincipal: 41666.66,
        totalPaidInterest: 2822.92,
        totalPaidFees: 7500,
        nextPaymentDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        nextPaymentAmount: 44489.58,
        daysOverdue: 0,
        payments: [
        {
          paymentId: 'PAY-001',
          paymentNumber: 1,
          dueDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          paymentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          principalAmount: 41666.66,
          interestAmount: 2822.92,
          feesAmount: 7500,
          totalAmount: 51989.58,
          paidAmount: 51989.58,
          balance: 0,
          status: 'PAID',
          paymentMethod: 'BANK_TRANSFER',
          transactionReference: 'TRX-001'
        },
        {
          paymentId: 'PAY-002',
          paymentNumber: 2,
          dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          principalAmount: 41666.67,
          interestAmount: 2822.91,
          totalAmount: 44489.58,
          status: 'SCHEDULED'
        }
      ]
    });
      await testLoan.save();

      console.log('Created test loan:', testLoan.loanNumber);
    }

    // Create another test loan that's overdue
    const overdueOffer = new LoanOffer({
      offerId: 'OFFER-202406-0002', // Provide offerId explicitly
      applicationId: 'APP-2024-002',
      productId: 'PROD-002',
      offeredAmount: 250000,
      approvedAmount: 250000,
      interestRate: 15,
      term: 6,
      termUnit: 'MONTHS',
      repaymentFrequency: 'MONTHLY',
      monthlyPayment: 43424.94, // Provide required fields
      totalRepayment: 260549.64,
      totalInterest: 10549.64,
      fees: {
        originationFee: 2500,
        processingFee: 1500
      },
      collateralRequired: false,
      status: 'ACCEPTED',
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      acceptedDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      riskAssessment: {
        creditScore: 650,
        riskRating: 'MEDIUM',
        riskFactors: [
          { factor: 'Credit Score', score: 65, impact: 'Neutral' },
          { factor: 'Business Age', score: 60, impact: 'Neutral' },
          { factor: 'Revenue Stability', score: 70, impact: 'Positive' }
        ]
      }
    });
    
    // Calculate repayment schedule
    overdueOffer.calculateRepaymentSchedule();
    await overdueOffer.save();

    const overdueLoan = new Loan({
      loanNumber: 'LN-2024-000002', // Provide loanNumber explicitly
      offerId: overdueOffer.offerId,
      applicationId: overdueOffer.applicationId,
      productId: overdueOffer.productId,
      principalAmount: overdueOffer.approvedAmount || overdueOffer.offeredAmount,
      disbursedAmount: overdueOffer.approvedAmount || overdueOffer.offeredAmount,
      interestRate: overdueOffer.interestRate,
      term: overdueOffer.term,
      termUnit: overdueOffer.termUnit,
      status: 'ACTIVE',
      disbursementDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
      firstPaymentDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
      maturityDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      outstandingPrincipal: 166666.67,
      outstandingInterest: 2083.33,
      outstandingFees: 500, // Late fee
      totalOutstanding: 169250,
      totalPaidPrincipal: 83333.33,
      totalPaidInterest: 4166.67,
      totalPaidFees: 4000,
      nextPaymentDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago (overdue)
      nextPaymentAmount: 43424.94,
      daysOverdue: 10,
      payments: [
        {
          paymentId: 'PAY-003',
          paymentNumber: 1,
          dueDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          paymentDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          principalAmount: 41666.67,
          interestAmount: 2083.33,
          feesAmount: 4000,
          totalAmount: 47750,
          paidAmount: 47750,
          balance: 0,
          status: 'PAID',
          paymentMethod: 'CASH',
          transactionReference: 'CASH-001'
        },
        {
          paymentId: 'PAY-004',
          paymentNumber: 2,
          dueDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          paymentDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000),
          principalAmount: 41666.66,
          interestAmount: 2083.34,
          totalAmount: 43750,
          paidAmount: 43750,
          balance: 0,
          status: 'PAID',
          paymentMethod: 'BANK_TRANSFER',
          transactionReference: 'TRX-002'
        },
        {
          paymentId: 'PAY-005',
          paymentNumber: 3,
          dueDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          principalAmount: 41666.67,
          interestAmount: 2083.33,
          feesAmount: 500, // Late fee
          totalAmount: 44250,
          status: 'LATE'
        }
      ]
    });
    await overdueLoan.save();

    console.log('Created overdue loan:', overdueLoan.loanNumber);

    console.log('\nTest loan data created successfully!');
    console.log('You can now test the payment recording interface with these loans.');

  } catch (error) {
    console.error('Error creating test loan data:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
createTestLoanData();