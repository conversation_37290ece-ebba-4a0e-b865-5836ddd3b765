const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const workflowService = require('../services/workflow-service');

// Get all workflows
router.get('/', authenticateToken, async (req, res) => {
  try {
    const workflows = await workflowService.getAllWorkflows(req.query);
    res.json(workflows);
  } catch (error) {
    console.error('Error fetching workflows:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get a single workflow
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const workflow = await workflowService.getWorkflowById(req.params.id);
    res.json(workflow);
  } catch (error) {
    console.error('Error fetching workflow:', error);
    res.status(404).json({ error: error.message });
  }
});

// Create a new workflow
router.post('/', authenticateToken, async (req, res) => {
  try {
    const workflow = await workflowService.createWorkflow(req.body, req.user.id);
    res.status(201).json(workflow);
  } catch (error) {
    console.error('Error creating workflow:', error);
    res.status(400).json({ error: error.message });
  }
});

// Update a workflow
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const workflow = await workflowService.updateWorkflow(req.params.id, req.body, req.user.id);
    res.json(workflow);
  } catch (error) {
    console.error('Error updating workflow:', error);
    res.status(400).json({ error: error.message });
  }
});

// Delete a workflow
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const result = await workflowService.deleteWorkflow(req.params.id);
    res.json(result);
  } catch (error) {
    console.error('Error deleting workflow:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get workflow statistics
router.get('/:id/statistics', authenticateToken, async (req, res) => {
  try {
    const statistics = await workflowService.getWorkflowStatistics(req.params.id);
    res.json(statistics);
  } catch (error) {
    console.error('Error fetching workflow statistics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Assign workflow to application
router.post('/assign', authenticateToken, async (req, res) => {
  try {
    const { applicationId, workflowId } = req.body;
    const assignment = await workflowService.assignWorkflowToApplication(
      applicationId,
      workflowId,
      req.user.id
    );
    res.status(201).json(assignment);
  } catch (error) {
    console.error('Error assigning workflow:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get application workflow
router.get('/application/:applicationId', authenticateToken, async (req, res) => {
  try {
    const assignment = await workflowService.getApplicationWorkflow(req.params.applicationId);
    res.json(assignment);
  } catch (error) {
    console.error('Error fetching application workflow:', error);
    res.status(404).json({ error: error.message });
  }
});

// Update application stage
router.put('/application/:applicationId/stage', authenticateToken, async (req, res) => {
  try {
    const assignment = await workflowService.updateApplicationStage(
      req.params.applicationId,
      req.body,
      req.user.id
    );
    res.json(assignment);
  } catch (error) {
    console.error('Error updating application stage:', error);
    res.status(400).json({ error: error.message });
  }
});

// Override workflow stage
router.post('/application/:applicationId/override', authenticateToken, async (req, res) => {
  try {
    const assignment = await workflowService.overrideWorkflowStage(
      req.params.applicationId,
      req.body,
      req.user.id
    );
    res.json(assignment);
  } catch (error) {
    console.error('Error overriding workflow stage:', error);
    res.status(400).json({ error: error.message });
  }
});

module.exports = router;