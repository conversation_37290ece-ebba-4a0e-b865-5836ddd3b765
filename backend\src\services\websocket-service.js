const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const User = require('../models/user');

/**
 * WebSocket Service
 * Handles real-time communication for notifications
 */
class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map of userId -> WebSocket connections
    this.JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
  }

  /**
   * Initialize WebSocket server
   * @param {Object} server - HTTP server instance
   * @returns {void}
   */
  initialize(server) {
    try {
      this.wss = new WebSocket.Server({
        server,
        path: '/ws/notifications',
        verifyClient: this.verifyClient.bind(this)
      });

      this.wss.on('connection', this.handleConnection.bind(this));
      this.wss.on('error', this.handleError.bind(this));

      console.log('WebSocket server initialized for notifications');
    } catch (error) {
      console.error('Error initializing WebSocket server:', error);
    }
  }

  /**
   * Verify client connection
   * @param {Object} info - Connection info
   * @returns {Boolean} Whether to accept the connection
   */
  async verifyClient(info) {
    try {
      const url = new URL(info.req.url, `http://${info.req.headers.host}`);
      const token = url.searchParams.get('token');

      if (!token) {
        console.log('WebSocket connection rejected: No token provided');
        return false;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, this.JWT_SECRET);
      
      // Check if user exists and is active
      const user = await User.findById(decoded.sub);
      if (!user || !user.isActive) {
        console.log('WebSocket connection rejected: Invalid or inactive user');
        return false;
      }

      // Store user info for later use
      info.req.user = decoded;
      return true;
    } catch (error) {
      console.log('WebSocket connection rejected:', error.message);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} req - HTTP request
   * @returns {void}
   */
  handleConnection(ws, req) {
    try {
      const userId = req.user.sub;
      const username = req.user.username;

      console.log(`WebSocket connected: User ${username} (${userId})`);

      // Store connection
      if (!this.clients.has(userId)) {
        this.clients.set(userId, new Set());
      }
      this.clients.get(userId).add(ws);

      // Set up connection properties
      ws.userId = userId;
      ws.username = username;
      ws.isAlive = true;

      // Send welcome message
      this.sendToConnection(ws, {
        type: 'CONNECTION_ESTABLISHED',
        data: {
          message: 'Connected to notification service',
          timestamp: new Date().toISOString()
        }
      });

      // Set up event handlers
      ws.on('message', (message) => this.handleMessage(ws, message));
      ws.on('close', () => this.handleDisconnection(ws));
      ws.on('error', (error) => this.handleConnectionError(ws, error));
      ws.on('pong', () => { ws.isAlive = true; });

      // Send any pending notifications
      this.sendPendingNotifications(userId);

    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close(1011, 'Server error');
    }
  }

  /**
   * Handle incoming WebSocket message
   * @param {WebSocket} ws - WebSocket connection
   * @param {String} message - Raw message
   * @returns {void}
   */
  handleMessage(ws, message) {
    try {
      const data = JSON.parse(message);
      
      switch (data.type) {
        case 'PING':
          this.sendToConnection(ws, { type: 'PONG', timestamp: new Date().toISOString() });
          break;
          
        case 'MARK_AS_READ':
          this.handleMarkAsRead(ws, data.payload);
          break;
          
        case 'MARK_AS_CLICKED':
          this.handleMarkAsClicked(ws, data.payload);
          break;
          
        case 'GET_UNREAD_COUNT':
          this.sendUnreadCount(ws);
          break;
          
        case 'SUBSCRIBE_TO_UPDATES':
          // Client is requesting to receive real-time updates
          ws.subscribed = true;
          break;
          
        case 'JOIN_CHAT_ROOM':
          this.handleJoinChatRoom(ws, data.payload);
          break;
          
        case 'LEAVE_CHAT_ROOM':
          this.handleLeaveChatRoom(ws, data.payload);
          break;
          
        case 'TYPING_START':
          this.handleTypingStart(ws, data.payload);
          break;
          
        case 'TYPING_STOP':
          this.handleTypingStop(ws, data.payload);
          break;
          
        default:
          console.log(`Unknown message type: ${data.type}`);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      this.sendToConnection(ws, {
        type: 'ERROR',
        data: { message: 'Invalid message format' }
      });
    }
  }

  /**
   * Handle mark as read request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  async handleMarkAsRead(ws, payload) {
    try {
      const { userNotificationId } = payload;
      
      const NotificationService = require('./notification-service');
      await NotificationService.markAsRead(ws.userId, userNotificationId);
      
      this.sendToConnection(ws, {
        type: 'MARKED_AS_READ',
        data: { userNotificationId }
      });
      
      // Send updated unread count
      this.sendUnreadCount(ws);
    } catch (error) {
      console.error('Error marking notification as read:', error);
      this.sendToConnection(ws, {
        type: 'ERROR',
        data: { message: 'Failed to mark notification as read' }
      });
    }
  }

  /**
   * Handle mark as clicked request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  async handleMarkAsClicked(ws, payload) {
    try {
      const { userNotificationId, actionLabel } = payload;
      
      const NotificationService = require('./notification-service');
      await NotificationService.handleActionClick(ws.userId, userNotificationId, actionLabel);
      
      this.sendToConnection(ws, {
        type: 'MARKED_AS_CLICKED',
        data: { userNotificationId, actionLabel }
      });
    } catch (error) {
      console.error('Error marking notification as clicked:', error);
      this.sendToConnection(ws, {
        type: 'ERROR',
        data: { message: 'Failed to mark notification as clicked' }
      });
    }
  }

  /**
   * Send unread notification count to client
   * @param {WebSocket} ws - WebSocket connection
   * @returns {void}
   */
  async sendUnreadCount(ws) {
    try {
      const NotificationService = require('./notification-service');
      const unreadNotifications = await NotificationService.getUnreadNotifications(ws.userId, 1);
      
      // Get total count
      const UserNotification = require('../models/user-notification');
      const unreadCount = await UserNotification.countDocuments({
        userId: ws.userId,
        isRead: false,
        status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
      });
      
      this.sendToConnection(ws, {
        type: 'UNREAD_COUNT',
        data: { count: unreadCount }
      });
    } catch (error) {
      console.error('Error sending unread count:', error);
    }
  }

  /**
   * Send pending notifications to newly connected user
   * @param {String} userId - User ID
   * @returns {void}
   */
  async sendPendingNotifications(userId) {
    try {
      const NotificationService = require('./notification-service');
      const unreadNotifications = await NotificationService.getUnreadNotifications(userId, 10);
      
      if (unreadNotifications.length > 0) {
        this.sendToUser(userId, {
          type: 'PENDING_NOTIFICATIONS',
          data: {
            notifications: unreadNotifications.map(un => ({
              id: un._id,
              notificationId: un.notificationId._id,
              title: un.notificationId.title,
              message: un.notificationId.message,
              type: un.notificationId.type,
              category: un.notificationId.category,
              priority: un.notificationId.priority,
              actions: un.notificationId.actions,
              createdAt: un.createdAt
            })),
            total: unreadNotifications.length
          }
        });
      }
    } catch (error) {
      console.error('Error sending pending notifications:', error);
    }
  }

  /**
   * Handle WebSocket disconnection
   * @param {WebSocket} ws - WebSocket connection
   * @returns {void}
   */
  handleDisconnection(ws) {
    try {
      console.log(`WebSocket disconnected: User ${ws.username} (${ws.userId})`);
      
      if (ws.userId && this.clients.has(ws.userId)) {
        const userConnections = this.clients.get(ws.userId);
        userConnections.delete(ws);
        
        if (userConnections.size === 0) {
          this.clients.delete(ws.userId);
        }
      }
    } catch (error) {
      console.error('Error handling WebSocket disconnection:', error);
    }
  }

  /**
   * Handle WebSocket connection error
   * @param {WebSocket} ws - WebSocket connection
   * @param {Error} error - Error object
   * @returns {void}
   */
  handleConnectionError(ws, error) {
    console.error(`WebSocket error for user ${ws.username}:`, error);
  }

  /**
   * Handle WebSocket server error
   * @param {Error} error - Error object
   * @returns {void}
   */
  handleError(error) {
    console.error('WebSocket server error:', error);
  }

  /**
   * Send message to a specific user
   * @param {String} userId - User ID
   * @param {Object} message - Message to send
   * @returns {Promise<Boolean>} Success status
   */
  async sendToUser(userId, message) {
    try {
      const userConnections = this.clients.get(userId);
      
      if (!userConnections || userConnections.size === 0) {
        console.log(`No active connections for user ${userId}`);
        return false;
      }

      let sentCount = 0;
      for (const ws of userConnections) {
        if (ws.readyState === WebSocket.OPEN) {
          this.sendToConnection(ws, message);
          sentCount++;
        } else {
          // Remove dead connection
          userConnections.delete(ws);
        }
      }

      // Clean up empty connection sets
      if (userConnections.size === 0) {
        this.clients.delete(userId);
      }

      return sentCount > 0;
    } catch (error) {
      console.error(`Error sending message to user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Send message to multiple users
   * @param {Array} userIds - Array of user IDs
   * @param {Object} message - Message to send
   * @returns {Promise<Object>} Send results
   */
  async sendToUsers(userIds, message) {
    const results = {
      total: userIds.length,
      sent: 0,
      failed: 0
    };

    for (const userId of userIds) {
      try {
        const success = await this.sendToUser(userId, message);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
        }
      } catch (error) {
        console.error(`Error sending to user ${userId}:`, error);
        results.failed++;
      }
    }

    return results;
  }

  /**
   * Broadcast message to all connected users
   * @param {Object} message - Message to send
   * @param {Array} excludeUsers - User IDs to exclude (optional)
   * @returns {Promise<Object>} Broadcast results
   */
  async broadcast(message, excludeUsers = []) {
    const results = {
      total: 0,
      sent: 0,
      failed: 0
    };

    for (const [userId, connections] of this.clients) {
      if (excludeUsers.includes(userId)) {
        continue;
      }

      results.total++;
      
      try {
        const success = await this.sendToUser(userId, message);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
        }
      } catch (error) {
        console.error(`Error broadcasting to user ${userId}:`, error);
        results.failed++;
      }
    }

    return results;
  }

  /**
   * Send message to a specific WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Message to send
   * @returns {void}
   */
  sendToConnection(ws, message) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          ...message,
          timestamp: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('Error sending message to WebSocket connection:', error);
    }
  }

  /**
   * Get connected users count
   * @returns {Number} Number of connected users
   */
  getConnectedUsersCount() {
    return this.clients.size;
  }

  /**
   * Get connection info for a user
   * @param {String} userId - User ID
   * @returns {Object} Connection info
   */
  getUserConnectionInfo(userId) {
    const connections = this.clients.get(userId);
    return {
      userId,
      isConnected: !!connections && connections.size > 0,
      connectionCount: connections ? connections.size : 0
    };
  }

  /**
   * Start heartbeat to keep connections alive
   * @returns {void}
   */
  startHeartbeat() {
    const interval = setInterval(() => {
      if (!this.wss) {
        clearInterval(interval);
        return;
      }

      this.wss.clients.forEach((ws) => {
        if (!ws.isAlive) {
          console.log(`Terminating dead connection for user ${ws.username}`);
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 30 seconds

    console.log('WebSocket heartbeat started');
  }

  /**
   * Handle join chat room request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  handleJoinChatRoom(ws, payload) {
    try {
      const { roomId } = payload;
      
      if (!roomId) {
        this.sendToConnection(ws, {
          type: 'ERROR',
          data: { message: 'Room ID is required' }
        });
        return;
      }
      
      // Store room subscription
      if (!ws.chatRooms) {
        ws.chatRooms = new Set();
      }
      ws.chatRooms.add(roomId);
      
      this.sendToConnection(ws, {
        type: 'JOINED_CHAT_ROOM',
        data: { roomId }
      });
      
      // Notify other room participants
      this.broadcastToRoom(roomId, {
        type: 'USER_JOINED_ROOM',
        data: {
          roomId,
          userId: ws.userId,
          username: ws.username
        }
      }, [ws.userId]);
    } catch (error) {
      console.error('Error joining chat room:', error);
      this.sendToConnection(ws, {
        type: 'ERROR',
        data: { message: 'Failed to join chat room' }
      });
    }
  }

  /**
   * Handle leave chat room request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  handleLeaveChatRoom(ws, payload) {
    try {
      const { roomId } = payload;
      
      if (!roomId || !ws.chatRooms || !ws.chatRooms.has(roomId)) {
        return;
      }
      
      ws.chatRooms.delete(roomId);
      
      this.sendToConnection(ws, {
        type: 'LEFT_CHAT_ROOM',
        data: { roomId }
      });
      
      // Notify other room participants
      this.broadcastToRoom(roomId, {
        type: 'USER_LEFT_ROOM',
        data: {
          roomId,
          userId: ws.userId,
          username: ws.username
        }
      }, [ws.userId]);
    } catch (error) {
      console.error('Error leaving chat room:', error);
    }
  }

  /**
   * Handle typing start
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  handleTypingStart(ws, payload) {
    try {
      const { roomId } = payload;
      
      if (!roomId || !ws.chatRooms || !ws.chatRooms.has(roomId)) {
        return;
      }
      
      // Broadcast to other room participants
      this.broadcastToRoom(roomId, {
        type: 'USER_TYPING',
        data: {
          roomId,
          userId: ws.userId,
          username: ws.username,
          isTyping: true
        }
      }, [ws.userId]);
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  /**
   * Handle typing stop
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} payload - Message payload
   * @returns {void}
   */
  handleTypingStop(ws, payload) {
    try {
      const { roomId } = payload;
      
      if (!roomId || !ws.chatRooms || !ws.chatRooms.has(roomId)) {
        return;
      }
      
      // Broadcast to other room participants
      this.broadcastToRoom(roomId, {
        type: 'USER_TYPING',
        data: {
          roomId,
          userId: ws.userId,
          username: ws.username,
          isTyping: false
        }
      }, [ws.userId]);
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  /**
   * Broadcast message to room participants
   * @param {String} roomId - Room ID
   * @param {Object} message - Message to send
   * @param {Array} excludeUsers - User IDs to exclude (optional)
   * @returns {Promise<void>}
   */
  async broadcastToRoom(roomId, message, excludeUsers = []) {
    try {
      // Get all connections subscribed to this room
      const roomConnections = [];
      
      for (const [userId, connections] of this.clients) {
        if (excludeUsers.includes(userId)) {
          continue;
        }
        
        for (const ws of connections) {
          if (ws.chatRooms && ws.chatRooms.has(roomId)) {
            roomConnections.push(ws);
          }
        }
      }
      
      // Send message to all room connections
      for (const ws of roomConnections) {
        this.sendToConnection(ws, message);
      }
    } catch (error) {
      console.error('Error broadcasting to room:', error);
    }
  }

  /**
   * Close all connections and shutdown
   * @returns {void}
   */
  shutdown() {
    try {
      if (this.wss) {
        this.wss.clients.forEach((ws) => {
          ws.close(1001, 'Server shutting down');
        });
        
        this.wss.close();
        this.wss = null;
      }
      
      this.clients.clear();
      console.log('WebSocket service shut down');
    } catch (error) {
      console.error('Error shutting down WebSocket service:', error);
    }
  }
}

module.exports = new WebSocketService();