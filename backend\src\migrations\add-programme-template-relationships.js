const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-app';

async function migrateTemplateSchema() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;
    
    console.log('Starting template-programme relationship migration...');
    
    // Step 1: Add new fields to existing templates
    const updateResult = await db.collection('scorecard_templates').updateMany(
      {},
      {
        $set: {
          fundingProgrammes: [],
          templateScope: 'global', // Default existing templates to global
          programmeCustomizations: [],
          programmeUsageStats: [],
          programmeStageMapping: []
        }
      }
    );
    
    console.log(`Updated ${updateResult.modifiedCount} existing templates with new programme fields`);
    
    // Step 2: Create indexes for efficient querying
    console.log('Creating indexes for programme-based queries...');
    
    try {
      await db.collection('scorecard_templates').createIndex({ 
        fundingProgrammes: 1, 
        category: 1, 
        isActive: 1 
      });
      console.log('Created compound index: fundingProgrammes + category + isActive');
    } catch (error) {
      console.log('Index already exists or error creating compound index:', error.message);
    }
    
    try {
      await db.collection('scorecard_templates').createIndex({ 
        templateScope: 1, 
        isActive: 1 
      });
      console.log('Created compound index: templateScope + isActive');
    } catch (error) {
      console.log('Index already exists or error creating templateScope index:', error.message);
    }
    
    try {
      await db.collection('scorecard_templates').createIndex({ 
        'programmeUsageStats.programmeId': 1 
      });
      console.log('Created index: programmeUsageStats.programmeId');
    } catch (error) {
      console.log('Index already exists or error creating programmeUsageStats index:', error.message);
    }
    
    // Step 3: Create funding programmes collection if it doesn't exist
    console.log('Creating funding programmes collection...');
    
    const collections = await db.listCollections({ name: 'funding_programmes' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('funding_programmes', {
        validator: {
          $jsonSchema: {
            bsonType: 'object',
            required: ['name', 'description', 'status'],
            properties: {
              name: { bsonType: 'string' },
              description: { bsonType: 'string' },
              status: { 
                bsonType: 'string',
                enum: ['draft', 'active', 'paused', 'completed', 'cancelled']
              },
              corporateSponsorId: { bsonType: 'string' },
              objectives: { 
                bsonType: 'array',
                items: { bsonType: 'string' }
              },
              fundingCriteria: { 
                bsonType: 'array',
                items: { bsonType: 'string' }
              },
              eligibilityCriteria: { 
                bsonType: 'array',
                items: { bsonType: 'string' }
              }
            }
          }
        }
      });
      console.log('Created funding_programmes collection');
    } else {
      console.log('funding_programmes collection already exists');
    }
    
    // Step 4: Create default funding programmes
    const existingProgrammes = await db.collection('funding_programmes').countDocuments();
    if (existingProgrammes === 0) {
      console.log('Creating default funding programmes...');
      
      const defaultProgrammes = [
        {
          name: 'Skills Development',
          description: 'Programme focused on developing skills and capabilities of beneficiaries',
          status: 'active',
          corporateSponsorId: 'corp_skills_dev',
          objectives: [
            'Enhance technical skills',
            'Improve employability',
            'Support career advancement',
            'Foster continuous learning'
          ],
          fundingCriteria: [
            'Skills gap analysis completed',
            'Training plan developed',
            'Measurable outcomes defined',
            'Sustainability plan in place'
          ],
          eligibilityCriteria: [
            'Individual or organization focused on skills development',
            'Clear training objectives',
            'Demonstrated need for skills enhancement',
            'Commitment to programme completion'
          ],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: 'Tech Innovation',
          description: 'Programme supporting technology innovation and digital transformation initiatives',
          status: 'active',
          corporateSponsorId: 'corp_tech_innovation',
          objectives: [
            'Foster technological innovation',
            'Support digital transformation',
            'Encourage R&D activities',
            'Promote tech entrepreneurship'
          ],
          fundingCriteria: [
            'Innovative technology solution',
            'Market potential demonstrated',
            'Technical feasibility confirmed',
            'Scalability potential'
          ],
          eligibilityCriteria: [
            'Technology-focused business or project',
            'Innovation component clearly defined',
            'Technical team with relevant expertise',
            'Intellectual property considerations addressed'
          ],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: 'Green Energy',
          description: 'Programme supporting renewable energy and environmental sustainability projects',
          status: 'active',
          corporateSponsorId: 'corp_green_energy',
          objectives: [
            'Promote renewable energy adoption',
            'Support environmental sustainability',
            'Reduce carbon footprint',
            'Foster green innovation'
          ],
          fundingCriteria: [
            'Environmental impact assessment',
            'Renewable energy component',
            'Sustainability metrics defined',
            'Long-term environmental benefits'
          ],
          eligibilityCriteria: [
            'Focus on renewable energy or sustainability',
            'Environmental compliance demonstrated',
            'Measurable environmental impact',
            'Alignment with green energy goals'
          ],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      const insertResult = await db.collection('funding_programmes').insertMany(defaultProgrammes);
      console.log(`Created ${insertResult.insertedCount} default funding programmes`);
      
      // Step 5: Assign existing templates to appropriate programmes based on their categories
      console.log('Assigning existing templates to appropriate programmes...');
      
      const programmes = await db.collection('funding_programmes').find({}).toArray();
      const skillsDevId = programmes.find(p => p.name === 'Skills Development')._id;
      const techInnovationId = programmes.find(p => p.name === 'Tech Innovation')._id;
      const greenEnergyId = programmes.find(p => p.name === 'Green Energy')._id;
      
      // Assign templates based on category patterns
      const templateAssignments = [
        // Skills Development - focus on human capital and training
        {
          categories: ['beneficiary-registration', 'pre-screening', 'sme-interview'],
          programmeId: skillsDevId,
          scope: 'programme-specific'
        },
        // Tech Innovation - focus on technical assessment and innovation
        {
          categories: ['desktop-analysis', 'data-validation'],
          programmeId: techInnovationId,
          scope: 'programme-specific'
        },
        // Green Energy - focus on sustainability and environmental impact
        {
          categories: ['site-visit', 'committee-review'],
          programmeId: greenEnergyId,
          scope: 'programme-specific'
        },
        // Shared templates - applicable to multiple programmes
        {
          categories: ['document-collection'],
          programmeIds: [skillsDevId, techInnovationId, greenEnergyId],
          scope: 'shared'
        }
      ];
      
      for (const assignment of templateAssignments) {
        const programmeIds = assignment.programmeIds || [assignment.programmeId];
        
        await db.collection('scorecard_templates').updateMany(
          { category: { $in: assignment.categories } },
          {
            $set: {
              fundingProgrammes: programmeIds,
              templateScope: assignment.scope
            }
          }
        );
        
        console.log(`Assigned templates with categories [${assignment.categories.join(', ')}] to programmes with scope: ${assignment.scope}`);
      }
    } else {
      console.log(`Found ${existingProgrammes} existing funding programmes. Skipping programme creation.`);
    }
    
    // Step 6: Create template-programme junction collection for complex queries
    console.log('Creating template-programme assignments collection...');
    
    const assignmentCollections = await db.listCollections({ name: 'template_programme_assignments' }).toArray();
    if (assignmentCollections.length === 0) {
      await db.createCollection('template_programme_assignments', {
        validator: {
          $jsonSchema: {
            bsonType: 'object',
            required: ['templateId', 'programmeId'],
            properties: {
              templateId: { bsonType: 'objectId' },
              programmeId: { bsonType: 'objectId' },
              assignedAt: { bsonType: 'date' },
              assignedBy: { bsonType: 'objectId' },
              customizations: { bsonType: 'object' },
              isActive: { bsonType: 'bool' }
            }
          }
        }
      });
      
      await db.collection('template_programme_assignments').createIndex({ 
        templateId: 1, 
        programmeId: 1 
      }, { unique: true });
      
      console.log('Created template_programme_assignments collection with unique index');
    } else {
      console.log('template_programme_assignments collection already exists');
    }
    
    console.log('Migration completed successfully');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration if called directly
if (require.main === module) {
  migrateTemplateSchema();
}

module.exports = { migrateTemplateSchema };