# Stage & Workflow Management Technical Implementation Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Data Models](#data-models)
3. [Service Layer](#service-layer)
4. [API Implementation](#api-implementation)
5. [Database Schema](#database-schema)
6. [Integration Points](#integration-points)
7. [Performance Considerations](#performance-considerations)
8. [Security Implementation](#security-implementation)
9. [Monitoring & Logging](#monitoring--logging)
10. [Deployment & Configuration](#deployment--configuration)

---

## Architecture Overview

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│                    API Gateway                              │
├─────────────────────────────────────────────────────────────┤
│  Stage Manager Service  │  Workflow Manager Service        │
├─────────────────────────────────────────────────────────────┤
│  Audit Trail Service    │  Notification Service            │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer (MongoDB)                     │
└─────────────────────────────────────────────────────────────┘
```

### Key Design Patterns
- **Service Layer Pattern**: Business logic encapsulated in service classes
- **Repository Pattern**: Data access abstraction through Mongoose models
- **Observer Pattern**: Event-driven architecture for stage transitions
- **Strategy Pattern**: Different workflow types with pluggable strategies
- **Command Pattern**: Audit trail logging and undo operations

---

## Data Models

### Application Model
```javascript
// backend/src/models/application.js
const applicationSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  
  // Stage Management Fields
  currentMainStage: {
    type: String,
    enum: Object.values(ApplicationMainStage),
    default: ApplicationMainStage.ONBOARDING
  },
  currentSubStage: {
    type: String,
    enum: Object.values(ApplicationSubStage),
    default: ApplicationSubStage.BENEFICIARY_REGISTRATION
  },
  currentStageStatus: {
    type: String,
    enum: Object.values(StageStatus),
    default: StageStatus.NOT_STARTED
  },
  
  // Three-level hierarchy
  stageHierarchy: [mainStageSchema],
  
  // Audit trail
  stageStatusAuditLog: [{
    timestamp: { type: Date, default: Date.now },
    mainStage: String,
    subStage: String,
    fromStatus: String,
    toStatus: String,
    changedBy: String,
    reason: String,
    isOverride: { type: Boolean, default: false }
  }]
});
```

### Workflow Model
```javascript
// backend/src/models/workflow.js
const workflowSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  type: {
    type: String,
    enum: ['Sequential', 'Parallel', 'Conditional', 'Priority'],
    default: 'Sequential'
  },
  
  // Stage configuration
  stages: [{
    mainStage: { type: String, required: true },
    subStages: [{
      name: String,
      required: Boolean,
      order: Number,
      conditions: [{ field: String, operator: String, value: Mixed }],
      parallelGroup: Number
    }]
  }],
  
  // Automation rules
  automationRules: [{
    trigger: {
      type: String,
      enum: ['StageComplete', 'DocumentUploaded', 'TimeElapsed', 'ConditionMet']
    },
    actions: [{
      type: {
        type: String,
        enum: ['AssignReviewer', 'SendNotification', 'UpdateStatus', 'CreateTask']
      },
      details: Mixed
    }]
  }]
});
```

### Stage Status Enums
```javascript
// backend/src/models/stage-status-enums.js
const ApplicationMainStage = {
  ONBOARDING: 'ONBOARDING',
  BUSINESS_CASE_REVIEW: 'BUSINESS_CASE_REVIEW',
  DUE_DILIGENCE: 'DUE_DILIGENCE',
  ASSESSMENT_REPORT: 'ASSESSMENT_REPORT',
  APPLICATION_APPROVAL: 'APPLICATION_APPROVAL'
};

const StageStatus = {
  NOT_STARTED: 'NOT_STARTED',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  SKIPPED: 'SKIPPED',
  NOT_APPLICABLE: 'NOT_APPLICABLE'
};

// Validation rules
const ValidStatusTransitions = {
  [StageStatus.NOT_STARTED]: [StageStatus.ACTIVE, StageStatus.SKIPPED, StageStatus.NOT_APPLICABLE],
  [StageStatus.ACTIVE]: [StageStatus.COMPLETED, StageStatus.SKIPPED, StageStatus.NOT_APPLICABLE],
  [StageStatus.COMPLETED]: [StageStatus.ACTIVE],
  [StageStatus.SKIPPED]: [StageStatus.ACTIVE, StageStatus.NOT_STARTED],
  [StageStatus.NOT_APPLICABLE]: [StageStatus.NOT_STARTED, StageStatus.ACTIVE]
};
```

---

## Service Layer

### Stage Status Service
```javascript
// backend/src/services/stage-status.service.js
class StageStatusService {
  async updateStageStatus(applicationId, mainStage, subStage, status, userId, notes) {
    // Validation
    this._validateStageTransition(currentStatus, status);
    
    // Update hierarchy
    this._updateSubStageStatus(application.stageHierarchy, mainStage, subStage, status, userId, notes);
    
    // Update current stage if active
    if (status === StageStatus.ACTIVE) {
      application.currentMainStage = mainStage;
      application.currentSubStage = subStage;
      application.currentStageStatus = status;
    }
    
    // Audit logging
    application.stageStatusAuditLog.push({
      timestamp: new Date(),
      mainStage, subStage,
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason: notes
    });
    
    return await application.save();
  }
  
  _validateStageTransition(fromStatus, toStatus) {
    const validTransitions = ValidStatusTransitions[fromStatus] || [];
    if (!validTransitions.includes(toStatus)) {
      throw new Error(`Invalid transition from ${fromStatus} to ${toStatus}`);
    }
  }
}
```

### Workflow Service
```javascript
// backend/src/services/workflow-service.js
class WorkflowService {
  async assignWorkflowToApplication(applicationId, workflowId, userId) {
    // Validation
    const application = await Application.findOne({ id: applicationId });
    const workflow = await Workflow.findById(workflowId);
    
    if (!workflow || workflow.status !== 'Active') {
      throw new Error('Workflow not found or inactive');
    }
    
    // Create assignment
    const assignment = new WorkflowAssignment({
      applicationId,
      workflowId,
      currentStage: {
        mainStage: workflow.stages[0].mainStage,
        subStage: workflow.stages[0].subStages[0].name
      },
      assignedBy: userId
    });
    
    return await assignment.save();
  }
  
  async executeAutomationRules(workflow, stageUpdate, applicationId) {
    const applicableRules = workflow.automationRules.filter(rule => 
      rule.enabled && this._evaluateTrigger(rule, stageUpdate)
    );
    
    for (const rule of applicableRules) {
      for (const action of rule.actions) {
        await this._executeAction(action, applicationId);
      }
    }
  }
}
```

### Audit Trail Service
```javascript
// backend/src/services/audit-trail.service.js
class AuditTrailService {
  static async logStageTransition(transitionData) {
    const auditEntry = new StageTransitionAudit({
      applicationId: transitionData.applicationId,
      transitionType: this.determineTransitionType(transitionData.from, transitionData.to),
      fromMainStage: transitionData.from.mainStage,
      toMainStage: transitionData.to.mainStage,
      durationInPreviousStage: await this.calculateDuration(transitionData.applicationId),
      userId: transitionData.context.userId,
      timestamp: new Date()
    });
    
    return await auditEntry.save();
  }
  
  static async getWorkflowEfficiencyMetrics(filters = {}) {
    const pipeline = [
      { $match: filters },
      {
        $group: {
          _id: { mainStage: '$toMainStage', subStage: '$toSubStage' },
          avgDuration: { $avg: '$durationInPreviousStage' },
          count: { $sum: 1 }
        }
      },
      { $sort: { avgDuration: -1 } }
    ];
    
    return await StageTransitionAudit.aggregate(pipeline);
  }
}
```

---

## API Implementation

### Stage Management Routes
```javascript
// backend/src/routes/applications.js

// Update stage with hierarchy support
router.put('/:id/stage-hierarchy', async (req, res) => {
  try {
    const { mainStage, subStage, status, notes } = req.body;
    
    // Validation
    if (!mainStage || !subStage || !status) {
      return res.status(400).json({
        message: 'Main stage, sub-stage, and status are required'
      });
    }
    
    // Get current state for audit
    const currentApplication = await Application.findOne({ id: req.params.id });
    
    // Update using service
    const updatedApplication = await StageStatusService.updateStageStatus(
      req.params.id, mainStage, subStage, status, req.user.id, notes
    );
    
    // Log audit trail
    await AuditTrailService.logStageTransition({
      applicationId: req.params.id,
      from: {
        mainStage: currentApplication.currentMainStage,
        subStage: currentApplication.currentSubStage,
        stageStatus: currentApplication.currentStageStatus
      },
      to: {
        mainStage: updatedApplication.currentMainStage,
        subStage: updatedApplication.currentSubStage,
        stageStatus: updatedApplication.currentStageStatus
      },
      context: {
        userId: req.user.id,
        userName: req.user.name,
        reason: notes
      }
    });
    
    res.json({ success: true, application: updatedApplication });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});
```

### Workflow Management Routes
```javascript
// backend/src/routes/workflows.js

// Assign workflow with validation
router.post('/assign', authenticateToken, async (req, res) => {
  try {
    const { applicationId, workflowId } = req.body;
    
    const assignment = await workflowService.assignWorkflowToApplication(
      applicationId, workflowId, req.user.id
    );
    
    res.status(201).json(assignment);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Update application stage with workflow validation
router.put('/application/:applicationId/stage', authenticateToken, async (req, res) => {
  try {
    const assignment = await workflowService.updateApplicationStage(
      req.params.applicationId, req.body, req.user.id
    );
    
    res.json(assignment);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

---

## Database Schema

### MongoDB Collections

#### Applications Collection
```javascript
{
  _id: ObjectId,
  id: "APP-2025-001",
  currentMainStage: "DUE_DILIGENCE",
  currentSubStage: "SME_INTERVIEW",
  currentStageStatus: "ACTIVE",
  stageHierarchy: [
    {
      mainStage: "ONBOARDING",
      status: "COMPLETED",
      subStages: [
        {
          subStage: "BENEFICIARY_REGISTRATION",
          status: {
            status: "COMPLETED",
            startedAt: ISODate,
            completedAt: ISODate,
            assignedTo: "<EMAIL>",
            notes: "Registration completed successfully"
          },
          history: [...]
        }
      ]
    }
  ],
  stageStatusAuditLog: [
    {
      timestamp: ISODate,
      mainStage: "ONBOARDING",
      subStage: "BENEFICIARY_REGISTRATION",
      fromStatus: "NOT_STARTED",
      toStatus: "ACTIVE",
      changedBy: "<EMAIL>",
      reason: "Starting registration process",
      isOverride: false
    }
  ]
}
```

#### Workflows Collection
```javascript
{
  _id: ObjectId,
  name: "Standard SME Processing Workflow",
  type: "Sequential",
  status: "Active",
  stages: [
    {
      mainStage: "ONBOARDING",
      subStages: [
        {
          name: "BENEFICIARY_REGISTRATION",
          required: true,
          order: 1,
          conditions: [],
          parallelGroup: null
        }
      ]
    }
  ],
  automationRules: [
    {
      name: "Auto-assign after registration",
      trigger: "StageComplete",
      triggerDetails: { stage: "ONBOARDING-BENEFICIARY_REGISTRATION" },
      actions: [
        {
          type: "AssignReviewer",
          details: { role: "business_analyst" }
        }
      ],
      enabled: true
    }
  ]
}
```

#### Workflow Assignments Collection
```javascript
{
  _id: ObjectId,
  applicationId: "APP-2025-001",
  workflowId: ObjectId,
  currentStage: {
    mainStage: "DUE_DILIGENCE",
    subStage: "SME_INTERVIEW"
  },
  stageHistory: [
    {
      mainStage: "ONBOARDING",
      subStage: "BENEFICIARY_REGISTRATION",
      status: "COMPLETED",
      startedAt: ISODate,
      completedAt: ISODate,
      completedBy: ObjectId,
      notes: "Registration completed"
    }
  ],
  overrides: [],
  assignedBy: ObjectId,
  createdAt: ISODate,
  updatedAt: ISODate
}
```

### Database Indexes
```javascript
// Applications
db.applications.createIndex({ "id": 1 }, { unique: true });
db.applications.createIndex({ "currentMainStage": 1, "currentSubStage": 1 });
db.applications.createIndex({ "status": 1 });
db.applications.createIndex({ "programmeId": 1 });
db.applications.createIndex({ "corporateSponsorId": 1 });

// Workflows
db.workflows.createIndex({ "name": 1 }, { unique: true });
db.workflows.createIndex({ "status": 1 });
db.workflows.createIndex({ "type": 1 });

// Workflow Assignments
db.workflowassignments.createIndex({ "applicationId": 1 }, { unique: true });
db.workflowassignments.createIndex({ "workflowId": 1 });
db.workflowassignments.createIndex({ "currentStage.mainStage": 1, "currentStage.subStage": 1 });

// Stage Transition Audit
db.stagetransitionaudits.createIndex({ "applicationId": 1, "timestamp": -1 });
db.stagetransitionaudits.createIndex({ "toMainStage": 1, "toSubStage": 1 });
db.stagetransitionaudits.createIndex({ "userId": 1 });
```

---

## Integration Points

### Event-Driven Architecture
```javascript
// Event emitter for stage transitions
const EventEmitter = require('events');
const stageEventEmitter = new EventEmitter();

// Stage transition event
stageEventEmitter.on('stageTransition', async (eventData) => {
  // Log audit trail
  await AuditTrailService.logStageTransition(eventData);
  
  // Execute workflow automation
  const workflow = await WorkflowService.getApplicationWorkflow(eventData.applicationId);
  if (workflow) {
    await WorkflowService.executeAutomationRules(workflow, eventData);
  }
  
  // Send notifications
  await NotificationService.handleStageTransition(eventData);
});

// Emit event on stage update
async function updateStage(applicationId, stageData) {
  const result = await StageStatusService.updateStageStatus(applicationId, stageData);
  
  stageEventEmitter.emit('stageTransition', {
    applicationId,
    from: stageData.from,
    to: stageData.to,
    context: stageData.context
  });
  
  return result;
}
```

### Notification Integration
```javascript
// Notification service integration
class NotificationService {
  static async handleStageTransition(eventData) {
    const notificationRules = await this.getNotificationRules(eventData);
    
    for (const rule of notificationRules) {
      await this.sendNotification({
        type: rule.type,
        recipients: rule.recipients,
        template: rule.template,
        data: eventData
      });
    }
  }
  
  static async sendNotification(notificationData) {
    // Email notification
    if (notificationData.type === 'email') {
      await EmailService.send(notificationData);
    }
    
    // In-app notification
    if (notificationData.type === 'in-app') {
      await WebSocketService.broadcast(notificationData);
    }
  }
}
```

---

## Performance Considerations

### Query Optimization
```javascript
// Efficient stage status queries
class ApplicationRepository {
  // Get applications with stage filtering
  async findByStageStatus(filters, pagination) {
    const query = {};
    
    // Use indexes for stage filtering
    if (filters.mainStage) {
      query.currentMainStage = filters.mainStage;
    }
    
    if (filters.subStage) {
      query.currentSubStage = filters.subStage;
    }
    
    // Efficient pagination
    return Application.find(query)
      .select('id currentMainStage currentSubStage currentStageStatus status')
      .limit(pagination.limit)
      .skip(pagination.offset)
      .lean(); // Use lean() for read-only operations
  }
  
  // Aggregation for stage statistics
  async getStageStatistics(filters = {}) {
    return Application.aggregate([
      { $match: filters },
      {
        $group: {
          _id: {
            mainStage: '$currentMainStage',
            subStage: '$currentSubStage',
            status: '$currentStageStatus'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.mainStage': 1, '_id.subStage': 1 } }
    ]);
  }
}
```

### Caching Strategy
```javascript
const Redis = require('redis');
const redis = Redis.createClient();

class CacheService {
  // Cache workflow configurations
  async getWorkflow(workflowId) {
    const cacheKey = `workflow:${workflowId}`;
    
    let workflow = await redis.get(cacheKey);
    if (workflow) {
      return JSON.parse(workflow);
    }
    
    workflow = await Workflow.findById(workflowId);
    if (workflow) {
      await redis.setex(cacheKey, 3600, JSON.stringify(workflow)); // 1 hour cache
    }
    
    return workflow;
  }
  
  // Cache stage statistics
  async getStageStatistics(filters) {
    const cacheKey = `stage_stats:${JSON.stringify(filters)}`;
    
    let stats = await redis.get(cacheKey);
    if (stats) {
      return JSON.parse(stats);
    }
    
    stats = await ApplicationRepository.getStageStatistics(filters);
    await redis.setex(cacheKey, 300, JSON.stringify(stats)); // 5 minute cache
    
    return stats;
  }
}
```

### Batch Operations
```javascript
// Bulk stage updates
class BulkOperationService {
  async bulkUpdateStages(updates) {
    const session = await mongoose.startSession();
    
    try {
      await session.withTransaction(async () => {
        for (const update of updates) {
          await StageStatusService.updateStageStatus(
            update.applicationId,
            update.mainStage,
            update.subStage,
            update.status,
            update.userId,
            update.notes
          );
        }
      });
    } finally {
      await session.endSession();
    }
  }
}
```

---

## Security Implementation

### Authentication & Authorization
```javascript
// Middleware for stage management permissions
const checkStagePermissions = (requiredPermission) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      const applicationId = req.params.id;
      
      // Check if user has general permission
      if (!user.permissions.includes(requiredPermission)) {
        return res.status(403).json({ message: 'Insufficient permissions' });
      }
      
      // Check application-specific permissions
      if (requiredPermission === 'UPDATE_STAGE') {
        const application = await Application.findOne({ id: applicationId });
        
        // Reviewers can only update assigned applications
        if (user.role === 'reviewer' && application.assignedTo !== user.email) {
          return res.status(403).json({ message: 'Not assigned to this application' });
        }
      }
      
      next();
    } catch (error) {
      res.status(500).json({ message: 'Permission check failed' });
    }
  };
};

// Usage in routes
router.put('/:id/stage-hierarchy', 
  authenticateToken, 
  checkStagePermissions('UPDATE_STAGE'), 
  async (req, res) => {
    // Stage update logic
  }
);
```

### Input Validation
```javascript
const { body, param, validationResult } = require('express-validator');

// Validation rules for stage updates
const stageUpdateValidation = [
  param('id').matches(/^APP-\d{4}-\d{3}$/).withMessage('Invalid application ID format'),
  body('mainStage').isIn(Object.values(ApplicationMainStage)).withMessage('Invalid main stage'),
  body('subStage').isIn(Object.values(ApplicationSubStage)).withMessage('Invalid sub-stage'),
  body('status').isIn(Object.values(StageStatus)).withMessage('Invalid status'),
  body('notes').optional().isLength({ max: 1000 }).withMessage('Notes too long')
];

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};
```

### Audit Logging
```javascript
// Security audit logging
class SecurityAuditService {
  static async logSecurityEvent(eventType, userId, details) {
    const auditEntry = {
      timestamp: new Date(),
      eventType,
      userId,
      userAgent: details.userAgent,
      ipAddress: details.ipAddress,
      resource: details.resource,
      action: details.action,
      success: details.success,
      errorMessage: details.errorMessage
    };
    
    // Log to security audit collection
    await SecurityAudit.create(auditEntry);
    
    // Alert on suspicious activity
    if (this.isSuspiciousActivity(auditEntry)) {
      await AlertService.sendSecurityAlert(auditEntry);
    }
  }
  
  static isSuspiciousActivity(auditEntry) {
    // Multiple failed attempts
    // Unusual access patterns
    // Privilege escalation attempts
    return false; // Implement logic
  }
}
```

---

## Monitoring & Logging

### Application Logging
```javascript
const winston = require('winston');

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Stage transition logging
class StageLogger {
  static logTransition(applicationId, from, to, userId) {
    logger.info('Stage transition', {
      applicationId,
      from: `${from.mainStage}/${from.subStage}`,
      to: `${to.mainStage}/${to.subStage}`,
      userId,
      timestamp: new Date().toISOString()
    });
  }
  
  static logError(operation, error, context) {
    logger.error('Stage operation failed', {
      operation,
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    });
  }
}
```

### Performance Monitoring
```javascript
// Performance metrics collection
class MetricsCollector {
  static async collectStageMetrics() {
    const metrics = {
      timestamp: new Date(),
      activeApplications: await Application.countDocuments({ status: 'in-review' }),
      stageDistribution: await this.getStageDistribution(),
      avgProcessingTime: await this.getAverageProcessingTime(),
      bottlenecks: await this.identifyBottlenecks()
    };
    
    // Send to monitoring system
    await this.sendToMonitoring(metrics);
    
    return metrics;
  }
  
  static async getStageDistribution() {
    return Application.aggregate([
      {
        $group: {
          _id: { mainStage: '$currentMainStage', subStage: '$currentSubStage' },
          count: { $sum: 1 }
        }
      }
    ]);
  }
  
  static async identifyBottlenecks() {
    // Identify stages with longest average processing times
    return StageTransitionAudit.aggregate([
      {
        $group: {
          _id: { mainStage: '$toMainStage', subStage: '$toSubStage' },
          avgDuration: { $avg: '$durationInPreviousStage' },
          count: { $sum: 1 }
        }
      },
      { $sort: { avgDuration: -1 } },
      { $limit: 5 }
    ]);
  }
}
```

### Health Checks
```javascript
// Health check endpoints
router.get('/health/stage-manager', async (req, res) => {
  try {
    // Check database connectivity
    await Application.findOne().limit(1);
    
    // Check service availability
    const stageService = new StageStatusService();
    
    // Check recent activity
    const recentTransitions = await StageTransitionAudit.countDocuments({
      timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    res.json({
      status: 'healthy',
      timestamp: new Date(),
      checks: {
        database: 'connected',
        service: 'available',
        recentActivity: recentTransitions
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date()
    });
  }
});
```

---

## Deployment & Configuration

### Environment Configuration
```javascript
// config/stage-workflow.js
module.exports = {
  development: {
    stageManager: {
      enableAuditTrail: true,
      enableAutomation: false,
      cacheTimeout: 300,
      maxBulkOperations: 100
    },
    workflowManager: {
      enableParallelProcessing: true,
      automationRuleTimeout: 30000,
      maxWorkflowDepth: 10
    }
  },
  
  production: {
    stageManager: {
      enableAuditTrail: true,
      enableAutomation: true,
      cacheTimeout: 3600,
      maxBulkOperations: 1000
    },
    workflowManager: {
      enableParallelProcessing: true,
      automationRuleTimeout: 60000,
      maxWorkflowDepth: 20
    }
  }
};
```

### Docker Configuration
```dockerfile
# Dockerfile for stage/workflow services
FROM node:16-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY config/ ./config/

# Set environment
ENV NODE_ENV=production
ENV PORT=3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health/stage-manager || exit 1

EXPOSE 3000

CMD ["node", "src/server.js"]
```

### Database Migration Scripts
```javascript
// migrations/001-initialize-stage-hierarchy.js
const mongoose = require('mongoose');
const Application = require('../src/models/application');

async function up() {
  console.log('Initializing stage hierarchy for existing applications...');
  
  const applications = await Application.find({ stageHierarchy: { $exists: false } });
  
  for (const app of applications) {
    app.stageHierarchy = initializeStageHierarchy();
    await app.save();
  }
  
  console.log(`Updated ${applications.length} applications`);
}

async function down() {
  console.log('Removing stage hierarchy...');
  
  await Application.updateMany(
    {},
    { $unset: { stageHierarchy: 1 } }
  );
}

function initializeStageHierarchy() {
  // Implementation from StageStatusService
  return [/* stage hierarchy structure */];
}

module.exports = { up, down };
```

### Monitoring Setup
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
