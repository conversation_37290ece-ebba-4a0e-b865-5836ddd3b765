const mongoose = require('mongoose');

/**
 * Stage Transition Audit Trail Model
 * Tracks all stage, substage, and status transitions for process analytics
 */
const stageTransitionAuditSchema = new mongoose.Schema({
  // Application reference
  applicationId: {
    type: String,
    required: true,
    index: true
  },
  
  // MongoDB ObjectId reference for population
  applicationObjectId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Application',
    required: true,
    index: true
  },
  
  // Transition details
  transitionType: {
    type: String,
    enum: ['STAGE_CHANGE', 'SUBSTAGE_CHANGE', 'STATUS_CHANGE', 'COMBINED_CHANGE'],
    required: true
  },
  
  // Previous state
  fromMainStage: {
    type: String,
    required: false // May be null for initial entries
  },
  fromSubStage: {
    type: String,
    required: false
  },
  fromStageStatus: {
    type: String,
    required: false
  },
  fromApplicationStatus: {
    type: String,
    required: false
  },
  
  // New state
  toMainStage: {
    type: String,
    required: true
  },
  toSubStage: {
    type: String,
    required: true
  },
  toStageStatus: {
    type: String,
    required: true
  },
  toApplicationStatus: {
    type: String,
    required: true
  },
  
  // Timing information
  timestamp: {
    type: Date,
    default: Date.now,
    required: true,
    index: true
  },
  
  // Duration in previous stage (in milliseconds)
  durationInPreviousStage: {
    type: Number,
    required: false // Will be null for initial entries
  },
  
  // Duration in previous stage (human readable)
  durationInPreviousStageFormatted: {
    type: String,
    required: false
  },
  
  // User and context information
  userId: {
    type: String,
    required: false
  },
  userName: {
    type: String,
    required: false
  },
  
  // Transition metadata
  reason: {
    type: String,
    required: false
  },
  notes: {
    type: String,
    required: false
  },
  
  // System information
  isSystemGenerated: {
    type: Boolean,
    default: false
  },
  isInitialEntry: {
    type: Boolean,
    default: false
  },
  
  // Additional context
  programmeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FundingProgramme',
    required: false
  },
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor',
    required: false
  },
  
  // Metadata for analytics
  metadata: {
    businessDay: {
      type: Boolean,
      default: true
    },
    dayOfWeek: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    hour: {
      type: Number,
      min: 0,
      max: 23
    },
    month: {
      type: Number,
      min: 1,
      max: 12
    },
    year: {
      type: Number
    }
  }
}, {
  timestamps: true,
  collection: 'stage_transition_audits'
});

// Indexes for performance
stageTransitionAuditSchema.index({ applicationId: 1, timestamp: -1 });
stageTransitionAuditSchema.index({ toMainStage: 1, timestamp: -1 });
stageTransitionAuditSchema.index({ toSubStage: 1, timestamp: -1 });
stageTransitionAuditSchema.index({ timestamp: -1 });
stageTransitionAuditSchema.index({ programmeId: 1, timestamp: -1 });
stageTransitionAuditSchema.index({ corporateSponsorId: 1, timestamp: -1 });

// Pre-save middleware to populate metadata
stageTransitionAuditSchema.pre('save', function(next) {
  if (this.isNew) {
    const date = this.timestamp || new Date();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    this.metadata = {
      businessDay: date.getDay() >= 1 && date.getDay() <= 5, // Monday to Friday
      dayOfWeek: dayNames[date.getDay()],
      hour: date.getHours(),
      month: date.getMonth() + 1,
      year: date.getFullYear()
    };
  }
  next();
});

// Static methods for analytics queries
stageTransitionAuditSchema.statics.getProcessingTimesByStage = async function(filters = {}) {
  console.log('🔍 getProcessingTimesByStage called with filters:', JSON.stringify(filters, null, 2));
  
  // Build match conditions with proper ObjectId handling
  const matchConditions = {
    durationInPreviousStage: { $exists: true, $ne: null }
  };
  
  // Handle filters with ObjectId conversion for corporate sponsor and programme
  Object.keys(filters).forEach(key => {
    const value = filters[key];
    if (value !== undefined && value !== null) {
      if (key === 'corporateSponsorId' || key === 'programmeId') {
        // Ensure ObjectId comparison works correctly
        if (mongoose.Types.ObjectId.isValid(value)) {
          matchConditions[key] = new mongoose.Types.ObjectId(value);
          console.log(`✓ Converted ${key} to ObjectId:`, matchConditions[key]);
        } else {
          // Fallback to string comparison if not a valid ObjectId
          matchConditions[key] = value;
          console.log(`⚠️ Using string comparison for ${key}:`, value);
        }
      } else {
        matchConditions[key] = value;
        console.log(`✓ Added filter ${key}:`, value);
      }
    }
  });
  
  console.log('🔍 Final match conditions:', JSON.stringify(matchConditions, null, 2));
  
  const pipeline = [
    {
      $match: matchConditions
    },
    {
      $group: {
        _id: {
          mainStage: '$fromMainStage',
          subStage: '$fromSubStage'
        },
        avgDuration: { $avg: '$durationInPreviousStage' },
        minDuration: { $min: '$durationInPreviousStage' },
        maxDuration: { $max: '$durationInPreviousStage' },
        count: { $sum: 1 },
        totalDuration: { $sum: '$durationInPreviousStage' }
      }
    },
    {
      $sort: { avgDuration: -1 }
    }
  ];
  
  console.log('🔍 Aggregation pipeline:', JSON.stringify(pipeline, null, 2));
  
  const result = await this.aggregate(pipeline);
  console.log('📊 Aggregation result count:', result.length);
  console.log('📋 Sample results:', JSON.stringify(result.slice(0, 2), null, 2));
  
  return result;
};

stageTransitionAuditSchema.statics.getBottleneckAnalysis = async function(filters = {}) {
  console.log('🔍 getBottleneckAnalysis called with filters:', JSON.stringify(filters, null, 2));
  
  // Build match conditions with proper ObjectId handling
  const matchConditions = {
    durationInPreviousStage: { $exists: true, $ne: null }
  };
  
  // Handle filters with ObjectId conversion for corporate sponsor and programme
  Object.keys(filters).forEach(key => {
    const value = filters[key];
    if (value !== undefined && value !== null) {
      if (key === 'corporateSponsorId' || key === 'programmeId') {
        // Ensure ObjectId comparison works correctly
        if (mongoose.Types.ObjectId.isValid(value)) {
          matchConditions[key] = new mongoose.Types.ObjectId(value);
          console.log(`✓ Converted ${key} to ObjectId:`, matchConditions[key]);
        } else {
          // Fallback to string comparison if not a valid ObjectId
          matchConditions[key] = value;
          console.log(`⚠️ Using string comparison for ${key}:`, value);
        }
      } else {
        matchConditions[key] = value;
        console.log(`✓ Added filter ${key}:`, value);
      }
    }
  });
  
  console.log('🔍 Final match conditions for bottlenecks:', JSON.stringify(matchConditions, null, 2));
  
  const pipeline = [
    {
      $match: matchConditions
    },
    {
      $group: {
        _id: {
          mainStage: '$fromMainStage',
          subStage: '$fromSubStage'
        },
        avgDuration: { $avg: '$durationInPreviousStage' },
        count: { $sum: 1 },
        applications: { $addToSet: '$applicationId' }
      }
    },
    {
      $addFields: {
        avgDurationDays: { $divide: ['$avgDuration', 86400000] } // Convert to days
      }
    },
    {
      $sort: { avgDurationDays: -1 }
    },
    {
      $limit: 10
    }
  ];
  
  const result = await this.aggregate(pipeline);
  console.log('📊 Bottleneck analysis result count:', result.length);
  
  return result;
};

stageTransitionAuditSchema.statics.getStageCompletionRates = async function(filters = {}) {
  console.log('🔍 getStageCompletionRates called with filters:', JSON.stringify(filters, null, 2));
  
  // Build match conditions with proper ObjectId handling
  const matchConditions = {};
  
  // Handle filters with ObjectId conversion for corporate sponsor and programme
  Object.keys(filters).forEach(key => {
    const value = filters[key];
    if (value !== undefined && value !== null) {
      if (key === 'corporateSponsorId' || key === 'programmeId') {
        // Ensure ObjectId comparison works correctly
        if (mongoose.Types.ObjectId.isValid(value)) {
          matchConditions[key] = new mongoose.Types.ObjectId(value);
          console.log(`✓ Converted ${key} to ObjectId:`, matchConditions[key]);
        } else {
          // Fallback to string comparison if not a valid ObjectId
          matchConditions[key] = value;
          console.log(`⚠️ Using string comparison for ${key}:`, value);
        }
      } else {
        matchConditions[key] = value;
        console.log(`✓ Added filter ${key}:`, value);
      }
    }
  });
  
  console.log('🔍 Final match conditions for completion rates:', JSON.stringify(matchConditions, null, 2));
  
  const pipeline = [
    {
      $match: matchConditions
    },
    {
      $group: {
        _id: {
          mainStage: '$toMainStage',
          subStage: '$toSubStage'
        },
        totalTransitions: { $sum: 1 },
        completedTransitions: {
          $sum: {
            $cond: [
              { $eq: ['$toStageStatus', 'COMPLETED'] },
              1,
              0
            ]
          }
        }
      }
    },
    {
      $addFields: {
        completionRate: {
          $multiply: [
            { $divide: ['$completedTransitions', '$totalTransitions'] },
            100
          ]
        }
      }
    },
    {
      $sort: { completionRate: -1 }
    }
  ];
  
  const result = await this.aggregate(pipeline);
  console.log('📊 Completion rates result count:', result.length);
  
  return result;
};

stageTransitionAuditSchema.statics.getApplicationJourney = async function(applicationId) {
  return this.find({ applicationId })
    .sort({ timestamp: 1 })
    .populate('programmeId', 'name')
    .populate('corporateSponsorId', 'name')
    .lean();
};

const StageTransitionAudit = mongoose.model('StageTransitionAudit', stageTransitionAuditSchema);

module.exports = StageTransitionAudit;