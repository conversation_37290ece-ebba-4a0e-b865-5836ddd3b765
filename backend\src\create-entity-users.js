const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/user');
const CorporateSponsor = require('./models/corporate-sponsor');
const FundingProgramme = require('./models/funding-programme');
const dbConfig = require('./config/database-config');

// Entity-specific user creation script
async function createEntityUsers() {
  try {
    console.log('Starting entity-specific user creation...');
    
    // Connect to database
    const dbConnection = await dbConfig.connectToDatabase();
    
    if (!dbConnection.success) {
      console.error('Failed to connect to database:', dbConnection.message);
      process.exit(1);
    }
    
    console.log('Connected to MongoDB database');
    
    // Fetch all corporate sponsors
    console.log('Fetching corporate sponsors...');
    const corporateSponsors = await CorporateSponsor.find({ status: 'active' });
    console.log(`Found ${corporateSponsors.length} active corporate sponsors`);
    
    // Fetch all funding programmes
    console.log('Fetching funding programmes...');
    const fundingProgrammes = await FundingProgramme.find({ status: 'active' });
    console.log(`Found ${fundingProgrammes.length} active funding programmes`);
    
    let createdUsersCount = 0;
    let existingUsersCount = 0;
    
    // Create corporate sponsor users
    console.log('\nCreating corporate sponsor users...');
    for (const sponsor of corporateSponsors) {
      const email = `${sponsor.name.toLowerCase().replace(/[^a-z0-9]/g, '')}.<EMAIL>`;
      const username = email; // Use email as username
      const sponsorName = sponsor.name.replace(/[^a-zA-Z0-9]/g, '');
      const password = `${sponsorName}User123!`;
      
      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { username: username },
          { email: email }
        ]
      });
      
      if (!existingUser) {
        console.log(`Creating corporate sponsor user: ${username}`);
        
        const userData = {
          username: username,
          email: email,
          password: password,
          firstName: sponsor.name,
          lastName: 'User',
          role: 'CORPORATE_REVIEWER',
          roles: ['corporate-sponsor-user', 'user'],
          permissions: [
            'read:entity_applications',
            'update:entity_applications',
            'read:entity_reports',
            'read:own_profile',
            'update:own_profile'
          ],
          organizationType: 'CorporateSponsor',
          organizationId: sponsor._id.toString(),
          corporateSponsorId: sponsor._id,
          isActive: true,
          status: 'active'
        };
        
        const user = new User(userData);
        await user.save();
        createdUsersCount++;
        
        // Log credentials for new users
        console.log(`  Username: ${username}`);
        console.log(`  Email: ${email}`);
        console.log(`  Password: ${password}`);
        console.log(`  Corporate Sponsor: ${sponsor.name}`);
        console.log(`  Role: corporate-sponsor-user`);
        console.log('  ---');
      } else {
        console.log(`Corporate sponsor user ${username} already exists`);
        existingUsersCount++;
      }
    }
    
    // Create funding programme users
    console.log('\nCreating funding programme users...');
    for (const programme of fundingProgrammes) {
      const email = `${programme.name.toLowerCase().replace(/[^a-z0-9]/g, '')}.<EMAIL>`;
      const username = email; // Use email as username
      const programmeName = programme.name.replace(/[^a-zA-Z0-9]/g, '');
      const password = `${programmeName}User123!`;
      
      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { username: username },
          { email: email }
        ]
      });
      
      if (!existingUser) {
        console.log(`Creating programme user: ${username}`);
        
        const userData = {
          username: username,
          email: email,
          password: password,
          firstName: programme.name,
          lastName: 'User',
          role: 'PROGRAMME_REVIEWER',
          roles: ['programme-user', 'user'],
          permissions: [
            'read:entity_applications',
            'update:entity_applications',
            'read:entity_reports',
            'read:own_profile',
            'update:own_profile'
          ],
          organizationType: '20/20Insight',
          fundingProgrammeId: programme._id,
          programmeAssignments: [{
            programmeId: programme._id.toString(),
            role: 'programme-user'
          }],
          isActive: true,
          status: 'active'
        };
        
        const user = new User(userData);
        await user.save();
        createdUsersCount++;
        
        // Log credentials for new users
        console.log(`  Username: ${username}`);
        console.log(`  Email: ${email}`);
        console.log(`  Password: ${password}`);
        console.log(`  Programme: ${programme.name}`);
        console.log(`  Role: programme-user`);
        console.log('  ---');
      } else {
        console.log(`Programme user ${username} already exists`);
        existingUsersCount++;
      }
    }
    
    console.log(`\nEntity user creation summary:`);
    console.log(`  Created: ${createdUsersCount} new entity users`);
    console.log(`  Existing: ${existingUsersCount} users already existed`);
    
    if (createdUsersCount > 0) {
      console.log('\n*** PLEASE CHANGE DEFAULT PASSWORDS AFTER FIRST LOGIN ***');
      console.log('\nAll entity users have been created with the following patterns:');
      console.log('  Corporate Sponsor Users:');
      console.log('    Username: [sponsor-name].user');
      console.log('    Password: [SponsorName]User123!');
      console.log('    Email: [sponsor-name].<EMAIL>');
      console.log('  Programme Users:');
      console.log('    Username: [programme-name].user');
      console.log('    Password: [ProgrammeName]User123!');
      console.log('    Email: [programme-name].<EMAIL>');
    }
    
    console.log('Entity user creation completed successfully');
    return true;
  } catch (error) {
    console.error('Error creating entity users:', error);
    return false;
  }
}

// Export the function
module.exports = { createEntityUsers };

// If this script is run directly, execute the creation
if (require.main === module) {
  async function runCreation() {
    try {
      const success = await createEntityUsers();
      
      if (success) {
        console.log('Entity user creation completed successfully');
        process.exit(0);
      } else {
        console.error('Entity user creation failed');
        process.exit(1);
      }
    } catch (error) {
      console.error('Error running entity user creation script:', error);
      process.exit(1);
    }
  }
  
  runCreation();
}
