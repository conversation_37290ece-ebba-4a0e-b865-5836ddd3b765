const mongoose = require('mongoose');

const userNotificationSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  notificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Notification',
    required: true
  },
  // Delivery status for this specific user
  deliveryStatus: {
    inApp: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED', 'SKIPPED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String
    },
    email: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED', 'SKIPPED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String,
      emailAddress: String
    },
    sms: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED', 'SKIPPED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String,
      phoneNumber: String
    },
    push: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED', 'SKIPPED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String,
      deviceToken: String
    }
  },
  // User interaction tracking
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: Date,
  isClicked: {
    type: Boolean,
    default: false
  },
  clickedAt: Date,
  clickedAction: String, // Which action button was clicked
  isDismissed: {
    type: Boolean,
    default: false
  },
  dismissedAt: Date,
  // User preferences override
  userPreferences: {
    inApp: {
      type: Boolean,
      default: null // null means use global preference
    },
    email: {
      type: Boolean,
      default: null
    },
    sms: {
      type: Boolean,
      default: null
    },
    push: {
      type: Boolean,
      default: null
    }
  },
  // Retry tracking
  retryCount: {
    type: Number,
    default: 0
  },
  lastRetryAt: Date,
  maxRetries: {
    type: Number,
    default: 3
  },
  // Scheduling for this user (if different from main notification)
  scheduledFor: Date,
  // Status
  status: {
    type: String,
    required: true,
    enum: ['PENDING', 'DELIVERED', 'PARTIALLY_DELIVERED', 'FAILED', 'CANCELLED', 'EXPIRED'],
    default: 'PENDING'
  },
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Additional user-specific data
  personalizedData: mongoose.Schema.Types.Mixed,
  // Expiry
  expiresAt: Date
});

// Compound indexes for performance
userNotificationSchema.index({ userId: 1, notificationId: 1 }, { unique: true });
userNotificationSchema.index({ userId: 1, status: 1, createdAt: -1 });
userNotificationSchema.index({ userId: 1, isRead: 1, createdAt: -1 });
userNotificationSchema.index({ notificationId: 1, status: 1 });
userNotificationSchema.index({ status: 1, scheduledFor: 1 });
userNotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Pre-save middleware
userNotificationSchema.pre('save', async function(next) {
  try {
    // Generate ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(8, '0');
      this.id = `UN-2025-${nextNumber}`;
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Virtual for overall delivery status
userNotificationSchema.virtual('overallDeliveryStatus').get(function() {
  const statuses = [];
  
  if (this.deliveryStatus.inApp.status !== 'SKIPPED') {
    statuses.push(this.deliveryStatus.inApp.status);
  }
  if (this.deliveryStatus.email.status !== 'SKIPPED') {
    statuses.push(this.deliveryStatus.email.status);
  }
  if (this.deliveryStatus.sms.status !== 'SKIPPED') {
    statuses.push(this.deliveryStatus.sms.status);
  }
  if (this.deliveryStatus.push.status !== 'SKIPPED') {
    statuses.push(this.deliveryStatus.push.status);
  }
  
  if (statuses.length === 0) return 'SKIPPED';
  if (statuses.every(status => status === 'DELIVERED')) return 'DELIVERED';
  if (statuses.every(status => status === 'FAILED')) return 'FAILED';
  if (statuses.some(status => status === 'DELIVERED')) return 'PARTIALLY_DELIVERED';
  if (statuses.every(status => status === 'PENDING')) return 'PENDING';
  
  return 'PARTIALLY_DELIVERED';
});

// Method to mark as read
userNotificationSchema.methods.markAsRead = function() {
  if (!this.isRead) {
    this.isRead = true;
    this.readAt = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to mark as clicked
userNotificationSchema.methods.markAsClicked = function(actionLabel = null) {
  if (!this.isClicked) {
    this.isClicked = true;
    this.clickedAt = new Date();
    if (actionLabel) {
      this.clickedAction = actionLabel;
    }
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to dismiss notification
userNotificationSchema.methods.dismiss = function() {
  if (!this.isDismissed) {
    this.isDismissed = true;
    this.dismissedAt = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to check if delivery should be retried
userNotificationSchema.methods.shouldRetry = function() {
  return this.retryCount < this.maxRetries && 
         this.status === 'FAILED' &&
         (!this.lastRetryAt || (Date.now() - this.lastRetryAt.getTime()) > 300000); // 5 minutes
};

// Method to increment retry count
userNotificationSchema.methods.incrementRetry = function() {
  this.retryCount += 1;
  this.lastRetryAt = new Date();
  return this.save();
};

// Method to update delivery status for a specific method
userNotificationSchema.methods.updateDeliveryStatus = function(method, status, error = null, additionalData = {}) {
  if (!this.deliveryStatus[method]) {
    throw new Error(`Invalid delivery method: ${method}`);
  }
  
  this.deliveryStatus[method].status = status;
  this.deliveryStatus[method].error = error;
  
  if (status === 'DELIVERED') {
    this.deliveryStatus[method].deliveredAt = new Date();
  }
  
  // Add method-specific data
  Object.assign(this.deliveryStatus[method], additionalData);
  
  // Update overall status
  this.status = this.overallDeliveryStatus;
  
  return this.save();
};

// Method to get effective user preferences (considering overrides)
userNotificationSchema.methods.getEffectivePreferences = async function() {
  const User = mongoose.model('User');
  const user = await User.findById(this.userId);
  
  if (!user) {
    throw new Error('User not found');
  }
  
  // Get user's global notification preferences (assuming they exist on user model)
  const globalPrefs = user.notificationPreferences || {
    inApp: true,
    email: false,
    sms: false,
    push: false
  };
  
  return {
    inApp: this.userPreferences.inApp !== null ? this.userPreferences.inApp : globalPrefs.inApp,
    email: this.userPreferences.email !== null ? this.userPreferences.email : globalPrefs.email,
    sms: this.userPreferences.sms !== null ? this.userPreferences.sms : globalPrefs.sms,
    push: this.userPreferences.push !== null ? this.userPreferences.push : globalPrefs.push
  };
};

// Static method to get user's unread notifications
userNotificationSchema.statics.getUnreadForUser = function(userId, limit = 50, skip = 0) {
  return this.find({
    userId: userId,
    isRead: false,
    status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
  })
  .populate('notificationId')
  .sort({ createdAt: -1 })
  .limit(limit)
  .skip(skip);
};

// Static method to get user's notification history
userNotificationSchema.statics.getHistoryForUser = function(userId, filters = {}, limit = 50, skip = 0) {
  const query = { userId: userId };
  
  if (filters.isRead !== undefined) {
    query.isRead = filters.isRead;
  }
  
  if (filters.status) {
    query.status = filters.status;
  }
  
  if (filters.dateFrom || filters.dateTo) {
    query.createdAt = {};
    if (filters.dateFrom) {
      query.createdAt.$gte = new Date(filters.dateFrom);
    }
    if (filters.dateTo) {
      query.createdAt.$lte = new Date(filters.dateTo);
    }
  }
  
  return this.find(query)
    .populate('notificationId')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to mark multiple notifications as read
userNotificationSchema.statics.markMultipleAsRead = function(userId, notificationIds) {
  return this.updateMany(
    {
      userId: userId,
      notificationId: { $in: notificationIds },
      isRead: false
    },
    {
      $set: {
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date()
      }
    }
  );
};

const UserNotification = mongoose.model('UserNotification', userNotificationSchema);

module.exports = UserNotification;