const { QuestionnaireTemplate, QuestionnaireResponse } = require('./questionnaire-template-model');
const Interview = require('./models/interview');

/**
 * Seed questionnaire data for testing
 */
async function seedQuestionnaireData() {
  try {
    console.log('Starting questionnaire data seeding process...');
    
    // Check if we already have templates and responses
    const existingTemplates = await QuestionnaireTemplate.find();
    const existingResponses = await QuestionnaireResponse.find();
    
    console.log(`Found ${existingTemplates.length} existing templates and ${existingResponses.length} existing responses`);
    
    // If we already have data, don't seed again
    if (existingTemplates.length > 0 && existingResponses.length > 0) {
      console.log('Questionnaire data already exists, skipping seeding');
      return;
    }
    
    console.log('Seeding questionnaire data...');
    
    // Check if template already exists
    let savedTemplate = await QuestionnaireTemplate.findOne({ 
      name: 'Funding Interview Template', 
      version: '1.0.0' 
    });
    
    if (!savedTemplate) {
      // Create a test template
      const template = new QuestionnaireTemplate({
        name: 'Funding Interview Template',
        description: 'Standard template for funding interviews',
        version: '1.0.0',
        isActive: true,
        createdBy: 'System',
        applicableIndustries: ['Technology', 'Manufacturing', 'Agriculture', 'All'],
        applicableFundingTypes: ['Loan', 'Grant', 'Equity', 'All'],
        applicableLifecycleStages: ['Startup', 'Growth', 'Mature', 'All'],
        sections: [
          {
            id: 'section1',
            title: 'Company Information',
            description: 'Basic information about the company',
            order: 1,
            questions: [
              {
                id: 'q1',
                text: 'What is the company name?',
                type: 'OPEN_ENDED',
                required: true
              },
              {
                id: 'q2',
                text: 'What industry does the company operate in?',
                type: 'MULTIPLE_CHOICE',
                required: true,
                options: ['Technology', 'Manufacturing', 'Agriculture', 'Finance', 'Other']
              }
            ],
            isRequired: true
          },
          {
            id: 'section2',
            title: 'Funding Requirements',
            description: 'Information about funding needs',
            order: 2,
            questions: [
              {
                id: 'q3',
                text: 'How much funding is required?',
                type: 'OPEN_ENDED',
                required: true
              },
              {
                id: 'q4',
                text: 'What will the funding be used for?',
                type: 'OPEN_ENDED',
                required: true
              }
            ],
            isRequired: true
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      savedTemplate = await template.save();
      console.log('Created new template:', savedTemplate.id);
    } else {
      console.log('Using existing template:', savedTemplate.id);
    }
    console.log('Created template:', savedTemplate.id);
    
    // Create test interviews with proper IDs
    const interviewData = [
      {
        id: 'interview-1',
        applicationId: '101',
        title: 'Initial Interview',
        status: 'SCHEDULED',
        scheduledDate: new Date(2025, 3, 20),
        interviewee: 'John Smith',
        primaryInterviewer: 'Sarah Johnson',
        type: 'ONLINE',
        duration: 60,
        questions: [],
        notes: [],
        participants: [],
        recordings: [],
        sections: [],
        createdAt: new Date(2025, 3, 15),
        updatedAt: new Date(2025, 3, 15)
      },
      {
        id: 'interview-2',
        applicationId: '102',
        title: 'Follow-up Interview',
        status: 'COMPLETED',
        scheduledDate: new Date(2025, 3, 15),
        interviewee: 'Jane Doe',
        primaryInterviewer: 'David Wilson',
        type: 'IN_PERSON',
        duration: 90,
        questions: [],
        notes: [],
        participants: [],
        recordings: [],
        sections: [],
        createdAt: new Date(2025, 3, 10),
        updatedAt: new Date(2025, 3, 15),
        completedAt: new Date(2025, 3, 15)
      }
    ];
    
    // Save interviews to MongoDB
    for (const data of interviewData) {
      // Check if interview already exists
      const existingInterview = await Interview.findOne({ id: data.id });
      
      if (!existingInterview) {
        const interview = new Interview(data);
        await interview.save();
        console.log('Created interview:', interview.id);
      } else {
        console.log('Using existing interview:', existingInterview.id);
      }
    }
    
    // Create responses for all interviews
    const responses = [
      {
        interviewId: 'interview-1',
        templateId: savedTemplate.id,
        templateVersion: savedTemplate.version,
        responses: new Map([
          ['q1', 'TechCorp Inc.'],
          ['q2', 'Technology'],
          ['q3', '$500,000'],
          ['q4', 'Product development and market expansion']
        ]),
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        interviewId: 'interview-2',
        templateId: savedTemplate.id,
        templateVersion: savedTemplate.version,
        responses: new Map([
          ['q1', 'AgriGrow Ltd.'],
          ['q2', 'Agriculture'],
          ['q3', '$250,000'],
          ['q4', 'Purchase of new equipment and hiring staff']
        ]),
        status: 'submitted',
        submittedBy: 'David Wilson',
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    // Get all interviews to ensure each has a questionnaire
    const allInterviews = await Interview.find();
    console.log(`Found ${allInterviews.length} interviews to check for questionnaires`);
    
    // Clear existing responses and create new ones for all interviews
    responses.length = 0;
    
    // Create a response for each interview
    for (const interview of allInterviews) {
      console.log(`Creating questionnaire for interview: ${interview.id}`);
      responses.push({
        interviewId: interview.id,
        templateId: savedTemplate.id,
        templateVersion: savedTemplate.version,
        responses: new Map([
          ['q1', 'Auto-generated response'],
          ['q2', 'Technology'],
          ['q3', '$100,000'],
          ['q4', 'Development and expansion']
        ]),
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    // Save the responses
    for (const response of responses) {
      // Check if response already exists
      const existingResponse = await QuestionnaireResponse.findOne({ 
        interviewId: response.interviewId,
        templateId: response.templateId
      });
      
      if (!existingResponse) {
        const newResponse = new QuestionnaireResponse(response);
        const savedResponse = await newResponse.save();
        console.log('Created response:', savedResponse.id, 'for interview:', response.interviewId);
      } else {
        console.log('Using existing response:', existingResponse.id, 'for interview:', response.interviewId);
      }
    }
    
    console.log('Questionnaire data seeding completed successfully');
  } catch (error) {
    console.error('Error seeding questionnaire data:', error);
  }
}

// Export for use in other files
module.exports = seedQuestionnaireData;
