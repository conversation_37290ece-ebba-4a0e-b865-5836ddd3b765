const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const geographicalCoverageSchema = new Schema({
  provinces: [{ type: String }],
  municipalities: [{ type: String }]
});

const sectorsSchema = new Schema({
  included: [{ type: String }],
  excluded: [{ type: String }]
});

const budgetSchema = new Schema({
  totalAmount: { type: Number },
  currency: { type: String, default: 'ZAR' },
  allocated: { type: Number, default: 0 },
  remaining: { type: Number }
});

const timelineSchema = new Schema({
  startDate: { type: Date },
  endDate: { type: Date },
  applicationDeadline: { type: Date }
});

const fundingProgrammeSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  corporateSponsorId: { 
    type: Schema.Types.ObjectId, 
    ref: 'CorporateSponsor',
    required: true
  },
  objectives: [{ 
    type: String,
    trim: true
  }],
  fundingCriteria: [{ 
    type: String,
    trim: true
  }],
  fundingTerms: { 
    type: String,
    trim: true
  },
  fundingTypes: [{ 
    type: String, 
    enum: ['Grant', 'Loan', 'Equity', 'Convertible Note', 'Other']
  }],
  eligibilityCriteria: [{ 
    type: String,
    trim: true
  }],
  geographicalCoverage: geographicalCoverageSchema,
  sectors: sectorsSchema,
  fundingPurposes: [{ 
    type: String, 
    enum: [
      'Assets Acquisition',
      'Working Capital',
      'Business Expansion',
      'Research & Development',
      'Business Startup',
      'Skills Development',
      'Infrastructure Development',
      'Community Projects',
      'Other'
    ]
  }],
  budget: budgetSchema,
  timeline: timelineSchema,
  status: { 
    type: String, 
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    default: 'draft'
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { timestamps: true });

// Add text index for search functionality
fundingProgrammeSchema.index({ 
  name: 'text', 
  description: 'text', 
  objectives: 'text',
  fundingCriteria: 'text',
  eligibilityCriteria: 'text'
});

// Virtual for URL
fundingProgrammeSchema.virtual('url').get(function() {
  return `/funding-programmes/${this._id}`;
});

// Pre-save hook to calculate remaining budget
fundingProgrammeSchema.pre('save', function(next) {
  if (this.budget && this.budget.totalAmount) {
    this.budget.remaining = this.budget.totalAmount - (this.budget.allocated || 0);
  }
  next();
});

module.exports = mongoose.model('FundingProgramme', fundingProgrammeSchema);
