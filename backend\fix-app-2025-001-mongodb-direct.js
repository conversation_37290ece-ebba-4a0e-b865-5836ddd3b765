/**
 * MongoDB Direct Fix for APP-2025-001 Stage Hierarchy Issue
 * 
 * This is a MongoDB shell script that can be run directly in MongoDB
 * without requiring Node.js dependencies.
 * 
 * USAGE:
 * 1. Connect to MongoDB: mongo your-database-name
 * 2. Copy and paste this entire script into the MongoDB shell
 * 3. Press Enter to execute
 * 
 * OR save as .js file and run: mongo your-database-name fix-app-2025-001-mongodb-direct.js
 */

// MongoDB Shell Script - No Node.js dependencies required
print("🔧 Starting MongoDB direct fix for APP-2025-001 stage hierarchy...");

// Check if application exists
var app = db.applications.findOne({id: "APP-2025-001"});

if (!app) {
    print("❌ Application APP-2025-001 not found");
    quit(1);
}

print("📋 Found application: " + app.id);
print("📊 Current stage hierarchy status:");
print("  - Has stageHierarchy: " + (app.stageHierarchy ? "Yes" : "No"));
print("  - StageHierarchy length: " + (app.stageHierarchy ? app.stageHierarchy.length : 0));
print("  - Current main stage: " + (app.currentMainStage || "Not set"));
print("  - Current sub stage: " + (app.currentSubStage || "Not set"));
print("  - Current stage status: " + (app.currentStageStatus || "Not set"));

// Check if stage hierarchy needs initialization
if (!app.stageHierarchy || app.stageHierarchy.length === 0) {
    print("🚀 Initializing stage hierarchy...");
    
    // Complete stage hierarchy structure
    var stageHierarchy = [
        {
            mainStage: "ONBOARDING",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "BENEFICIARY_REGISTRATION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "PRE_SCREENING",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "BUSINESS_CASE_REVIEW",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "DOCUMENT_COLLECTION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "DESKTOP_ANALYSIS",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "DUE_DILIGENCE",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "DATA_VALIDATION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "SME_INTERVIEW",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "SITE_VISIT",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "ASSESSMENT_REPORT",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "REPORT_COMPLETION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "REPORT_QUALITY_CHECK",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "REPORT_REVIEW",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "APPLICATION_APPROVAL",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "CORPORATE_APPROVAL_1",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_2",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_3",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_4",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        }
    ];
    
    // Prepare update document
    var updateDoc = {
        $set: {
            stageHierarchy: stageHierarchy
        }
    };
    
    // Set default current stage if not set
    if (!app.currentMainStage) {
        updateDoc.$set.currentMainStage = "ONBOARDING";
        updateDoc.$set.currentSubStage = "BENEFICIARY_REGISTRATION";
        updateDoc.$set.currentStageStatus = "NOT_STARTED";
    }
    
    // Add initial audit log entry
    if (!app.stageStatusAuditLog) {
        updateDoc.$set.stageStatusAuditLog = [];
    }
    
    updateDoc.$push = {
        stageStatusAuditLog: {
            timestamp: new Date(),
            mainStage: app.currentMainStage || "ONBOARDING",
            subStage: app.currentSubStage || "BENEFICIARY_REGISTRATION",
            fromStatus: null,
            toStatus: app.currentStageStatus || "NOT_STARTED",
            changedBy: "system",
            reason: "Stage hierarchy initialization - fixing missing stage data via MongoDB direct fix",
            isOverride: false
        }
    };
    
    // Execute the update
    var result = db.applications.updateOne(
        {id: "APP-2025-001"},
        updateDoc
    );
    
    if (result.modifiedCount === 1) {
        print("✅ Stage hierarchy initialized successfully!");
        print("📈 New stage hierarchy structure:");
        stageHierarchy.forEach(function(mainStage) {
            print("  📁 " + mainStage.mainStage + " (" + mainStage.status + ")");
            mainStage.subStages.forEach(function(subStage) {
                print("    📄 " + subStage.subStage + " (" + subStage.status.status + ")");
            });
        });
    } else {
        print("❌ Failed to update application");
        quit(1);
    }
    
} else {
    print("ℹ️  Stage hierarchy already exists");
    print("📈 Current stage hierarchy:");
    app.stageHierarchy.forEach(function(mainStage) {
        print("  📁 " + mainStage.mainStage + " (" + mainStage.status + ")");
        if (mainStage.subStages) {
            mainStage.subStages.forEach(function(subStage) {
                print("    📄 " + subStage.subStage + " (" + subStage.status.status + ")");
            });
        }
    });
}

print("\n🎉 Fix completed successfully!");
print("💡 You can now refresh the application page to see the Stage Manager tab working.");

// Verify the fix
print("\n🔍 Verification - checking updated application:");
var updatedApp = db.applications.findOne({id: "APP-2025-001"});
print("  - Stage hierarchy length: " + (updatedApp.stageHierarchy ? updatedApp.stageHierarchy.length : 0));
print("  - Current main stage: " + (updatedApp.currentMainStage || "Not set"));
print("  - Current sub stage: " + (updatedApp.currentSubStage || "Not set"));
print("  - Current stage status: " + (updatedApp.currentStageStatus || "Not set"));
print("  - Audit log entries: " + (updatedApp.stageStatusAuditLog ? updatedApp.stageStatusAuditLog.length : 0));

print("\n✨ MongoDB direct fix completed!");
