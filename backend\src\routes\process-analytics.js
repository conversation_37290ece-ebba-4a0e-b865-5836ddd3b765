const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const AuditTrailService = require('../services/audit-trail.service');
const StageTransitionAudit = require('../models/stage-transition-audit');
const { ApplicationMainStage, ApplicationSubStage } = require('../models/stage-status-enums');

/**
 * Process Analytics Routes
 * Provides endpoints for stage transition analytics and process insights
 */

// Get processing times by stage
router.get('/processing-times', async (req, res) => {
  try {
    const {
      programmeId,
      corporateSponsorId,
      startDate,
      endDate,
      mainStage,
      subStage
    } = req.query;

    console.log('=== PROCESSING TIMES DETAILED DEBUG ===');
    console.log('Raw query parameters:', req.query);
    console.log('Request headers:', req.headers);
    console.log('Request URL:', req.url);

    // Build filters with ObjectId validation and conversion
    const filters = {};
    
    if (programmeId) {
      if (isValidObjectId(programmeId)) {
        filters.programmeId = new mongoose.Types.ObjectId(programmeId);
        console.log('✓ Added programmeId filter:', filters.programmeId);
      } else {
        console.warn('❌ Invalid programmeId format:', programmeId);
        return res.status(400).json({
          success: false,
          message: 'Invalid programme ID format',
          error: 'Programme ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (corporateSponsorId) {
      if (isValidObjectId(corporateSponsorId)) {
        filters.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
        console.log('✓ Added corporateSponsorId filter:', filters.corporateSponsorId);
      } else {
        console.warn('❌ Invalid corporateSponsorId format:', corporateSponsorId);
        return res.status(400).json({
          success: false,
          message: 'Invalid corporate sponsor ID format',
          error: 'Corporate sponsor ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (mainStage) {
      filters.fromMainStage = mainStage;
      console.log('✓ Added mainStage filter:', mainStage);
    }
    if (subStage) {
      filters.fromSubStage = subStage;
      console.log('✓ Added subStage filter:', subStage);
    }
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) {
        filters.timestamp.$gte = new Date(startDate);
        console.log('✓ Added startDate filter:', startDate);
      }
      if (endDate) {
        filters.timestamp.$lte = new Date(endDate);
        console.log('✓ Added endDate filter:', endDate);
      }
    }

    console.log('🔍 Final built filters:', JSON.stringify(filters, null, 2));

    // Debug: Check total stage transitions before filtering
    const totalTransitions = await StageTransitionAudit.countDocuments({});
    console.log('📊 Total stage transitions in database:', totalTransitions);

    // Debug: Check transitions matching each filter individually
    if (corporateSponsorId) {
      console.log('🔍 Testing corporateSponsorId filter...');
      
      // Test with string comparison first
      const transitionsWithSponsorString = await StageTransitionAudit.countDocuments({
        corporateSponsorId: corporateSponsorId
      });
      console.log(`📊 Transitions with corporateSponsorId as string "${corporateSponsorId}":`, transitionsWithSponsorString);
      
      // Test with ObjectId comparison
      const transitionsWithSponsorObjectId = await StageTransitionAudit.countDocuments({
        corporateSponsorId: new mongoose.Types.ObjectId(corporateSponsorId)
      });
      console.log(`📊 Transitions with corporateSponsorId as ObjectId:`, transitionsWithSponsorObjectId);
      
      // Get sample transitions to see actual data structure
      const sampleTransitions = await StageTransitionAudit.find({})
        .limit(5)
        .select('applicationId corporateSponsorId programmeId fromMainStage fromSubStage durationInPreviousStage')
        .lean();
      console.log('📋 Sample transitions (first 5):', JSON.stringify(sampleTransitions, null, 2));
      
      // Check what corporateSponsorId values exist in the database
      const distinctSponsorIds = await StageTransitionAudit.distinct('corporateSponsorId');
      console.log('📋 Distinct corporateSponsorId values in database:', distinctSponsorIds);
    }

    // Debug: Check transitions with valid durations
    const transitionsWithDuration = await StageTransitionAudit.countDocuments({
      durationInPreviousStage: { $exists: true, $ne: null }
    });
    console.log('📊 Transitions with valid duration:', transitionsWithDuration);

    // Debug: Test the exact aggregation pipeline that will be used
    console.log('🔍 Testing aggregation pipeline...');
    const testPipeline = [
      {
        $match: {
          durationInPreviousStage: { $exists: true, $ne: null },
          ...filters
        }
      },
      {
        $group: {
          _id: {
            mainStage: '$fromMainStage',
            subStage: '$fromSubStage'
          },
          avgDuration: { $avg: '$durationInPreviousStage' },
          minDuration: { $min: '$durationInPreviousStage' },
          maxDuration: { $max: '$durationInPreviousStage' },
          count: { $sum: 1 },
          totalDuration: { $sum: '$durationInPreviousStage' }
        }
      },
      {
        $sort: { avgDuration: -1 }
      }
    ];
    
    console.log('🔍 Aggregation pipeline:', JSON.stringify(testPipeline, null, 2));
    
    // Test the match stage separately
    const matchStageResult = await StageTransitionAudit.countDocuments({
      durationInPreviousStage: { $exists: true, $ne: null },
      ...filters
    });
    console.log('📊 Documents matching the $match stage:', matchStageResult);

    const processingTimes = await AuditTrailService.getProcessingTimesByStage(filters);
    console.log('📊 Processing times result count:', processingTimes.length);
    console.log('📋 Processing times sample:', JSON.stringify(processingTimes.slice(0, 2), null, 2));
    
    // Format the results for better readability
    const formattedResults = processingTimes.map(item => ({
      mainStage: item._id.mainStage,
      subStage: item._id.subStage,
      averageDurationMs: item.avgDuration,
      averageDurationDays: Math.round((item.avgDuration / (1000 * 60 * 60 * 24)) * 100) / 100,
      averageDurationHours: Math.round((item.avgDuration / (1000 * 60 * 60)) * 100) / 100,
      minDurationMs: item.minDuration,
      maxDurationMs: item.maxDuration,
      totalDurationMs: item.totalDuration,
      transitionCount: item.count
    }));

    res.json({
      success: true,
      data: formattedResults,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting processing times:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get processing times',
      error: error.message
    });
  }
});

// Get bottleneck analysis
router.get('/bottlenecks', async (req, res) => {
  try {
    const {
      programmeId,
      corporateSponsorId,
      startDate,
      endDate,
      limit = 10
    } = req.query;

    console.log('=== BOTTLENECKS DEBUG ===');
    console.log('Query parameters:', req.query);

    // Build filters with ObjectId validation and conversion
    const filters = {};
    
    if (programmeId) {
      if (isValidObjectId(programmeId)) {
        filters.programmeId = new mongoose.Types.ObjectId(programmeId);
      } else {
        console.warn('Invalid programmeId format:', programmeId);
        return res.status(400).json({
          success: false,
          message: 'Invalid programme ID format',
          error: 'Programme ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (corporateSponsorId) {
      if (isValidObjectId(corporateSponsorId)) {
        filters.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
      } else {
        console.warn('Invalid corporateSponsorId format:', corporateSponsorId);
        return res.status(400).json({
          success: false,
          message: 'Invalid corporate sponsor ID format',
          error: 'Corporate sponsor ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) filters.timestamp.$gte = new Date(startDate);
      if (endDate) filters.timestamp.$lte = new Date(endDate);
    }

    console.log('Built filters for bottlenecks:', JSON.stringify(filters, null, 2));

    const bottlenecks = await AuditTrailService.getBottleneckAnalysis(filters);
    console.log('Bottlenecks result count:', bottlenecks.length);
    console.log('Bottlenecks sample:', JSON.stringify(bottlenecks.slice(0, 2), null, 2));
    
    // Format results and add severity indicators
    const formattedBottlenecks = bottlenecks.slice(0, parseInt(limit)).map(item => {
      const avgDays = item.avgDurationDays;
      let severity = 'LOW';
      
      if (avgDays > 30) severity = 'CRITICAL';
      else if (avgDays > 14) severity = 'HIGH';
      else if (avgDays > 7) severity = 'MEDIUM';
      
      return {
        mainStage: item._id.mainStage,
        subStage: item._id.subStage,
        averageDurationDays: Math.round(avgDays * 100) / 100,
        averageDurationMs: item.avgDuration,
        transitionCount: item.count,
        affectedApplications: item.applications.length,
        severity: severity,
        applicationIds: item.applications
      };
    });

    res.json({
      success: true,
      data: formattedBottlenecks,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting bottleneck analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get bottleneck analysis',
      error: error.message
    });
  }
});

// Get stage completion rates
router.get('/completion-rates', async (req, res) => {
  try {
    const { 
      programmeId, 
      corporateSponsorId, 
      startDate, 
      endDate 
    } = req.query;

    // Build filters with ObjectId validation and conversion
    const filters = {};
    
    if (programmeId) {
      if (isValidObjectId(programmeId)) {
        filters.programmeId = new mongoose.Types.ObjectId(programmeId);
      } else {
        console.warn('Invalid programmeId format:', programmeId);
        return res.status(400).json({
          success: false,
          message: 'Invalid programme ID format',
          error: 'Programme ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (corporateSponsorId) {
      if (isValidObjectId(corporateSponsorId)) {
        filters.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
      } else {
        console.warn('Invalid corporateSponsorId format:', corporateSponsorId);
        return res.status(400).json({
          success: false,
          message: 'Invalid corporate sponsor ID format',
          error: 'Corporate sponsor ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) filters.timestamp.$gte = new Date(startDate);
      if (endDate) filters.timestamp.$lte = new Date(endDate);
    }

    const completionRates = await AuditTrailService.getStageCompletionRates(filters);
    
    // Format results with additional insights
    const formattedRates = completionRates.map(item => ({
      mainStage: item._id.mainStage,
      subStage: item._id.subStage,
      totalTransitions: item.totalTransitions,
      completedTransitions: item.completedTransitions,
      completionRate: Math.round(item.completionRate * 100) / 100,
      incompletedTransitions: item.totalTransitions - item.completedTransitions,
      status: item.completionRate >= 80 ? 'GOOD' : 
              item.completionRate >= 60 ? 'FAIR' : 'POOR'
    }));

    res.json({
      success: true,
      data: formattedRates,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting completion rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get completion rates',
      error: error.message
    });
  }
});

// Get workflow efficiency metrics
router.get('/workflow-efficiency', async (req, res) => {
  try {
    const { 
      programmeId, 
      corporateSponsorId, 
      startDate, 
      endDate 
    } = req.query;

    // Build filters with ObjectId validation and conversion
    const filters = {};
    
    if (programmeId) {
      if (isValidObjectId(programmeId)) {
        filters.programmeId = new mongoose.Types.ObjectId(programmeId);
      } else {
        console.warn('Invalid programmeId format:', programmeId);
        return res.status(400).json({
          success: false,
          message: 'Invalid programme ID format',
          error: 'Programme ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (corporateSponsorId) {
      if (isValidObjectId(corporateSponsorId)) {
        filters.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
      } else {
        console.warn('Invalid corporateSponsorId format:', corporateSponsorId);
        return res.status(400).json({
          success: false,
          message: 'Invalid corporate sponsor ID format',
          error: 'Corporate sponsor ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) filters.timestamp.$gte = new Date(startDate);
      if (endDate) filters.timestamp.$lte = new Date(endDate);
    }

    const metrics = await AuditTrailService.getWorkflowEfficiencyMetrics(filters);
    
    // Add additional calculated metrics
    const enhancedMetrics = {
      ...metrics,
      avgProcessingTimeFormatted: AuditTrailService.formatDuration(metrics.avgProcessingTimeMs || 0),
      efficiency: {
        score: metrics.avgProcessingTimeDays <= 30 ? 'EXCELLENT' :
               metrics.avgProcessingTimeDays <= 60 ? 'GOOD' :
               metrics.avgProcessingTimeDays <= 90 ? 'FAIR' : 'POOR',
        avgDaysPerStage: Math.round((metrics.avgProcessingTimeDays / 5) * 100) / 100, // Assuming 5 main stages
        recommendedImprovements: []
      }
    };

    // Add recommendations based on bottlenecks
    if (metrics.bottlenecks && metrics.bottlenecks.length > 0) {
      const topBottleneck = metrics.bottlenecks[0];
      if (topBottleneck.avgDurationDays > 14) {
        enhancedMetrics.efficiency.recommendedImprovements.push(
          `Focus on optimizing ${topBottleneck._id.mainStage}/${topBottleneck._id.subStage} stage (${Math.round(topBottleneck.avgDurationDays)} days average)`
        );
      }
    }

    res.json({
      success: true,
      data: enhancedMetrics,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting workflow efficiency metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get workflow efficiency metrics',
      error: error.message
    });
  }
});

// Get application journey
router.get('/application-journey/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;
    
    const journey = await AuditTrailService.getApplicationJourney(applicationId);
    
    // Calculate stage durations and add insights
    const enhancedJourney = journey.map((transition, index) => {
      const nextTransition = journey[index + 1];
      let timeInStage = null;
      
      if (nextTransition) {
        const duration = nextTransition.timestamp.getTime() - transition.timestamp.getTime();
        timeInStage = {
          milliseconds: duration,
          formatted: AuditTrailService.formatDuration(duration),
          days: Math.round((duration / (1000 * 60 * 60 * 24)) * 100) / 100
        };
      }
      
      return {
        ...transition,
        timeInStage,
        isCurrentStage: index === journey.length - 1
      };
    });

    // Calculate total journey time
    const totalJourneyTime = journey.length > 1 ? 
      journey[journey.length - 1].timestamp.getTime() - journey[0].timestamp.getTime() : 0;

    res.json({
      success: true,
      data: {
        applicationId,
        journey: enhancedJourney,
        totalStages: journey.length,
        totalJourneyTime: {
          milliseconds: totalJourneyTime,
          formatted: AuditTrailService.formatDuration(totalJourneyTime),
          days: Math.round((totalJourneyTime / (1000 * 60 * 60 * 24)) * 100) / 100
        },
        currentStage: journey.length > 0 ? {
          mainStage: journey[journey.length - 1].toMainStage,
          subStage: journey[journey.length - 1].toSubStage,
          status: journey[journey.length - 1].toStageStatus
        } : null
      },
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting application journey:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get application journey',
      error: error.message
    });
  }
});

// Get user performance metrics
router.get('/user-performance', async (req, res) => {
  try {
    const { 
      startDate, 
      endDate,
      userId 
    } = req.query;

    // Build filters
    const filters = {};
    
    if (userId) filters.userId = userId;
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) filters.timestamp.$gte = new Date(startDate);
      if (endDate) filters.timestamp.$lte = new Date(endDate);
    }

    const userMetrics = await AuditTrailService.getUserPerformanceMetrics(filters);
    
    // Format and rank users
    const formattedMetrics = userMetrics.map((user, index) => ({
      rank: index + 1,
      userId: user._id.userId,
      userName: user._id.userName || 'Unknown User',
      averageProcessingTimeMs: user.avgProcessingTime,
      averageProcessingTimeDays: Math.round(user.avgProcessingTimeDays * 100) / 100,
      averageProcessingTimeFormatted: AuditTrailService.formatDuration(user.avgProcessingTime),
      totalTransitions: user.totalTransitions,
      applicationsHandled: user.applicationsCount,
      efficiency: user.avgProcessingTimeDays <= 7 ? 'EXCELLENT' :
                 user.avgProcessingTimeDays <= 14 ? 'GOOD' :
                 user.avgProcessingTimeDays <= 21 ? 'FAIR' : 'NEEDS_IMPROVEMENT'
    }));

    res.json({
      success: true,
      data: formattedMetrics,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting user performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user performance metrics',
      error: error.message
    });
  }
});

// Get dashboard summary
router.get('/dashboard-summary', async (req, res) => {
  try {
    const { 
      programmeId, 
      corporateSponsorId, 
      startDate, 
      endDate 
    } = req.query;

    // Build filters with ObjectId validation and conversion
    const filters = {};
    
    if (programmeId) {
      if (isValidObjectId(programmeId)) {
        filters.programmeId = new mongoose.Types.ObjectId(programmeId);
      } else {
        console.warn('Invalid programmeId format:', programmeId);
        return res.status(400).json({
          success: false,
          message: 'Invalid programme ID format',
          error: 'Programme ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (corporateSponsorId) {
      if (isValidObjectId(corporateSponsorId)) {
        filters.corporateSponsorId = new mongoose.Types.ObjectId(corporateSponsorId);
      } else {
        console.warn('Invalid corporateSponsorId format:', corporateSponsorId);
        return res.status(400).json({
          success: false,
          message: 'Invalid corporate sponsor ID format',
          error: 'Corporate sponsor ID must be a valid MongoDB ObjectId'
        });
      }
    }
    
    if (startDate || endDate) {
      filters.timestamp = {};
      if (startDate) filters.timestamp.$gte = new Date(startDate);
      if (endDate) filters.timestamp.$lte = new Date(endDate);
    }

    // Get all metrics in parallel
    const [
      processingTimes,
      bottlenecks,
      completionRates,
      workflowMetrics
    ] = await Promise.all([
      AuditTrailService.getProcessingTimesByStage(filters),
      AuditTrailService.getBottleneckAnalysis(filters),
      AuditTrailService.getStageCompletionRates(filters),
      AuditTrailService.getWorkflowEfficiencyMetrics(filters)
    ]);

    // Calculate summary statistics
    const summary = {
      totalApplicationsTracked: workflowMetrics.totalApplications,
      averageProcessingTimeDays: Math.round(workflowMetrics.avgProcessingTimeDays * 100) / 100,
      totalStagesTracked: processingTimes.length,
      criticalBottlenecks: bottlenecks.filter(b => b.avgDurationDays > 30).length,
      averageCompletionRate: completionRates.length > 0 ? 
        Math.round((completionRates.reduce((sum, rate) => sum + rate.completionRate, 0) / completionRates.length) * 100) / 100 : 0,
      
      // Top insights
      topBottleneck: bottlenecks.length > 0 ? {
        stage: `${bottlenecks[0]._id.mainStage}/${bottlenecks[0]._id.subStage}`,
        avgDays: Math.round(bottlenecks[0].avgDurationDays * 100) / 100,
        affectedApplications: bottlenecks[0].applications.length
      } : null,
      
      fastestStage: processingTimes.length > 0 ? {
        stage: `${processingTimes[processingTimes.length - 1]._id.mainStage}/${processingTimes[processingTimes.length - 1]._id.subStage}`,
        avgDays: Math.round((processingTimes[processingTimes.length - 1].avgDuration / (1000 * 60 * 60 * 24)) * 100) / 100
      } : null,
      
      // Recent activity
      recentTransitions: await StageTransitionAudit.find(filters)
        .sort({ timestamp: -1 })
        .limit(5)
        .select('applicationId toMainStage toSubStage timestamp')
        .lean()
    };

    res.json({
      success: true,
      data: summary,
      filters: filters,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting dashboard summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dashboard summary',
      error: error.message
    });
  }
});

// Initialize audit trails for existing applications
router.post('/initialize-audit-trails', async (req, res) => {
  try {
    console.log('Initializing audit trails for existing applications...');
    
    const auditEntries = await AuditTrailService.createInitialAuditEntries();
    
    res.json({
      success: true,
      message: `Successfully created ${auditEntries.length} initial audit entries`,
      data: {
        entriesCreated: auditEntries.length,
        entries: auditEntries.map(entry => ({
          applicationId: entry.applicationId,
          stage: `${entry.toMainStage}/${entry.toSubStage}`,
          status: entry.toStageStatus,
          timestamp: entry.timestamp
        }))
      }
    });
  } catch (error) {
    console.error('Error initializing audit trails:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize audit trails',
      error: error.message
    });
  }
});

/**
 * Helper function to validate MongoDB ObjectId format
 */
function isValidObjectId(id) {
  if (!id || typeof id !== 'string') {
    return false;
  }
  return /^[0-9a-fA-F]{24}$/.test(id);
}

module.exports = router;