const StageTransitionAudit = require('../models/stage-transition-audit');
const Application = require('../models/application');
const { ApplicationMainStage, ApplicationSubStage, StageStatus } = require('../models/stage-status-enums');

/**
 * Audit Trail Service
 * Handles logging and querying of stage transitions for process analytics
 */
class AuditTrailService {
  
  /**
   * Log a stage transition
   * @param {Object} transitionData - The transition data
   * @param {string} transitionData.applicationId - Application ID
   * @param {Object} transitionData.from - Previous state
   * @param {Object} transitionData.to - New state
   * @param {Object} transitionData.context - Additional context
   * @returns {Promise<Object>} The created audit entry
   */
  static async logStageTransition(transitionData) {
    try {
      const {
        applicationId,
        applicationObjectId,
        from = {},
        to = {},
        context = {}
      } = transitionData;

      // Determine transition type
      const transitionType = this.determineTransitionType(from, to);
      
      // Calculate duration in previous stage
      const duration = await this.calculateDurationInPreviousStage(applicationId, from);
      
      // Get application details for additional context
      const application = await Application.findOne({ id: applicationId })
        .select('programmeId corporateSponsorId')
        .lean();

      const auditEntry = new StageTransitionAudit({
        applicationId,
        applicationObjectId: applicationObjectId || application?._id,
        transitionType,
        
        // Previous state
        fromMainStage: from.mainStage,
        fromSubStage: from.subStage,
        fromStageStatus: from.stageStatus,
        fromApplicationStatus: from.applicationStatus,
        
        // New state
        toMainStage: to.mainStage,
        toSubStage: to.subStage,
        toStageStatus: to.stageStatus,
        toApplicationStatus: to.applicationStatus,
        
        // Duration
        durationInPreviousStage: duration?.milliseconds,
        durationInPreviousStageFormatted: duration?.formatted,
        
        // Context
        userId: context.userId,
        userName: context.userName,
        reason: context.reason,
        notes: context.notes,
        isSystemGenerated: context.isSystemGenerated || false,
        isInitialEntry: context.isInitialEntry || false,
        
        // Application context
        programmeId: application?.programmeId,
        corporateSponsorId: application?.corporateSponsorId,
        
        timestamp: new Date()
      });

      const savedEntry = await auditEntry.save();
      
      console.log(`Audit trail logged for application ${applicationId}: ${from.mainStage}/${from.subStage} -> ${to.mainStage}/${to.subStage}`);
      
      return savedEntry;
    } catch (error) {
      console.error('Error logging stage transition:', error);
      throw error;
    }
  }

  /**
   * Determine the type of transition
   * @param {Object} from - Previous state
   * @param {Object} to - New state
   * @returns {string} Transition type
   */
  static determineTransitionType(from, to) {
    const stageChanged = from.mainStage !== to.mainStage;
    const subStageChanged = from.subStage !== to.subStage;
    const statusChanged = from.stageStatus !== to.stageStatus || from.applicationStatus !== to.applicationStatus;
    
    if (stageChanged && subStageChanged) {
      return 'COMBINED_CHANGE';
    } else if (stageChanged) {
      return 'STAGE_CHANGE';
    } else if (subStageChanged) {
      return 'SUBSTAGE_CHANGE';
    } else if (statusChanged) {
      return 'STATUS_CHANGE';
    }
    
    return 'STATUS_CHANGE'; // Default
  }

  /**
   * Calculate duration in previous stage
   * @param {string} applicationId - Application ID
   * @param {Object} fromState - Previous state
   * @returns {Promise<Object>} Duration information
   */
  static async calculateDurationInPreviousStage(applicationId, fromState) {
    try {
      if (!fromState.mainStage || !fromState.subStage) {
        return null; // No previous state
      }

      // Find the last transition to the previous stage
      const lastTransition = await StageTransitionAudit.findOne({
        applicationId,
        toMainStage: fromState.mainStage,
        toSubStage: fromState.subStage
      }).sort({ timestamp: -1 });

      if (!lastTransition) {
        return null; // No previous transition found
      }

      const now = new Date();
      const milliseconds = now.getTime() - lastTransition.timestamp.getTime();
      const formatted = this.formatDuration(milliseconds);

      return {
        milliseconds,
        formatted,
        startTime: lastTransition.timestamp,
        endTime: now
      };
    } catch (error) {
      console.error('Error calculating duration:', error);
      return null;
    }
  }

  /**
   * Format duration in human-readable format
   * @param {number} milliseconds - Duration in milliseconds
   * @returns {string} Formatted duration
   */
  static formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Create initial audit entries for existing applications
   * @param {Array} applications - Array of applications
   * @returns {Promise<Array>} Created audit entries
   */
  static async createInitialAuditEntries(applications = null) {
    try {
      // Get all applications if not provided
      if (!applications) {
        applications = await Application.find({})
          .select('id _id currentMainStage currentSubStage currentStageStatus status programmeId corporateSponsorId submissionDate')
          .lean();
      }

      const auditEntries = [];

      for (const app of applications) {
        // Check if initial entry already exists
        const existingEntry = await StageTransitionAudit.findOne({
          applicationId: app.id,
          isInitialEntry: true
        });

        if (existingEntry) {
          console.log(`Initial audit entry already exists for application ${app.id}`);
          continue;
        }

        // Create initial audit entry
        const initialEntry = new StageTransitionAudit({
          applicationId: app.id,
          applicationObjectId: app._id,
          transitionType: 'COMBINED_CHANGE',
          
          // No previous state for initial entry
          fromMainStage: null,
          fromSubStage: null,
          fromStageStatus: null,
          fromApplicationStatus: null,
          
          // Current state
          toMainStage: app.currentMainStage || ApplicationMainStage.ONBOARDING,
          toSubStage: app.currentSubStage || ApplicationSubStage.BENEFICIARY_REGISTRATION,
          toStageStatus: app.currentStageStatus || StageStatus.NOT_STARTED,
          toApplicationStatus: app.status || 'pending',
          
          // No duration for initial entry
          durationInPreviousStage: null,
          durationInPreviousStageFormatted: null,
          
          // Context
          userId: 'system',
          userName: 'System',
          reason: 'Initial application state',
          notes: 'Baseline audit entry for existing application',
          isSystemGenerated: true,
          isInitialEntry: true,
          
          // Application context
          programmeId: app.programmeId,
          corporateSponsorId: app.corporateSponsorId,
          
          // Use submission date or current date
          timestamp: app.submissionDate || new Date()
        });

        const savedEntry = await initialEntry.save();
        auditEntries.push(savedEntry);
        
        console.log(`Created initial audit entry for application ${app.id}`);
      }

      console.log(`Created ${auditEntries.length} initial audit entries`);
      return auditEntries;
    } catch (error) {
      console.error('Error creating initial audit entries:', error);
      throw error;
    }
  }

  /**
   * Get application journey
   * @param {string} applicationId - Application ID
   * @returns {Promise<Array>} Application journey
   */
  static async getApplicationJourney(applicationId) {
    return StageTransitionAudit.getApplicationJourney(applicationId);
  }

  /**
   * Get processing times by stage
   * @param {Object} filters - Query filters
   * @returns {Promise<Array>} Processing times
   */
  static async getProcessingTimesByStage(filters = {}) {
    return StageTransitionAudit.getProcessingTimesByStage(filters);
  }

  /**
   * Get bottleneck analysis
   * @param {Object} filters - Query filters
   * @returns {Promise<Array>} Bottleneck analysis
   */
  static async getBottleneckAnalysis(filters = {}) {
    return StageTransitionAudit.getBottleneckAnalysis(filters);
  }

  /**
   * Get stage completion rates
   * @param {Object} filters - Query filters
   * @returns {Promise<Array>} Completion rates
   */
  static async getStageCompletionRates(filters = {}) {
    return StageTransitionAudit.getStageCompletionRates(filters);
  }

  /**
   * Get workflow efficiency metrics
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Efficiency metrics
   */
  static async getWorkflowEfficiencyMetrics(filters = {}) {
    try {
      const [processingTimes, bottlenecks, completionRates] = await Promise.all([
        this.getProcessingTimesByStage(filters),
        this.getBottleneckAnalysis(filters),
        this.getStageCompletionRates(filters)
      ]);

      // Calculate overall metrics
      const totalApplications = await StageTransitionAudit.distinct('applicationId', filters);
      const avgProcessingTime = processingTimes.reduce((sum, stage) => sum + stage.avgDuration, 0) / processingTimes.length;

      return {
        totalApplications: totalApplications.length,
        avgProcessingTimeMs: avgProcessingTime,
        avgProcessingTimeDays: avgProcessingTime / (1000 * 60 * 60 * 24),
        processingTimesByStage: processingTimes,
        bottlenecks: bottlenecks,
        completionRates: completionRates,
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('Error getting workflow efficiency metrics:', error);
      throw error;
    }
  }

  /**
   * Get user performance metrics
   * @param {Object} filters - Query filters
   * @returns {Promise<Array>} User performance data
   */
  static async getUserPerformanceMetrics(filters = {}) {
    try {
      const pipeline = [
        {
          $match: {
            userId: { $exists: true, $ne: null },
            durationInPreviousStage: { $exists: true, $ne: null },
            ...filters
          }
        },
        {
          $group: {
            _id: {
              userId: '$userId',
              userName: '$userName'
            },
            avgProcessingTime: { $avg: '$durationInPreviousStage' },
            totalTransitions: { $sum: 1 },
            applicationsHandled: { $addToSet: '$applicationId' }
          }
        },
        {
          $addFields: {
            avgProcessingTimeDays: { $divide: ['$avgProcessingTime', 86400000] },
            applicationsCount: { $size: '$applicationsHandled' }
          }
        },
        {
          $sort: { avgProcessingTime: 1 }
        }
      ];

      return StageTransitionAudit.aggregate(pipeline);
    } catch (error) {
      console.error('Error getting user performance metrics:', error);
      throw error;
    }
  }
}

module.exports = AuditTrailService;