/**
 * Email Service
 * 
 * This service provides functionality for sending emails for committee meeting
 * notifications, reminders, and other communications. In a production environment,
 * this would integrate with external email services like SendGrid, Mailgun, etc.
 */

const logger = require('../logger');

/**
 * Send an email
 * @param {string} to Recipient email address
 * @param {string} subject Email subject
 * @param {string} body Email body (HTML or plain text)
 * @param {Object} options Additional options (attachments, cc, bcc, etc.)
 * @returns {Promise<boolean>} Success status
 */
async function sendEmail(to, subject, body, options = {}) {
  try {
    logger.info(`Sending email to: ${to}`);
    logger.info(`Subject: ${subject}`);
    
    // In a real implementation, this would call an external email API
    // For now, we'll just log the email details
    logger.info('Email sent successfully');
    
    return true;
  } catch (error) {
    logger.error('Error sending email:', error);
    throw new Error('Failed to send email');
  }
}

/**
 * Send meeting invitation emails
 * @param {Object} meeting Committee meeting data
 * @param {Array} recipients List of recipients with email addresses
 * @returns {Promise<Array>} Array of notification results
 */
async function sendMeetingInvitations(meeting, recipients) {
  try {
    logger.info(`Sending meeting invitations for meeting: ${meeting.id}`);
    
    const results = [];
    
    for (const recipient of recipients) {
      const subject = `Committee Meeting Invitation: ${meeting.title}`;
      
      // Format date and time
      const meetingDate = new Date(meeting.date).toLocaleDateString('en-ZA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Generate location string
      let location = '';
      if (meeting.location === 'virtual') {
        location = `Virtual Meeting: ${meeting.meetingLink || 'Link to be provided'}`;
      } else if (meeting.location === 'physical' && meeting.physicalAddress) {
        const addr = meeting.physicalAddress;
        location = `${addr.street}, ${addr.city}, ${addr.province}, ${addr.country}`;
      } else if (meeting.location === 'hybrid') {
        const physical = meeting.physicalAddress ? 
          `${meeting.physicalAddress.street}, ${meeting.physicalAddress.city}` : 
          'Physical location to be confirmed';
        location = `Hybrid Meeting: ${physical} and Virtual (${meeting.meetingLink || 'Link to be provided'})`;
      }
      
      // Generate email body
      const body = `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1976d2; color: white; padding: 10px 20px; }
            .content { padding: 20px; border: 1px solid #ddd; }
            .footer { font-size: 12px; color: #666; margin-top: 20px; }
            .button { display: inline-block; background-color: #1976d2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Committee Meeting Invitation</h2>
            </div>
            <div class="content">
              <p>Dear ${recipient.name || recipient.email},</p>
              
              <p>You are invited to attend the following committee meeting:</p>
              
              <h3>${meeting.title}</h3>
              <p><strong>Date:</strong> ${meetingDate}</p>
              <p><strong>Time:</strong> ${meeting.startTime} - ${meeting.endTime}</p>
              <p><strong>Location:</strong> ${location}</p>
              
              ${meeting.description ? `<p><strong>Description:</strong> ${meeting.description}</p>` : ''}
              
              <p>Please confirm your attendance by clicking one of the following options:</p>
              
              <p>
                <a href="#" class="button">Accept</a>
                <a href="#" class="button" style="background-color: #f44336;">Decline</a>
                <a href="#" class="button" style="background-color: #ff9800;">Tentative</a>
              </p>
              
              <p>If you have any questions, please contact the meeting organizer.</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
        </html>
      `;
      
      // Send the email
      try {
        await sendEmail(recipient.email, subject, body, {
          isHtml: true,
          attachments: [
            {
              filename: 'meeting.ics',
              content: generateICalendarAttachment(meeting)
            }
          ]
        });
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'sent',
          sentDate: new Date()
        });
      } catch (error) {
        logger.error(`Error sending invitation to ${recipient.email}:`, error);
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error sending meeting invitations:', error);
    throw new Error('Failed to send meeting invitations');
  }
}

/**
 * Send meeting reminder emails
 * @param {Object} meeting Committee meeting data
 * @param {Array} recipients List of recipients with email addresses
 * @returns {Promise<Array>} Array of notification results
 */
async function sendMeetingReminders(meeting, recipients) {
  try {
    logger.info(`Sending meeting reminders for meeting: ${meeting.id}`);
    
    const results = [];
    
    for (const recipient of recipients) {
      const subject = `Reminder: Committee Meeting - ${meeting.title}`;
      
      // Format date and time
      const meetingDate = new Date(meeting.date).toLocaleDateString('en-ZA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Generate location string
      let location = '';
      if (meeting.location === 'virtual') {
        location = `Virtual Meeting: ${meeting.meetingLink || 'Link to be provided'}`;
      } else if (meeting.location === 'physical' && meeting.physicalAddress) {
        const addr = meeting.physicalAddress;
        location = `${addr.street}, ${addr.city}, ${addr.province}, ${addr.country}`;
      } else if (meeting.location === 'hybrid') {
        const physical = meeting.physicalAddress ? 
          `${meeting.physicalAddress.street}, ${meeting.physicalAddress.city}` : 
          'Physical location to be confirmed';
        location = `Hybrid Meeting: ${physical} and Virtual (${meeting.meetingLink || 'Link to be provided'})`;
      }
      
      // Generate email body
      const body = `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #ff9800; color: white; padding: 10px 20px; }
            .content { padding: 20px; border: 1px solid #ddd; }
            .footer { font-size: 12px; color: #666; margin-top: 20px; }
            .button { display: inline-block; background-color: #1976d2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Committee Meeting Reminder</h2>
            </div>
            <div class="content">
              <p>Dear ${recipient.name || recipient.email},</p>
              
              <p>This is a reminder for the upcoming committee meeting:</p>
              
              <h3>${meeting.title}</h3>
              <p><strong>Date:</strong> ${meetingDate}</p>
              <p><strong>Time:</strong> ${meeting.startTime} - ${meeting.endTime}</p>
              <p><strong>Location:</strong> ${location}</p>
              
              ${meeting.description ? `<p><strong>Description:</strong> ${meeting.description}</p>` : ''}
              
              <p>Please ensure you have reviewed all relevant materials before the meeting.</p>
              
              <p>If you are unable to attend, please notify the meeting organizer as soon as possible.</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
        </html>
      `;
      
      // Send the email
      try {
        await sendEmail(recipient.email, subject, body, {
          isHtml: true,
          attachments: [
            {
              filename: 'meeting.ics',
              content: generateICalendarAttachment(meeting)
            }
          ]
        });
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'sent',
          sentDate: new Date()
        });
      } catch (error) {
        logger.error(`Error sending reminder to ${recipient.email}:`, error);
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error sending meeting reminders:', error);
    throw new Error('Failed to send meeting reminders');
  }
}

/**
 * Send meeting minutes to attendees
 * @param {Object} meeting Committee meeting data
 * @param {Array} recipients List of recipients with email addresses
 * @returns {Promise<Array>} Array of notification results
 */
async function sendMeetingMinutes(meeting, recipients) {
  try {
    logger.info(`Sending meeting minutes for meeting: ${meeting.id}`);
    
    if (!meeting.minutes) {
      throw new Error('Meeting minutes not available');
    }
    
    const results = [];
    
    for (const recipient of recipients) {
      const subject = `Meeting Minutes: ${meeting.title}`;
      
      // Format date and time
      const meetingDate = new Date(meeting.date).toLocaleDateString('en-ZA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Generate email body
      const body = `
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2e7d32; color: white; padding: 10px 20px; }
            .content { padding: 20px; border: 1px solid #ddd; }
            .footer { font-size: 12px; color: #666; margin-top: 20px; }
            .minutes { white-space: pre-wrap; background-color: #f5f5f5; padding: 15px; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Meeting Minutes</h2>
            </div>
            <div class="content">
              <p>Dear ${recipient.name || recipient.email},</p>
              
              <p>Please find attached the minutes from the recent committee meeting:</p>
              
              <h3>${meeting.title}</h3>
              <p><strong>Date:</strong> ${meetingDate}</p>
              <p><strong>Time:</strong> ${meeting.startTime} - ${meeting.endTime}</p>
              
              <p>The minutes are also included below for your convenience:</p>
              
              <div class="minutes">
                ${meeting.minutes.replace(/\n/g, '<br>')}
              </div>
              
              <p>If you have any questions or notice any discrepancies in the minutes, please contact the meeting organizer.</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
        </html>
      `;
      
      // Send the email
      try {
        await sendEmail(recipient.email, subject, body, {
          isHtml: true,
          attachments: [
            {
              filename: `${meeting.title.replace(/\s+/g, '_')}_Minutes.txt`,
              content: meeting.minutes
            }
          ]
        });
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'sent',
          sentDate: new Date()
        });
      } catch (error) {
        logger.error(`Error sending minutes to ${recipient.email}:`, error);
        
        results.push({
          recipientId: recipient.id,
          email: recipient.email,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error sending meeting minutes:', error);
    throw new Error('Failed to send meeting minutes');
  }
}

/**
 * Generate iCalendar attachment for meeting invitations
 * @param {Object} meeting Committee meeting data
 * @returns {string} iCalendar content
 */
function generateICalendarAttachment(meeting) {
  const startDate = new Date(meeting.date);
  const [startHours, startMinutes] = meeting.startTime.split(':').map(Number);
  startDate.setHours(startHours, startMinutes, 0, 0);
  
  const endDate = new Date(meeting.date);
  const [endHours, endMinutes] = meeting.endTime.split(':').map(Number);
  endDate.setHours(endHours, endMinutes, 0, 0);
  
  // Format dates for iCalendar
  const formatDate = (date) => {
    return date.toISOString().replace(/-|:|\.\d+/g, '');
  };
  
  const startDateStr = formatDate(startDate);
  const endDateStr = formatDate(endDate);
  const now = formatDate(new Date());
  
  // Generate a unique ID for the event
  const eventUid = `meeting-${meeting.id}@funding-screening-app.com`;
  
  // Generate location string
  let location = '';
  if (meeting.location === 'virtual') {
    location = `Virtual Meeting: ${meeting.meetingLink || 'Link to be provided'}`;
  } else if (meeting.location === 'physical' && meeting.physicalAddress) {
    const addr = meeting.physicalAddress;
    location = `${addr.street}, ${addr.city}, ${addr.province}, ${addr.country}`;
  } else if (meeting.location === 'hybrid') {
    const physical = meeting.physicalAddress ? 
      `${meeting.physicalAddress.street}, ${meeting.physicalAddress.city}` : 
      'Physical location to be confirmed';
    location = `Hybrid Meeting: ${physical} and Virtual (${meeting.meetingLink || 'Link to be provided'})`;
  }
  
  // Generate description
  let description = meeting.description || '';
  if (meeting.agenda) {
    description += `\n\nAgenda:\n${meeting.agenda}`;
  }
  
  // Format description for iCalendar (escape special characters)
  description = description.replace(/\n/g, '\\n');
  
  // Generate iCalendar content
  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Funding Screening App//Committee Meeting//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:REQUEST',
    'BEGIN:VEVENT',
    `UID:${eventUid}`,
    `DTSTAMP:${now}`,
    `DTSTART:${startDateStr}`,
    `DTEND:${endDateStr}`,
    `SUMMARY:${meeting.title}`,
    `DESCRIPTION:${description}`,
    `LOCATION:${location}`,
    'STATUS:CONFIRMED',
    'SEQUENCE:0',
    'BEGIN:VALARM',
    'ACTION:DISPLAY',
    'DESCRIPTION:Reminder',
    'TRIGGER:-PT30M',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');
  
  return icsContent;
}

module.exports = {
  sendEmail,
  sendMeetingInvitations,
  sendMeetingReminders,
  sendMeetingMinutes
};