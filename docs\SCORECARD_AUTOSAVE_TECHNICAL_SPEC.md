# Technical Specification: Scorecard Auto-Save with Conflict Detection

## 1. Executive Summary

This document outlines the technical implementation of an auto-save feature for the scorecard creation system, including intermediate saves, version history, and conflict detection mechanisms. The solution enables users to save their work progressively without losing data, while handling concurrent edits gracefully.

## 2. Objectives

- **Primary Goal**: Prevent data loss during scorecard creation through automatic and manual intermediate saves
- **Secondary Goals**:
  - Enable version history and recovery
  - Handle concurrent edits with conflict detection
  - Provide clear save status feedback to users
  - Maintain data integrity across multiple save operations

## 3. System Architecture

### 3.1 High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │────▶│  Backend API    │────▶│  Database       │
│  - Auto-save    │     │  - Version Mgmt │     │  - Scorecards   │
│  - UI Controls  │     │  - Conflict Det │     │  - Versions     │
│  - Status Ind.  │     │  - Validation   │     │  - Metadata     │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### 3.2 Data Flow

1. User edits scorecard in frontend
2. Auto-save timer triggers after 30 seconds of inactivity
3. Frontend sends save request with current version number
4. Backend checks for conflicts
5. If no conflicts, saves new version
6. If conflicts exist, returns conflict data for resolution
7. Frontend updates save status indicator

## 4. Database Schema

### 4.1 Modified Scorecard Schema

```javascript
// Existing scorecard schema with additions
const scorecardSchema = {
  // ... existing fields ...
  
  // New fields for versioning
  isDraft: { type: Boolean, default: true },
  currentVersion: { type: Number, default: 1 },
  lastModifiedBy: { type: String },
  lastModifiedAt: { type: Date, default: Date.now },
  lockedBy: { type: String, default: null },
  lockedAt: { type: Date, default: null },
  finalizedAt: { type: Date, default: null },
  finalizedBy: { type: String, default: null }
};
```

### 4.2 New Scorecard Version Schema

```javascript
const scorecardVersionSchema = {
  scorecardId: { type: ObjectId, ref: 'Scorecard', required: true },
  versionNumber: { type: Number, required: true },
  data: {
    name: String,
    description: String,
    criteria: [{
      id: String,
      name: String,
      description: String,
      weight: Number,
      maxScore: Number,
      score: Number,
      comments: String
    }],
    totalScore: Number,
    maxPossibleScore: Number
  },
  metadata: {
    isAutoSave: { type: Boolean, default: false },
    isFinalSave: { type: Boolean, default: false },
    saveType: { type: String, enum: ['auto', 'manual', 'final'], default: 'manual' },
    conflictResolved: { type: Boolean, default: false },
    changesSummary: String
  },
  createdBy: { type: String, required: true },
  createdAt: { type: Date, default: Date.now }
};
```

### 4.3 Indexes

```javascript
// Performance optimization indexes
scorecardVersionSchema.index({ scorecardId: 1, versionNumber: -1 });
scorecardVersionSchema.index({ scorecardId: 1, createdAt: -1 });
scorecardSchema.index({ applicationId: 1, isDraft: 1 });
```

## 5. API Endpoints

### 5.1 Save Draft Endpoint

```typescript
POST /api/scorecards/:id/save-draft
```

**Request Body:**
```json
{
  "data": {
    "name": "string",
    "criteria": [...],
    "totalScore": "number"
  },
  "lastKnownVersion": "number",
  "saveType": "auto|manual"
}
```

**Response (Success):**
```json
{
  "success": true,
  "version": 2,
  "savedAt": "2024-01-20T10:30:00Z",
  "message": "Draft saved successfully"
}
```

**Response (Conflict):**
```json
{
  "success": false,
  "error": "CONFLICT_DETECTED",
  "conflicts": [{
    "field": "criteria[0].score",
    "yourValue": 8,
    "currentValue": 7,
    "baseValue": 6
  }],
  "currentVersion": 3,
  "yourVersion": 2
}
```

### 5.2 Finalize Scorecard Endpoint

```typescript
POST /api/scorecards/:id/finalize
```

**Request Body:**
```json
{
  "data": {
    "name": "string",
    "criteria": [...],
    "totalScore": "number"
  },
  "lastKnownVersion": "number"
}
```

**Response:**
```json
{
  "success": true,
  "scorecardId": "string",
  "finalVersion": 5,
  "finalizedAt": "2024-01-20T11:00:00Z"
}
```

### 5.3 Get Version History Endpoint

```typescript
GET /api/scorecards/:id/versions
```

**Query Parameters:**
- `limit`: Number of versions to return (default: 10)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "versions": [{
    "versionNumber": 3,
    "createdBy": "<EMAIL>",
    "createdAt": "2024-01-20T10:30:00Z",
    "saveType": "auto",
    "changesSummary": "Updated criteria scores"
  }],
  "total": 15,
  "currentVersion": 3
}
```

### 5.4 Restore Version Endpoint

```typescript
POST /api/scorecards/:id/restore/:versionNumber
```

**Response:**
```json
{
  "success": true,
  "restoredVersion": 2,
  "newVersion": 4,
  "message": "Version 2 restored as version 4"
}
```

## 6. Frontend Implementation

### 6.1 Auto-Save Service

```typescript
// services/scorecard-autosave.service.ts
export class ScorecardAutoSaveService {
  private autoSaveTimer: any;
  private readonly AUTO_SAVE_DELAY = 30000; // 30 seconds
  private readonly DEBOUNCE_TIME = 2000; // 2 seconds
  
  startAutoSave(scorecardId: string, getDataFn: () => any): void {
    this.stopAutoSave();
    
    this.autoSaveTimer = setTimeout(() => {
      this.performAutoSave(scorecardId, getDataFn());
    }, this.AUTO_SAVE_DELAY);
  }
  
  stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }
  
  private async performAutoSave(scorecardId: string, data: any): Promise<void> {
    try {
      const response = await this.http.post(
        `/api/scorecards/${scorecardId}/save-draft`,
        {
          data,
          lastKnownVersion: this.currentVersion,
          saveType: 'auto'
        }
      ).toPromise();
      
      if (response.success) {
        this.updateSaveStatus('saved');
        this.currentVersion = response.version;
      } else if (response.error === 'CONFLICT_DETECTED') {
        this.handleConflict(response.conflicts);
      }
    } catch (error) {
      this.updateSaveStatus('error');
    }
  }
}
```

### 6.2 UI Component Updates

```typescript
// Modified scorecard.component.ts additions
export class ScorecardComponent implements OnInit, OnDestroy {
  // New properties
  saveStatus: SaveStatus = SaveStatus.SAVED;
  lastSavedTime: Date | null = null;
  currentVersion: number = 1;
  isDraft: boolean = true;
  
  // Save status enum
  enum SaveStatus {
    SAVED = 'saved',
    SAVING = 'saving',
    MODIFIED = 'modified',
    CONFLICT = 'conflict',
    ERROR = 'error'
  }
  
  ngOnInit(): void {
    // ... existing initialization ...
    this.initializeAutoSave();
  }
  
  ngOnDestroy(): void {
    this.autoSaveService.stopAutoSave();
  }
  
  onFormChange(): void {
    this.saveStatus = SaveStatus.MODIFIED;
    this.autoSaveService.startAutoSave(
      this.scorecard.id,
      () => this.getFormData()
    );
  }
  
  async saveDraft(): Promise<void> {
    this.saveStatus = SaveStatus.SAVING;
    try {
      const response = await this.scorecardService.saveDraft(
        this.scorecard.id,
        this.getFormData(),
        this.currentVersion
      );
      
      if (response.success) {
        this.saveStatus = SaveStatus.SAVED;
        this.lastSavedTime = new Date();
        this.currentVersion = response.version;
      }
    } catch (error) {
      this.saveStatus = SaveStatus.ERROR;
    }
  }
  
  async finalizeScorecard(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }
    
    try {
      const response = await this.scorecardService.finalize(
        this.scorecard.id,
        this.getFormData(),
        this.currentVersion
      );
      
      if (response.success) {
        this.isDraft = false;
        this.saveStatus = SaveStatus.SAVED;
        this.notificationService.showSuccess('Scorecard finalized successfully');
        this.saved.emit(this.scorecard);
      }
    } catch (error) {
      this.notificationService.showError('Failed to finalize scorecard');
    }
  }
}
```

### 6.3 UI Template Updates

```html
<!-- scorecard.component.html additions -->
<div class="scorecard-toolbar">
  <div class="save-controls">
    <button mat-button 
            (click)="saveDraft()" 
            [disabled]="saveStatus === 'saving' || saveStatus === 'saved'"
            class="save-draft-btn">
      <mat-icon>save</mat-icon>
      Save Draft
    </button>
    
    <button mat-raised-button 
            color="primary"
            (click)="finalizeScorecard()"
            [disabled]="saveStatus === 'saving' || !scorecardForm.valid"
            class="finalize-btn">
      <mat-icon>check_circle</mat-icon>
      Save & Finalize
    </button>
    
    <button mat-button
            (click)="openVersionHistory()"
            class="version-history-btn">
      <mat-icon>history</mat-icon>
      Version History
    </button>
  </div>
  
  <div class="save-status-indicator">
    <mat-icon [ngClass]="'status-' + saveStatus">
      {{ getSaveStatusIcon() }}
    </mat-icon>
    <span class="status-text">{{ getSaveStatusText() }}</span>
    <span class="last-saved" *ngIf="lastSavedTime">
      Last saved: {{ getTimeSinceLastSave() }}
    </span>
  </div>
</div>

<!-- Conflict Resolution Dialog -->
<ng-template #conflictDialog>
  <h2 mat-dialog-title>Resolve Conflicts</h2>
  <mat-dialog-content>
    <p>Another user has made changes to this scorecard. Please resolve the conflicts:</p>
    
    <div *ngFor="let conflict of conflicts" class="conflict-item">
      <h4>{{ conflict.field }}</h4>
      <div class="conflict-values">
        <div class="your-value">
          <label>Your Value:</label>
          <span>{{ conflict.yourValue }}</span>
        </div>
        <div class="current-value">
          <label>Current Value:</label>
          <span>{{ conflict.currentValue }}</span>
        </div>
      </div>
      <mat-radio-group [(ngModel)]="conflict.resolution">
        <mat-radio-button value="yours">Keep your changes</mat-radio-button>
        <mat-radio-button value="theirs">Keep current changes</mat-radio-button>
      </mat-radio-group>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions>
    <button mat-button (click)="cancelConflictResolution()">Cancel</button>
    <button mat-raised-button color="primary" (click)="resolveConflicts()">
      Resolve & Save
    </button>
  </mat-dialog-actions>
</ng-template>
```

## 7. Conflict Detection Algorithm

### 7.1 Backend Implementation

```javascript
// services/conflict-detection.service.js
class ConflictDetectionService {
  detectConflicts(currentData, incomingData, baseVersion) {
    const conflicts = [];
    
    // Compare criteria
    for (let i = 0; i < incomingData.criteria.length; i++) {
      const current = currentData.criteria[i];
      const incoming = incomingData.criteria[i];
      const base = baseVersion.data.criteria[i];
      
      // Check each field for conflicts
      ['score', 'weight', 'comments'].forEach(field => {
        if (current[field] !== base[field] && 
            incoming[field] !== base[field] && 
            current[field] !== incoming[field]) {
          conflicts.push({
            field: `criteria[${i}].${field}`,
            yourValue: incoming[field],
            currentValue: current[field],
            baseValue: base[field]
          });
        }
      });
    }
    
    return conflicts;
  }
  
  resolveConflicts(conflicts, resolutions, currentData, incomingData) {
    const resolvedData = { ...incomingData };
    
    resolutions.forEach((resolution, index) => {
      const conflict = conflicts[index];
      const fieldPath = conflict.field.split('.');
      
      if (resolution === 'theirs') {
        // Apply current value
        this.setNestedValue(resolvedData, fieldPath, conflict.currentValue);
      }
      // If 'yours', keep incoming value
    });
    
    return resolvedData;
  }
}
```

## 8. Implementation Phases

### Phase 1: Basic Auto-Save (Week 1-2)
- [ ] Update scorecard model with draft status
- [ ] Implement save-draft endpoint
- [ ] Add auto-save timer to frontend
- [ ] Create save status indicator
- [ ] Add manual save button

### Phase 2: Version History (Week 3-4)
- [ ] Create version schema and model
- [ ] Implement version storage on saves
- [ ] Create version history endpoint
- [ ] Build version history UI component
- [ ] Add restore functionality

### Phase 3: Conflict Detection (Week 5-6)
- [ ] Implement optimistic locking
- [ ] Create conflict detection service
- [ ] Build conflict resolution UI
- [ ] Add comprehensive testing
- [ ] Deploy and monitor

## 9. Testing Strategy

### 9.1 Unit Tests
- Auto-save timer functionality
- Conflict detection algorithm
- Version management service
- Save status state management

### 9.2 Integration Tests
- API endpoint functionality
- Database operations
- Frontend-backend communication
- Conflict resolution flow

### 9.3 E2E Tests
- Complete auto-save workflow
- Concurrent edit scenarios
- Version history and restore
- Network failure handling

### 9.4 Test Scenarios
1. Single user auto-save
2. Manual save overriding auto-save
3. Concurrent edits by two users
4. Network interruption during save
5. Version restore with conflicts
6. Finalize with pending changes

## 10. Performance Considerations

### 10.1 Optimization Strategies
- Debounce auto-save triggers
- Compress version data
- Limit version history retention
- Index database queries
- Cache current version

### 10.2 Scalability
- Version cleanup job (keep last 50 versions)
- Archival strategy for old versions
- Database partitioning by scorecard ID
- CDN for static assets

## 11. Security Considerations

### 11.1 Access Control
- Verify user permissions before save
- Audit trail for all saves
- Prevent unauthorized version access
- Secure conflict resolution

### 11.2 Data Integrity
- Transaction-based saves
- Checksum validation
- Backup strategy
- Recovery procedures

## 12. Monitoring and Analytics

### 12.1 Metrics to Track
- Auto-save success rate
- Average save time
- Conflict frequency
- Version restore usage
- Error rates by type

### 12.2 Alerts
- High conflict rate
- Save failures
- Performance degradation
- Storage threshold

## 13. User Documentation

### 13.1 Feature Overview
- How auto-save works
- Understanding save status
- Resolving conflicts
- Using version history

### 13.2 Best Practices
- When to manual save
- Handling conflicts
- Version management
- Collaborative editing tips

## 14. Rollback Plan

In case of issues:
1. Disable auto-save feature flag
2. Revert to manual save only
3. Preserve version history
4. Communicate to users
5. Fix and re-deploy

## 15. Success Criteria

- 99% auto-save success rate
- < 2 second save time
- < 5% conflict rate
- Zero data loss incidents
- Positive user feedback

---

**Document Version**: 1.0  
**Last Updated**: January 2024  
**Author**: Technical Architecture Team  
**Status**: Ready for Review