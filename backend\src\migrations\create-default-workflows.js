const mongoose = require('mongoose');
const Workflow = require('../models/workflow');
const User = require('../models/user');

// Define the enums directly in the migration
const ApplicationMainStage = {
  ONBOARDING: 'Onboarding',
  BUSINESS_CASE_REVIEW: 'Business Case Review',
  DUE_DILIGENCE: 'Due Diligence',
  ASSESSMENT_REPORT: 'Assessment Report',
  APPLICATION_APPROVAL: 'Application Approval'
};

const ApplicationSubStage = {
  BENEFICIARY_REGISTRATION: 'BENEFICIARY_REGISTRATION',
  PRE_SCREENING: 'PRE_SCREENING',
  DOCUMENT_COLLECTION: 'DOCUMENT_COLLECTION',
  DESKTOP_ANALYSIS: 'DESKTOP_ANALYSIS',
  DATA_VALIDATION: 'DATA_VALIDATION',
  SME_INTERVIEW: 'SME_INTERVIEW',
  SITE_VISIT: 'SITE_VISIT',
  REPORT_COMPLETION: 'REPORT_COMPLETION',
  REPORT_QUALITY_CHECK: 'REPORT_QUALITY_CHECK',
  REPORT_REVIEW: 'REPORT_REVIEW',
  CORPORATE_APPROVAL_1: 'CORPORATE_APPROVAL_1',
  CORPORATE_APPROVAL_2: 'CORPORATE_APPROVAL_2',
  CORPORATE_APPROVAL_3: 'CORPORATE_APPROVAL_3',
  CORPORATE_APPROVAL_4: 'CORPORATE_APPROVAL_4'
};

async function createDefaultWorkflows() {
  try {
    // Find an admin user to set as creator, or use a system user
    let adminUser = await User.findOne({ role: 'admin' });
    
    if (!adminUser) {
      // Try to find any user, or create a system user
      adminUser = await User.findOne();
      
      if (!adminUser) {
        console.log('No users found. Creating a system user for workflow creation.');
        adminUser = await User.create({
          name: 'System',
          email: '<EMAIL>',
          password: 'system-password-not-for-login',
          role: 'admin',
          isActive: false
        });
      }
    }

    // Check if workflows already exist
    const existingWorkflows = await Workflow.countDocuments();
    if (existingWorkflows > 0) {
      console.log('Workflows already exist. Skipping creation.');
      return;
    }

    const defaultWorkflows = [
      {
        name: 'Standard Application Review',
        description: 'Standard sequential workflow for most funding applications',
        type: 'Sequential',
        status: 'Active',
        stages: [
          {
            mainStage: ApplicationMainStage.ONBOARDING,
            subStages: [
              { name: ApplicationSubStage.BENEFICIARY_REGISTRATION, required: true, order: 1 },
              { name: ApplicationSubStage.PRE_SCREENING, required: true, order: 2 }
            ]
          },
          {
            mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
            subStages: [
              { name: ApplicationSubStage.DOCUMENT_COLLECTION, required: true, order: 3 },
              { name: ApplicationSubStage.DESKTOP_ANALYSIS, required: true, order: 4 }
            ]
          },
          {
            mainStage: ApplicationMainStage.DUE_DILIGENCE,
            subStages: [
              { name: ApplicationSubStage.DATA_VALIDATION, required: true, order: 5 },
              { name: ApplicationSubStage.SME_INTERVIEW, required: true, order: 6 },
              { name: ApplicationSubStage.SITE_VISIT, required: true, order: 7 }
            ]
          },
          {
            mainStage: ApplicationMainStage.ASSESSMENT_REPORT,
            subStages: [
              { name: ApplicationSubStage.REPORT_COMPLETION, required: true, order: 8 },
              { name: ApplicationSubStage.REPORT_QUALITY_CHECK, required: true, order: 9 },
              { name: ApplicationSubStage.REPORT_REVIEW, required: true, order: 10 }
            ]
          },
          {
            mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
            subStages: [
              { name: ApplicationSubStage.CORPORATE_APPROVAL_1, required: true, order: 11 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_2, required: true, order: 12 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_3, required: true, order: 13 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_4, required: true, order: 14 }
            ]
          }
        ],
        automationRules: [
          {
            name: 'Auto-assign Document Reviewer',
            description: 'Automatically assign applications to available document reviewers',
            trigger: 'StageComplete',
            triggerDetails: { stage: 'Onboarding-PRE_SCREENING' },
            actions: [{
              type: 'AssignReviewer',
              details: { role: 'document_reviewer', strategy: 'least_busy' }
            }],
            enabled: true
          },
          {
            name: 'Document Upload Reminder',
            description: 'Send reminder if documents not uploaded within 3 days',
            trigger: 'TimeElapsed',
            triggerDetails: { stage: 'Business Case Review-DOCUMENT_COLLECTION', days: 3 },
            actions: [{
              type: 'SendNotification',
              details: { template: 'document_reminder', recipient: 'applicant' }
            }],
            enabled: true
          }
        ],
        priority: 1,
        createdBy: adminUser._id,
        updatedBy: adminUser._id
      },
      {
        name: 'Fast Track Approval',
        description: 'Expedited workflow for high-priority or pre-approved applications',
        type: 'Parallel',
        status: 'Active',
        stages: [
          {
            mainStage: ApplicationMainStage.ONBOARDING,
            subStages: [
              { name: ApplicationSubStage.BENEFICIARY_REGISTRATION, required: true, order: 1 },
              { name: ApplicationSubStage.PRE_SCREENING, required: true, order: 2 }
            ]
          },
          {
            mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
            subStages: [
              { name: ApplicationSubStage.DOCUMENT_COLLECTION, required: true, order: 3, parallelGroup: 1 },
              { name: ApplicationSubStage.DESKTOP_ANALYSIS, required: true, order: 3, parallelGroup: 1 }
            ]
          },
          {
            mainStage: ApplicationMainStage.DUE_DILIGENCE,
            subStages: [
              { name: ApplicationSubStage.DATA_VALIDATION, required: false, order: 4 },
              { name: ApplicationSubStage.SME_INTERVIEW, required: false, order: 5 },
              { name: ApplicationSubStage.SITE_VISIT, required: false, order: 6 }
            ]
          },
          {
            mainStage: ApplicationMainStage.ASSESSMENT_REPORT,
            subStages: [
              { name: ApplicationSubStage.REPORT_COMPLETION, required: true, order: 7 },
              { name: ApplicationSubStage.REPORT_REVIEW, required: true, order: 8 }
            ]
          },
          {
            mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
            subStages: [
              { name: ApplicationSubStage.CORPORATE_APPROVAL_2, required: true, order: 9 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_4, required: true, order: 10 }
            ]
          }
        ],
        automationRules: [
          {
            name: 'Fast Track Notification',
            description: 'Notify reviewers of fast-track application',
            trigger: 'StageComplete',
            triggerDetails: { stage: 'Onboarding-PRE_SCREENING' },
            actions: [{
              type: 'SendNotification',
              details: { template: 'fast_track_alert', recipient: 'all_reviewers' }
            }],
            enabled: true
          }
        ],
        priority: 2,
        createdBy: adminUser._id,
        updatedBy: adminUser._id
      },
      {
        name: 'Corporate Sponsor Review',
        description: 'Workflow for applications with corporate sponsorship',
        type: 'Conditional',
        status: 'Active',
        stages: [
          {
            mainStage: ApplicationMainStage.ONBOARDING,
            subStages: [
              { name: ApplicationSubStage.BENEFICIARY_REGISTRATION, required: true, order: 1 },
              { name: ApplicationSubStage.PRE_SCREENING, required: true, order: 2 }
            ]
          },
          {
            mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
            subStages: [
              { name: ApplicationSubStage.DOCUMENT_COLLECTION, required: true, order: 3 },
              {
                name: ApplicationSubStage.DESKTOP_ANALYSIS,
                required: true,
                order: 4,
                conditions: [{ field: 'sponsorshipAmount', operator: 'greaterThan', value: 100000 }]
              }
            ]
          },
          {
            mainStage: ApplicationMainStage.DUE_DILIGENCE,
            subStages: [
              { name: ApplicationSubStage.DATA_VALIDATION, required: true, order: 5 },
              { name: ApplicationSubStage.SME_INTERVIEW, required: true, order: 6 },
              {
                name: ApplicationSubStage.SITE_VISIT,
                required: true,
                order: 7,
                conditions: [{ field: 'businessType', operator: 'equals', value: 'manufacturing' }]
              }
            ]
          },
          {
            mainStage: ApplicationMainStage.ASSESSMENT_REPORT,
            subStages: [
              { name: ApplicationSubStage.REPORT_COMPLETION, required: true, order: 8 },
              { name: ApplicationSubStage.REPORT_QUALITY_CHECK, required: true, order: 9 },
              { name: ApplicationSubStage.REPORT_REVIEW, required: true, order: 10 }
            ]
          },
          {
            mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
            subStages: [
              { name: ApplicationSubStage.CORPORATE_APPROVAL_1, required: true, order: 11 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_2, required: true, order: 12 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_3, required: true, order: 13 },
              { name: ApplicationSubStage.CORPORATE_APPROVAL_4, required: true, order: 14 }
            ]
          }
        ],
        automationRules: [
          {
            name: 'Sponsor Notification',
            description: 'Notify corporate sponsor of application progress',
            trigger: 'StageComplete',
            triggerDetails: { stage: 'Assessment Report-REPORT_REVIEW' },
            actions: [{
              type: 'SendNotification',
              details: { template: 'sponsor_update', recipient: 'corporate_sponsor' }
            }],
            enabled: true
          }
        ],
        priority: 1,
        createdBy: adminUser._id,
        updatedBy: adminUser._id
      }
    ];

    // Create workflows
    for (const workflowData of defaultWorkflows) {
      const workflow = new Workflow(workflowData);
      await workflow.save();
      console.log(`Created workflow: ${workflow.name}`);
    }

    console.log('Default workflows created successfully');
  } catch (error) {
    console.error('Error creating default workflows:', error);
    throw error;
  }
}

module.exports = createDefaultWorkflows;

// Run if called directly
if (require.main === module) {
  const mongoose = require('mongoose');
  const path = require('path');
  require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app')
    .then(() => {
      console.log('Connected to MongoDB');
      return createDefaultWorkflows();
    })
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}