openapi: 3.0.3
info:
  title: Funding Screening App API
  description: |
    Comprehensive API documentation for the Funding Screening Application backend.
    This API provides endpoints for managing applications, users, corporate sponsors,
    funding programmes, and various administrative functions.
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3003/api/v1
    description: Development server
  - url: https://api.fundingscreening.com/api/v1
    description: Production server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/login endpoint

  schemas:
    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              example: "VALIDATION_ERROR"
            message:
              type: string
              example: "Validation failed"
            details:
              type: array
              items:
                type: string
            timestamp:
              type: string
              format: date-time
            path:
              type: string

    User:
      type: object
      properties:
        id:
          type: string
          example: "507f1f77bcf86cd799439011"
        username:
          type: string
          example: "john.doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        roles:
          type: array
          items:
            type: string
          example: ["admin", "manager"]
        permissions:
          type: array
          items:
            type: string
        organizationType:
          type: string
          enum: ["CorporateSponsor", "ServiceProvider", "Internal"]
        organizationId:
          type: string
        isActive:
          type: boolean
          default: true
        lastLogin:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Application:
      type: object
      properties:
        id:
          type: string
          example: "APP-2025-001"
        applicationNumber:
          type: string
          example: "APP-2025-001"
        currentMainStage:
          type: string
          enum: ["ONBOARDING", "PRE_SCREENING_PENDING", "PRE_SCREENING_COMPLETE", "DOCUMENT_COLLECTION", "DESKTOP_ANALYSIS", "DATA_VALIDATION", "SME_INTERVIEW_SCHEDULED", "SME_INTERVIEW_COMPLETED", "FINAL_REVIEW", "APPLICATION_APPROVAL", "APPROVED", "REJECTED", "WITHDRAWN", "ON_HOLD"]
        currentSubStage:
          type: string
        currentStageStatus:
          type: string
          enum: ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "ON_HOLD", "REJECTED"]
        programmeId:
          type: string
          example: "507f1f77bcf86cd799439011"
        corporateSponsorId:
          type: string
          example: "507f1f77bcf86cd799439011"
        personalInfo:
          type: object
          properties:
            firstName:
              type: string
            lastName:
              type: string
            email:
              type: string
              format: email
            phone:
              type: string
        businessInfo:
          type: object
          properties:
            legalName:
              type: string
            tradingName:
              type: string
            registrationNumber:
              type: string
            industry:
              type: string
        financialInfo:
          type: object
          properties:
            requestedAmount:
              type: number
            fundingAmount:
              type: number
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CorporateSponsor:
      type: object
      properties:
        _id:
          type: string
          example: "507f1f77bcf86cd799439011"
        name:
          type: string
          example: "Tech Innovation Corp"
        industry:
          type: string
          example: "Technology"
        status:
          type: string
          enum: ["active", "inactive", "pending"]
          default: "active"
        totalFunding:
          type: number
          example: 1000000
        description:
          type: string
        contactInfo:
          type: object
          properties:
            email:
              type: string
              format: email
            phone:
              type: string
            address:
              type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    FundingProgramme:
      type: object
      properties:
        _id:
          type: string
          example: "507f1f77bcf86cd799439011"
        name:
          type: string
          example: "Skills Development Programme"
        description:
          type: string
        corporateSponsorId:
          type: string
          example: "507f1f77bcf86cd799439011"
        status:
          type: string
          enum: ["active", "inactive", "draft"]
          default: "active"
        maxFundingAmount:
          type: number
          example: 50000
        minFundingAmount:
          type: number
          example: 1000
        applicationDeadline:
          type: string
          format: date
        eligibilityCriteria:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          example: "john.doe"
        password:
          type: string
          format: password
          example: "password123"

    LoginResponse:
      type: object
      properties:
        token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        user:
          $ref: '#/components/schemas/User'

    PaginatedResponse:
      type: object
      properties:
        total:
          type: integer
          example: 100
        page:
          type: integer
          example: 1
        totalPages:
          type: integer
          example: 10
        limit:
          type: integer
          example: 10

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check endpoint
      description: Returns the health status of the API
      security: []
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ok"
                  timestamp:
                    type: string
                    format: date-time
                  service:
                    type: string
                    example: "funding-screening-app-backend"
                  version:
                    type: string
                    example: "1.0.0"

  # Authentication Endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          description: Too many login attempts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/profile:
    get:
      tags:
        - Authentication
      summary: Get user profile
      description: Get the authenticated user's profile information
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Authentication
      summary: Update user profile
      description: Update the authenticated user's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                lastName:
                  type: string
                email:
                  type: string
                  format: email
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Profile updated successfully"
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/change-password:
    put:
      tags:
        - Authentication
      summary: Change password
      description: Change the authenticated user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - currentPassword
                - newPassword
              properties:
                currentPassword:
                  type: string
                  format: password
                newPassword:
                  type: string
                  format: password
                  minLength: 8
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Password changed successfully"
        '400':
          description: Validation error or incorrect current password
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Application Endpoints
  /applications:
    get:
      tags:
        - Applications
      summary: Get all applications
      description: Retrieve all applications with filtering and pagination
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 25
        - name: stage
          in: query
          description: Filter by application stage
          schema:
            type: string
        - name: status
          in: query
          description: Filter by application status
          schema:
            type: string
        - name: programmeId
          in: query
          description: Filter by funding programme ID
          schema:
            type: string
        - name: corporateSponsorId
          in: query
          description: Filter by corporate sponsor ID
          schema:
            type: string
        - name: searchText
          in: query
          description: Search text for filtering applications
          schema:
            type: string
        - name: startDate
          in: query
          description: Filter applications created after this date
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: Filter applications created before this date
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Applications retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Application'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Applications
      summary: Create new application
      description: Create a new funding application
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - programmeId
                - corporateSponsorId
              properties:
                programmeId:
                  type: string
                  example: "507f1f77bcf86cd799439011"
                corporateSponsorId:
                  type: string
                  example: "507f1f77bcf86cd799439011"
                personalInfo:
                  type: object
                  properties:
                    firstName:
                      type: string
                    lastName:
                      type: string
                    email:
                      type: string
                      format: email
                    phone:
                      type: string
                businessInfo:
                  type: object
                  properties:
                    legalName:
                      type: string
                    tradingName:
                      type: string
                    registrationNumber:
                      type: string
                    industry:
                      type: string
                financialInfo:
                  type: object
                  properties:
                    requestedAmount:
                      type: number
      responses:
        '201':
          description: Application created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Application created successfully"
                  data:
                    $ref: '#/components/schemas/Application'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /applications/filter-options:
    get:
      tags:
        - Applications
      summary: Get filter options
      description: Get available filter options for applications
      responses:
        '200':
          description: Filter options retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  stages:
                    type: array
                    items:
                      type: string
                  statuses:
                    type: array
                    items:
                      type: string
                  programmes:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        name:
                          type: string
                  corporateSponsors:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        name:
                          type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /applications/{id}:
    get:
      tags:
        - Applications
      summary: Get application by ID
      description: Retrieve a specific application by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Application retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Application'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Application not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Applications
      summary: Update application
      description: Update an existing application
      parameters:
        - name: id
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Application'
      responses:
        '200':
          description: Application updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Application updated successfully"
                  data:
                    $ref: '#/components/schemas/Application'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Application not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Applications
      summary: Delete application
      description: Delete an application
      parameters:
        - name: id
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Application deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Application deleted successfully"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Application not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Corporate Sponsors Endpoints
  /corporate-sponsors:
    get:
      tags:
        - Corporate Sponsors
      summary: Get all corporate sponsors
      description: Retrieve all corporate sponsors with pagination and filtering
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: ["active", "inactive", "pending"]
        - name: industry
          in: query
          description: Filter by industry
          schema:
            type: string
        - name: search
          in: query
          description: Text search
          schema:
            type: string
      responses:
        '200':
          description: Corporate sponsors retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/CorporateSponsor'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Corporate Sponsors
      summary: Create new corporate sponsor
      description: Create a new corporate sponsor
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - industry
              properties:
                name:
                  type: string
                  example: "Tech Innovation Corp"
                industry:
                  type: string
                  example: "Technology"
                description:
                  type: string
                totalFunding:
                  type: number
                  example: 1000000
                contactInfo:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                    phone:
                      type: string
                    address:
                      type: string
      responses:
        '201':
          description: Corporate sponsor created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Corporate sponsor created successfully"
                  data:
                    $ref: '#/components/schemas/CorporateSponsor'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /corporate-sponsors/active:
    get:
      tags:
        - Corporate Sponsors
      summary: Get active corporate sponsors
      description: Get all active corporate sponsors for filter dropdown
      responses:
        '200':
          description: Active corporate sponsors retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        name:
                          type: string
                        industry:
                          type: string
                        totalFunding:
                          type: number
                        description:
                          type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /corporate-sponsors/{id}:
    get:
      tags:
        - Corporate Sponsors
      summary: Get corporate sponsor by ID
      description: Retrieve a specific corporate sponsor by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Corporate sponsor ID
          schema:
            type: string
      responses:
        '200':
          description: Corporate sponsor retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/CorporateSponsor'
        '404':
          description: Corporate sponsor not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Corporate Sponsors
      summary: Update corporate sponsor
      description: Update an existing corporate sponsor
      parameters:
        - name: id
          in: path
          required: true
          description: Corporate sponsor ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CorporateSponsor'
      responses:
        '200':
          description: Corporate sponsor updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Corporate sponsor updated successfully"
                  data:
                    $ref: '#/components/schemas/CorporateSponsor'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Corporate sponsor not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Corporate Sponsors
      summary: Delete corporate sponsor
      description: Delete a corporate sponsor
      parameters:
        - name: id
          in: path
          required: true
          description: Corporate sponsor ID
          schema:
            type: string
      responses:
        '200':
          description: Corporate sponsor deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Corporate sponsor deleted successfully"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Corporate sponsor not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Funding Programmes Endpoints
  /funding-programmes:
    get:
      tags:
        - Funding Programmes
      summary: Get all funding programmes
      description: Retrieve all funding programmes with optional filtering
      parameters:
        - name: corporateSponsorId
          in: query
          description: Filter by corporate sponsor ID
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: ["active", "inactive", "draft"]
      responses:
        '200':
          description: Funding programmes retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FundingProgramme'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Funding Programmes
      summary: Create new funding programme
      description: Create a new funding programme
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - corporateSponsorId
              properties:
                name:
                  type: string
                  example: "Skills Development Programme"
                description:
                  type: string
                corporateSponsorId:
                  type: string
                  example: "507f1f77bcf86cd799439011"
                maxFundingAmount:
                  type: number
                  example: 50000
                minFundingAmount:
                  type: number
                  example: 1000
                applicationDeadline:
                  type: string
                  format: date
                eligibilityCriteria:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Funding programme created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Funding programme created successfully"
                  data:
                    $ref: '#/components/schemas/FundingProgramme'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Admin Endpoints
  /admin/users:
    get:
      tags:
        - Admin
      summary: Get all users
      description: Retrieve all users (admin only)
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /admin/users/{id}:
    get:
      tags:
        - Admin
      summary: Get user by ID
      description: Retrieve a specific user by ID (admin only)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Admin
      summary: Update user
      description: Update a user (admin only)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  minLength: 3
                email:
                  type: string
                  format: email
                firstName:
                  type: string
                lastName:
                  type: string
                roles:
                  type: array
                  items:
                    type: string
                isActive:
                  type: boolean
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User updated successfully"
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Admin
      summary: Delete user
      description: Delete a user (admin only)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User deleted successfully"
        '400':
          description: Cannot delete last admin
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Chat Endpoints
  /chat/rooms:
    get:
      tags:
        - Chat
      summary: Get user's chat rooms
      description: Retrieve all chat rooms for the authenticated user
      parameters:
        - name: includeArchived
          in: query
          description: Include archived rooms
          schema:
            type: boolean
            default: false
        - name: type
          in: query
          description: Filter by room type
          schema:
            type: string
      responses:
        '200':
          description: Chat rooms retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        name:
                          type: string
                        type:
                          type: string
                        participants:
                          type: array
                          items:
                            type: object
                        lastMessage:
                          type: object
                        createdAt:
                          type: string
                          format: date-time
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Chat
      summary: Create new chat room
      description: Create a new chat room
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - type
              properties:
                name:
                  type: string
                  example: "Application Discussion"
                type:
                  type: string
                  enum: ["direct", "group", "application", "support"]
                description:
                  type: string
                participants:
                  type: array
                  items:
                    type: string
                  description: Array of user IDs
                applicationId:
                  type: string
                  description: Associated application ID (for application type rooms)
      responses:
        '201':
          description: Chat room created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      _id:
                        type: string
                      name:
                        type: string
                      type:
                        type: string
                      participants:
                        type: array
                        items:
                          type: object
                      createdAt:
                        type: string
                        format: date-time
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /chat/rooms/{roomId}/messages:
    get:
      tags:
        - Chat
      summary: Get room messages
      description: Retrieve messages from a specific chat room
      parameters:
        - name: roomId
          in: path
          required: true
          description: Chat room ID
          schema:
            type: string
        - name: limit
          in: query
          description: Number of messages to retrieve
          schema:
            type: integer
            default: 50
        - name: before
          in: query
          description: Get messages before this message ID
          schema:
            type: string
        - name: after
          in: query
          description: Get messages after this message ID
          schema:
            type: string
        - name: includeDeleted
          in: query
          description: Include deleted messages
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Messages retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        content:
                          type: string
                        sender:
                          type: object
                          properties:
                            _id:
                              type: string
                            firstName:
                              type: string
                            lastName:
                              type: string
                        timestamp:
                          type: string
                          format: date-time
                        messageType:
                          type: string
                          enum: ["text", "file", "image", "system"]
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Access denied to this chat room
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Chat room not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Chat
      summary: Send message
      description: Send a message to a chat room
      parameters:
        - name: roomId
          in: path
          required: true
          description: Chat room ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content
              properties:
                content:
                  type: string
                  example: "Hello, how can I help you?"
                messageType:
                  type: string
                  enum: ["text", "file", "image"]
                  default: "text"
                attachments:
                  type: array
                  items:
                    type: object
                    properties:
                      filename:
                        type: string
                      url:
                        type: string
                      size:
                        type: integer
      responses:
        '201':
          description: Message sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      _id:
                        type: string
                      content:
                        type: string
                      sender:
                        type: object
                      timestamp:
                        type: string
                        format: date-time
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Access denied to this chat room
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Notification Endpoints
  /notifications:
    get:
      tags:
        - Notifications
      summary: Get user notifications
      description: Retrieve notifications for the authenticated user
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of notifications per page
          schema:
            type: integer
            default: 20
        - name: unreadOnly
          in: query
          description: Get only unread notifications
          schema:
            type: boolean
            default: false
        - name: type
          in: query
          description: Filter by notification type
          schema:
            type: string
      responses:
        '200':
          description: Notifications retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        title:
                          type: string
                        message:
                          type: string
                        type:
                          type: string
                          enum: ["info", "warning", "error", "success"]
                        isRead:
                          type: boolean
                        createdAt:
                          type: string
                          format: date-time
                        applicationId:
                          type: string
                        actionUrl:
                          type: string
                  total:
                    type: integer
                  unreadCount:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /notifications/{id}/read:
    put:
      tags:
        - Notifications
      summary: Mark notification as read
      description: Mark a specific notification as read
      parameters:
        - name: id
          in: path
          required: true
          description: Notification ID
          schema:
            type: string
      responses:
        '200':
          description: Notification marked as read
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Notification marked as read"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Notification not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /notifications/mark-all-read:
    put:
      tags:
        - Notifications
      summary: Mark all notifications as read
      description: Mark all notifications for the authenticated user as read
      responses:
        '200':
          description: All notifications marked as read
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "All notifications marked as read"
                  modifiedCount:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Reports and Analytics Endpoints
  /reports:
    get:
      tags:
        - Reports
      summary: Get available reports
      description: Retrieve list of available reports
      responses:
        '200':
          description: Reports retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        name:
                          type: string
                        description:
                          type: string
                        type:
                          type: string
                        parameters:
                          type: array
                          items:
                            type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /analytics:
    get:
      tags:
        - Analytics
      summary: Get dashboard analytics
      description: Retrieve analytics data for dashboards
      parameters:
        - name: dateRange
          in: query
          description: Date range for analytics
          schema:
            type: string
            enum: ["7d", "30d", "90d", "1y"]
            default: "30d"
        - name: programmeId
          in: query
          description: Filter by programme ID
          schema:
            type: string
        - name: corporateSponsorId
          in: query
          description: Filter by corporate sponsor ID
          schema:
            type: string
      responses:
        '200':
          description: Analytics data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      totalApplications:
                        type: integer
                      approvedApplications:
                        type: integer
                      rejectedApplications:
                        type: integer
                      pendingApplications:
                        type: integer
                      totalFunding:
                        type: number
                      averageProcessingTime:
                        type: number
                      applicationsByStage:
                        type: object
                      applicationsByMonth:
                        type: array
                        items:
                          type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Site Visits Endpoints
  /site-visits:
    get:
      tags:
        - Site Visits
      summary: Get all site visits
      description: Retrieve all site visits with pagination
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Site visits retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        title:
                          type: string
                        applicationId:
                          type: string
                        scheduledDate:
                          type: string
                          format: date-time
                        status:
                          type: string
                          enum: ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED"]
                        location:
                          type: object
                          properties:
                            address:
                              type: string
                        duration:
                          type: integer
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Site Visits
      summary: Create new site visit
      description: Create a new site visit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - applicationId
                - scheduledDate
              properties:
                title:
                  type: string
                  example: "Site Visit for Application ABC-123"
                applicationId:
                  type: string
                  example: "507f1f77bcf86cd799439011"
                scheduledDate:
                  type: string
                  format: date-time
                location:
                  type: object
                  properties:
                    address:
                      type: string
                duration:
                  type: integer
                  example: 120
                notes:
                  type: string
      responses:
        '201':
          description: Site visit created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /site-visits/scheduler:
    get:
      tags:
        - Site Visits
      summary: Get site visits for scheduler
      description: Get all site visits for the scheduler view
      responses:
        '200':
          description: Site visits retrieved for scheduler
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                  message:
                    type: string

  /site-visits/application/{applicationId}:
    get:
      tags:
        - Site Visits
      summary: Get site visits for application
      description: Get all site visits for a specific application
      parameters:
        - name: applicationId
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Site visits retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object

  /site-visits/{id}:
    get:
      tags:
        - Site Visits
      summary: Get site visit by ID
      description: Retrieve a specific site visit by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Site visit ID
          schema:
            type: string
      responses:
        '200':
          description: Site visit retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
        '404':
          description: Site visit not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Site Visits
      summary: Update site visit
      description: Update an existing site visit
      parameters:
        - name: id
          in: path
          required: true
          description: Site visit ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                scheduledDate:
                  type: string
                  format: date-time
                location:
                  type: object
                duration:
                  type: integer
                notes:
                  type: string
                status:
                  type: string
                  enum: ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED"]
      responses:
        '200':
          description: Site visit updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
        '404':
          description: Site visit not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Site Visits
      summary: Delete site visit
      description: Delete a site visit
      parameters:
        - name: id
          in: path
          required: true
          description: Site visit ID
          schema:
            type: string
      responses:
        '200':
          description: Site visit deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
        '404':
          description: Site visit not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /site-visits/{id}/complete:
    put:
      tags:
        - Site Visits
      summary: Complete site visit
      description: Mark a site visit as completed
      parameters:
        - name: id
          in: path
          required: true
          description: Site visit ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                notes:
                  type: string
                findings:
                  type: string
                recommendations:
                  type: string
      responses:
        '200':
          description: Site visit completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
        '404':
          description: Site visit not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Interviews Endpoints
  /interviews:
    get:
      tags:
        - Interviews
      summary: Get all interviews
      description: Retrieve all interviews with pagination
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Interviews retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        title:
                          type: string
                        applicationId:
                          type: string
                        scheduledDate:
                          type: string
                          format: date-time
                        status:
                          type: string
                          enum: ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED"]
                        interviewType:
                          type: string
                        duration:
                          type: integer
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Interviews
      summary: Create new interview
      description: Create a new interview
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - applicationId
                - scheduledDate
              properties:
                title:
                  type: string
                  example: "SME Interview for Application ABC-123"
                applicationId:
                  type: string
                  example: "507f1f77bcf86cd799439011"
                scheduledDate:
                  type: string
                  format: date-time
                interviewType:
                  type: string
                  example: "SME"
                duration:
                  type: integer
                  example: 60
                notes:
                  type: string
      responses:
        '201':
          description: Interview created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object

  /interviews/scheduler:
    get:
      tags:
        - Interviews
      summary: Get interviews for scheduler
      description: Get all interviews for the scheduler view
      responses:
        '200':
          description: Interviews retrieved for scheduler
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string

  /interviews/application/{applicationId}:
    get:
      tags:
        - Interviews
      summary: Get interviews for application
      description: Get all interviews for a specific application
      parameters:
        - name: applicationId
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Interviews retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object

  /interviews/{id}:
    get:
      tags:
        - Interviews
      summary: Get interview by ID
      description: Retrieve a specific interview by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Interview retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object

    put:
      tags:
        - Interviews
      summary: Update interview
      description: Update an existing interview
      parameters:
        - name: id
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                scheduledDate:
                  type: string
                  format: date-time
                interviewType:
                  type: string
                duration:
                  type: integer
                notes:
                  type: string
                status:
                  type: string
                  enum: ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED"]
      responses:
        '200':
          description: Interview updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object

    delete:
      tags:
        - Interviews
      summary: Delete interview
      description: Delete an interview
      parameters:
        - name: id
          in: path
          required: true
          description: Interview ID
          schema:
            type: string
      responses:
        '200':
          description: Interview deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string

  # Scheduler Endpoints
  /scheduler/events:
    get:
      tags:
        - Scheduler
      summary: Get scheduled events
      description: Get all scheduled events within a date range
      parameters:
        - name: startDate
          in: query
          required: true
          description: Start date for event range
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          required: true
          description: End date for event range
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Events retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    title:
                      type: string
                    start:
                      type: string
                      format: date-time
                    end:
                      type: string
                      format: date-time
                    type:
                      type: string
                      enum: ["interview", "siteVisit"]
                    applicationId:
                      type: string
                    status:
                      type: string
                    location:
                      type: string
        '400':
          description: Missing required parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /scheduler/upcoming:
    get:
      tags:
        - Scheduler
      summary: Get upcoming events
      description: Get upcoming events for the next specified days
      parameters:
        - name: days
          in: query
          description: Number of days to look ahead
          schema:
            type: integer
            default: 7
      responses:
        '200':
          description: Upcoming events retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object

  # Public Applications Endpoints (No Auth Required)
  /public-applications:
    get:
      tags:
        - Public Applications
      summary: Get public applications
      description: Retrieve applications without authentication (public endpoint)
      security: []
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 1000
        - name: stage
          in: query
          description: Filter by application stage (comma-separated for multiple)
          schema:
            type: string
        - name: status
          in: query
          description: Filter by application status
          schema:
            type: string
        - name: programmeId
          in: query
          description: Filter by programme ID
          schema:
            type: string
        - name: corporateSponsorId
          in: query
          description: Filter by corporate sponsor ID
          schema:
            type: string
      responses:
        '200':
          description: Public applications retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Application'

  /public-applications/filter-options:
    get:
      tags:
        - Public Applications
      summary: Get public filter options
      description: Get available filter options for public applications
      security: []
      responses:
        '200':
          description: Filter options retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  stages:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                        display:
                          type: string
                  statuses:
                    type: array
                    items:
                      type: object
                  programmes:
                    type: array
                    items:
                      type: object
                  corporateSponsors:
                    type: array
                    items:
                      type: object

  # Service Providers Endpoints
  /service-providers:
    get:
      tags:
        - Service Providers
      summary: Get all service providers
      description: Retrieve all service providers with filtering
      parameters:
        - name: search
          in: query
          description: Text search
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: ["active", "inactive"]
        - name: type
          in: query
          description: Filter by provider type
          schema:
            type: string
            enum: ["individual", "company"]
        - name: specialization
          in: query
          description: Filter by specialization
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items to return
          schema:
            type: integer
            default: 100
        - name: skip
          in: query
          description: Number of items to skip
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Service providers retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    _id:
                      type: string
                    name:
                      type: string
                    type:
                      type: string
                      enum: ["individual", "company"]
                    status:
                      type: string
                      enum: ["active", "inactive"]
                    specializations:
                      type: array
                      items:
                        type: string
                    contactInfo:
                      type: object
                    rating:
                      type: number
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Service Providers
      summary: Create new service provider
      description: Create a new service provider (admin/manager only)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - type
              properties:
                name:
                  type: string
                  example: "John Doe Consulting"
                type:
                  type: string
                  enum: ["individual", "company"]
                specializations:
                  type: array
                  items:
                    type: string
                contactInfo:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                    phone:
                      type: string
                    address:
                      type: string
      responses:
        '201':
          description: Service provider created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - Admin/Manager access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /service-providers/meta/specializations:
    get:
      tags:
        - Service Providers
      summary: Get all specializations
      description: Get all available specializations for filtering
      responses:
        '200':
          description: Specializations retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /service-providers/{id}:
    get:
      tags:
        - Service Providers
      summary: Get service provider by ID
      description: Retrieve a specific service provider by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Service provider ID
          schema:
            type: string
      responses:
        '200':
          description: Service provider retrieved successfully
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Service provider not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Service Providers
      summary: Update service provider
      description: Update an existing service provider (admin/manager only)
      parameters:
        - name: id
          in: path
          required: true
          description: Service provider ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                type:
                  type: string
                  enum: ["individual", "company"]
                specializations:
                  type: array
                  items:
                    type: string
                contactInfo:
                  type: object
                status:
                  type: string
                  enum: ["active", "inactive"]
      responses:
        '200':
          description: Service provider updated successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Service provider not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Service Providers
      summary: Delete service provider
      description: Delete a service provider (admin/manager only)
      parameters:
        - name: id
          in: path
          required: true
          description: Service provider ID
          schema:
            type: string
      responses:
        '200':
          description: Service provider deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: Service provider not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /service-providers/{id}/assignments:
    get:
      tags:
        - Service Providers
      summary: Get service provider assignments
      description: Get assignments for a specific service provider
      parameters:
        - name: id
          in: path
          required: true
          description: Service provider ID
          schema:
            type: string
      responses:
        '200':
          description: Assignments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  assignments:
                    type: array
                    items:
                      type: object
                  total:
                    type: integer

  # Scorecards Endpoints
  /scorecards:
    get:
      tags:
        - Scorecards
      summary: Get all scorecards
      description: Retrieve all scorecards
      responses:
        '200':
          description: Scorecards retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    _id:
                      type: string
                    applicationId:
                      type: string
                    stageId:
                      type: string
                    substageId:
                      type: string
                    name:
                      type: string
                    description:
                      type: string
                    criteria:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                          name:
                            type: string
                          description:
                            type: string
                          weight:
                            type: number
                          maxScore:
                            type: number
                          score:
                            type: number
                          comments:
                            type: string
                    totalScore:
                      type: number
                    maxPossibleScore:
                      type: number
                    status:
                      type: string
                      enum: ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "APPROVED", "REJECTED"]
                    completedBy:
                      type: string
                    completedAt:
                      type: string
                      format: date-time
                    createdAt:
                      type: string
                      format: date-time
                    updatedAt:
                      type: string
                      format: date-time
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Scorecards
      summary: Create new scorecard
      description: Create a new scorecard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - applicationId
                - stageId
                - substageId
                - name
              properties:
                applicationId:
                  type: string
                stageId:
                  type: string
                substageId:
                  type: string
                name:
                  type: string
                description:
                  type: string
                criteria:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      description:
                        type: string
                      weight:
                        type: number
                        minimum: 1
                        maximum: 100
                      maxScore:
                        type: number
                        minimum: 1
                        maximum: 10
                      score:
                        type: number
                        minimum: 0
                        maximum: 10
                      comments:
                        type: string
      responses:
        '201':
          description: Scorecard created successfully
          content:
            application/json:
              schema:
                type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /scorecards/application/{applicationId}:
    get:
      tags:
        - Scorecards
      summary: Get scorecards for application
      description: Get all scorecards for a specific application
      parameters:
        - name: applicationId
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Application scorecards retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /scorecards/{id}:
    get:
      tags:
        - Scorecards
      summary: Get scorecard by ID
      description: Retrieve a specific scorecard by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard ID
          schema:
            type: string
      responses:
        '200':
          description: Scorecard retrieved successfully
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Scorecard not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Scorecards
      summary: Update scorecard
      description: Update an existing scorecard
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                criteria:
                  type: array
                  items:
                    type: object
                status:
                  type: string
                  enum: ["NOT_STARTED", "IN_PROGRESS", "COMPLETED", "APPROVED", "REJECTED"]
      responses:
        '200':
          description: Scorecard updated successfully
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Scorecard not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Scorecards
      summary: Delete scorecard
      description: Delete a scorecard
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard ID
          schema:
            type: string
      responses:
        '200':
          description: Scorecard deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: Scorecard not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Scorecard Templates Endpoints
  /scorecard-templates:
    get:
      tags:
        - Scorecard Templates
      summary: Get all scorecard templates
      description: Retrieve all scorecard templates
      parameters:
        - name: programmeId
          in: query
          description: Filter by programme ID
          schema:
            type: string
        - name: stage
          in: query
          description: Filter by stage
          schema:
            type: string
        - name: isActive
          in: query
          description: Filter by active status
          schema:
            type: boolean
      responses:
        '200':
          description: Scorecard templates retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    _id:
                      type: string
                    name:
                      type: string
                    description:
                      type: string
                    stage:
                      type: string
                    substage:
                      type: string
                    criteria:
                      type: array
                      items:
                        type: object
                    isActive:
                      type: boolean
                    templateScope:
                      type: string
                      enum: ["global", "shared", "programme-specific"]
                    fundingProgrammes:
                      type: array
                      items:
                        type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      tags:
        - Scorecard Templates
      summary: Create new scorecard template
      description: Create a new scorecard template
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - stage
                - substage
              properties:
                name:
                  type: string
                description:
                  type: string
                stage:
                  type: string
                substage:
                  type: string
                criteria:
                  type: array
                  items:
                    type: object
                templateScope:
                  type: string
                  enum: ["global", "shared", "programme-specific"]
                fundingProgrammes:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Scorecard template created successfully
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /scorecard-templates/hierarchy:
    get:
      tags:
        - Scorecard Templates
      summary: Get hierarchical template structure
      description: Get templates organized by programme and stage hierarchy
      parameters:
        - name: programmeId
          in: query
          description: Filter by programme ID
          schema:
            type: string
      responses:
        '200':
          description: Template hierarchy retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  programmes:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        name:
                          type: string
                  stages:
                    type: object
                  templates:
                    type: object

  /scorecard-templates/{id}:
    get:
      tags:
        - Scorecard Templates
      summary: Get scorecard template by ID
      description: Retrieve a specific scorecard template by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard template ID
          schema:
            type: string
      responses:
        '200':
          description: Scorecard template retrieved successfully
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Scorecard template not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - Scorecard Templates
      summary: Update scorecard template
      description: Update an existing scorecard template
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard template ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                criteria:
                  type: array
                  items:
                    type: object
                isActive:
                  type: boolean
      responses:
        '200':
          description: Scorecard template updated successfully
          content:
            application/json:
              schema:
                type: object
        '404':
          description: Scorecard template not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - Scorecard Templates
      summary: Delete scorecard template
      description: Delete a scorecard template
      parameters:
        - name: id
          in: path
          required: true
          description: Scorecard template ID
          schema:
            type: string
      responses:
        '200':
          description: Scorecard template deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: Scorecard template not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Additional Application Endpoints
  /applications/{applicationId}/site-visits:
    get:
      tags:
        - Applications
      summary: Get site visits for application
      description: Get all site visits for a specific application
      parameters:
        - name: applicationId
          in: path
          required: true
          description: Application ID
          schema:
            type: string
      responses:
        '200':
          description: Site visits retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Reports Endpoints (Extended)
  /reports/templates:
    get:
      tags:
        - Reports
      summary: Get report templates
      description: Get available report templates
      parameters:
        - name: type
          in: query
          description: Filter by report type
          schema:
            type: string
            enum: ["status", "processing-time", "approval-rates", "funding-distribution", "regional-analysis", "custom"]
      responses:
        '200':
          description: Report templates retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    _id:
                      type: string
                    name:
                      type: string
                    type:
                      type: string
                    description:
                      type: string
                    isTemplate:
                      type: boolean
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /reports/{id}/generate:
    post:
      tags:
        - Reports
      summary: Generate report
      description: Generate a report based on template
      parameters:
        - name: id
          in: path
          required: true
          description: Report template ID
          schema:
            type: string
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                format:
                  type: string
                  enum: ["json", "pdf", "excel", "csv"]
                  default: "json"
                parameters:
                  type: object
      responses:
        '200':
          description: Report generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  metadata:
                    type: object
        '404':
          description: Report template not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Permission denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

tags:
  - name: Health
    description: Health check endpoints
  - name: Authentication
    description: User authentication and profile management
  - name: Applications
    description: Funding application management
  - name: Corporate Sponsors
    description: Corporate sponsor management
  - name: Funding Programmes
    description: Funding programme management
  - name: Admin
    description: Administrative functions (admin only)
  - name: Chat
    description: Chat and messaging system
  - name: Notifications
    description: User notifications
  - name: Reports
    description: Report generation and management
  - name: Analytics
    description: Analytics and dashboard data
  - name: Site Visits
    description: Site visit management and scheduling
  - name: Interviews
    description: Interview management and scheduling
  - name: Scheduler
    description: Event scheduling and calendar management
  - name: Service Providers
    description: Service provider management
  - name: Scorecards
    description: Scorecard and evaluation management
  - name: Scorecard Templates
    description: Scorecard template management
  - name: Workflows
    description: Workflow and process management
  - name: Loan Management
    description: Loan management and tracking
  - name: Stage Status
    description: Application stage status management
  - name: Public Applications
    description: Public application endpoints (no auth required)
