<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scorecard Management - Fixed Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 5px 0 0 0;
            opacity: 0.8;
        }

        .content {
            display: flex;
            min-height: 600px;
        }

        .sidebar {
            width: 300px;
            background: #ecf0f1;
            border-right: 1px solid #ddd;
            padding: 20px;
        }

        .main-content {
            flex: 1;
            padding: 20px;
        }

        .form-section {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group.full-width {
            flex: none;
            width: 100%;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group label.required::after {
            content: " *";
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-group select:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .template-library {
            margin-top: 30px;
        }

        .template-library h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }

        .template-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: white;
            transition: box-shadow 0.3s;
        }

        .template-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .template-card h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 12px;
            color: #7f8c8d;
        }

        .template-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .template-actions .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
        }

        .criteria-section {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .criteria-section h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }

        .criteria-placeholder {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .criteria-placeholder i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .search-box {
            margin-bottom: 15px;
        }

        .search-box input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .filter-section {
            margin-bottom: 20px;
        }

        .filter-section select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .stage-indicator {
            display: inline-block;
            padding: 2px 8px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Scorecard Management</h1>
            <p>Manage scorecard templates and evaluation criteria</p>
        </div>

        <div class="content">
            <div class="sidebar">
                <div class="search-box">
                    <input type="text" placeholder="Search templates..." id="searchInput">
                </div>

                <div class="filter-section">
                    <h4>Filter Templates</h4>
                    <select id="filterStage">
                        <option value="">All Stages</option>
                        <option value="ONBOARDING">Onboarding</option>
                        <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
                        <option value="DUE_DILIGENCE">Due Diligence</option>
                        <option value="ASSESSMENT_REPORT">Assessment Report</option>
                        <option value="APPLICATION_APPROVAL">Application Approval</option>
                    </select>
                    
                    <select id="filterScope">
                        <option value="">All Scopes</option>
                        <option value="programme-specific">Programme Specific</option>
                        <option value="shared">Shared</option>
                        <option value="global">Global</option>
                    </select>
                </div>

                <div class="template-library">
                    <h3>Template Library</h3>
                    <div id="templateList">
                        <!-- Templates will be loaded here -->
                        <div class="template-card">
                            <h4>Data Validation Scorecard</h4>
                            <div class="template-meta">
                                <span>Due Diligence</span>
                                <span class="badge badge-success">Active</span>
                            </div>
                            <p style="font-size: 12px; color: #666; margin: 8px 0;">5 criteria • Used 12 times</p>
                            <div class="template-actions">
                                <button class="btn btn-primary">Edit</button>
                                <button class="btn btn-secondary">Duplicate</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="form-section">
                    <h3>Create New Template</h3>
                    
                    <div class="alert alert-info">
                        <strong>Fixed Interface:</strong> Now showing separate Stage and Sub-Stage dropdowns instead of the single "Category" dropdown.
                    </div>

                    <form id="templateForm">
                        <div class="form-group full-width">
                            <label for="templateName" class="required">Template Name</label>
                            <input type="text" id="templateName" name="templateName" placeholder="Enter template name" required>
                        </div>

                        <!-- FIXED: Separate Stage and Sub-Stage dropdowns -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="mainStage" class="required">Stage</label>
                                <select id="mainStage" name="mainStage" required>
                                    <option value="">Select Stage</option>
                                    <option value="ONBOARDING">Onboarding</option>
                                    <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
                                    <option value="DUE_DILIGENCE">Due Diligence</option>
                                    <option value="ASSESSMENT_REPORT">Assessment Report</option>
                                    <option value="APPLICATION_APPROVAL">Application Approval</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="subStage" class="required">Sub-Stage</label>
                                <select id="subStage" name="subStage" required disabled>
                                    <option value="">Select Sub-Stage</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="templateScope">Template Scope</label>
                                <select id="templateScope" name="templateScope">
                                    <option value="programme-specific">Programme Specific</option>
                                    <option value="shared">Shared</option>
                                    <option value="global">Global</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="passingScore">Passing Score (%)</label>
                                <input type="number" id="passingScore" name="passingScore" min="0" max="100" value="70">
                            </div>
                        </div>

                        <div class="form-group full-width">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" placeholder="Describe the purpose and usage of this template"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="allowCustomCriteria" name="allowCustomCriteria" checked>
                                    Allow users to add custom criteria when using this template
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="requireAllCriteria" name="requireAllCriteria">
                                    Require all criteria to be scored
                                </label>
                            </div>
                        </div>

                        <div style="margin-top: 20px;">
                            <button type="submit" class="btn btn-success">Create Template</button>
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="criteria-section">
                    <h4>Evaluation Criteria <span id="criteriaCount">(0)</span></h4>
                    <div class="criteria-placeholder">
                        <i>📋</i>
                        <p>No criteria defined yet</p>
                        <button class="btn btn-primary" onclick="addCriterion()">Add First Criterion</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Stage to Sub-Stage mapping
        const stageSubStageMap = {
            'ONBOARDING': [
                { value: 'beneficiary-registration', label: 'Beneficiary Registration' },
                { value: 'pre-screening', label: 'Pre-Screening' }
            ],
            'BUSINESS_CASE_REVIEW': [
                { value: 'document-collection', label: 'Document Collection' },
                { value: 'desktop-analysis', label: 'Desktop Analysis' },
                { value: 'data-validation', label: 'Data Validation' }
            ],
            'DUE_DILIGENCE': [
                { value: 'sme-interview', label: 'SME Interview' },
                { value: 'site-visit', label: 'Site Visit' }
            ],
            'ASSESSMENT_REPORT': [
                { value: 'report-completion', label: 'Report Completion' },
                { value: 'report-quality-check', label: 'Report Quality Check' },
                { value: 'report-review', label: 'Report Review' }
            ],
            'APPLICATION_APPROVAL': [
                { value: 'committee-review', label: 'Committee Review' },
                { value: 'final-decision', label: 'Final Decision' },
                { value: 'approval-documentation', label: 'Approval Documentation' },
                { value: 'corporate-approval-1', label: 'Corporate Approval Level 1' },
                { value: 'corporate-approval-2', label: 'Corporate Approval Level 2' },
                { value: 'corporate-approval-3', label: 'Corporate Approval Level 3' },
                { value: 'corporate-approval-4', label: 'Corporate Approval Level 4' }
            ]
        };

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            setupStageSubStageLogic();
            loadTemplates();
        });

        function setupStageSubStageLogic() {
            const mainStageSelect = document.getElementById('mainStage');
            const subStageSelect = document.getElementById('subStage');

            mainStageSelect.addEventListener('change', function() {
                const selectedStage = this.value;
                
                // Clear sub-stage options
                subStageSelect.innerHTML = '<option value="">Select Sub-Stage</option>';
                
                if (selectedStage && stageSubStageMap[selectedStage]) {
                    // Enable sub-stage dropdown
                    subStageSelect.disabled = false;
                    
                    // Populate sub-stage options
                    stageSubStageMap[selectedStage].forEach(subStage => {
                        const option = document.createElement('option');
                        option.value = subStage.value;
                        option.textContent = subStage.label;
                        subStageSelect.appendChild(option);
                    });
                } else {
                    // Disable sub-stage dropdown
                    subStageSelect.disabled = true;
                }
            });
        }

        function loadTemplates() {
            // This would normally fetch from the API
            // For demo purposes, we'll show the fixed interface
            console.log('Templates would be loaded from /api/v1/scorecard-templates/stage-options');
        }

        function resetForm() {
            document.getElementById('templateForm').reset();
            document.getElementById('subStage').disabled = true;
            document.getElementById('subStage').innerHTML = '<option value="">Select Sub-Stage</option>';
        }

        function addCriterion() {
            alert('Criterion editor would open here. The main fix is the Stage/Sub-Stage dropdowns which are now working correctly.');
        }

        // Form submission
        document.getElementById('templateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const templateData = {
                name: formData.get('templateName'),
                mainStage: formData.get('mainStage'),
                subStage: formData.get('subStage'),
                category: formData.get('subStage'), // Use subStage as category for backward compatibility
                description: formData.get('description'),
                passingScore: parseInt(formData.get('passingScore')),
                templateScope: formData.get('templateScope'),
                allowCustomCriteria: formData.has('allowCustomCriteria'),
                requireAllCriteria: formData.has('requireAllCriteria'),
                status: 'active',
                version: 1,
                criteria: []
            };

            console.log('Template data to be sent to API:', templateData);
            
            // This would normally be sent to: POST /api/v1/scorecard-templates
            alert('Template would be created with the following data:\n\n' + 
                  'Stage: ' + templateData.mainStage + '\n' +
                  'Sub-Stage: ' + templateData.subStage + '\n' +
                  'Name: ' + templateData.name + '\n\n' +
                  'The Stage and Sub-Stage dropdowns are now working correctly!');
        });

        // Search and filter functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            console.log('Searching for:', this.value);
        });

        document.getElementById('filterStage').addEventListener('change', function() {
            console.log('Filtering by stage:', this.value);
        });

        document.getElementById('filterScope').addEventListener('change', function() {
            console.log('Filtering by scope:', this.value);
        });
    </script>
</body>
</html>
