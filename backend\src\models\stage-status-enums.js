/**
 * Enums for the three-level hierarchy of application stages
 */

// Main Stages (5 stages)
const ApplicationMainStage = {
  ONBOARDING: 'ONBOARDING',
  BUSINESS_CASE_REVIEW: 'BUSINESS_CASE_REVIEW',
  DUE_DILIGENCE: 'DUE_DILIGENCE',
  ASSESSMENT_REPORT: 'ASSESSMENT_REPORT',
  APPLICATION_APPROVAL: 'APPLICATION_APPROVAL'
};

// Substages (17 substages distributed across main stages)
const ApplicationSubStage = {
  // Onboarding sub-stages
  BENEFICIARY_REGISTRATION: 'BENEFICIARY_REGISTRATION',
  PRE_SCREENING: 'PRE_SCREENING',
  
  // Business Case Review sub-stages
  DOCUMENT_COLLECTION: 'DOCUMENT_COLLECTION',
  DESKTOP_ANALYSIS: 'DESKTOP_ANALYSIS',
  
  // Due Diligence sub-stages
  DATA_VALIDATION: 'DATA_VALIDATION',
  SME_INTERVIEW: 'SME_INTERVIEW',
  SITE_VISIT: 'SITE_VISIT',
  
  // Assessment Report sub-stages
  REPORT_COMPLETION: 'REPORT_COMPLETION',
  REPORT_QUALITY_CHECK: 'REPORT_QUALITY_CHECK',
  REPORT_REVIEW: 'REPORT_REVIEW',
  
  // Application Approval sub-stages
  // Note: Each corporate approval substage includes committee review, decision, and approval documentation
  CORPORATE_APPROVAL_1: 'CORPORATE_APPROVAL_1',
  CORPORATE_APPROVAL_2: 'CORPORATE_APPROVAL_2',
  CORPORATE_APPROVAL_3: 'CORPORATE_APPROVAL_3',
  CORPORATE_APPROVAL_4: 'CORPORATE_APPROVAL_4'
};

// Stage Statuses
const StageStatus = {
  NOT_STARTED: 'NOT_STARTED',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  SKIPPED: 'SKIPPED',
  NOT_APPLICABLE: 'NOT_APPLICABLE'
};

// Mapping between Main Stages and Sub-Stages
const SubStageToMainStageMap = {
  [ApplicationSubStage.BENEFICIARY_REGISTRATION]: ApplicationMainStage.ONBOARDING,
  [ApplicationSubStage.PRE_SCREENING]: ApplicationMainStage.ONBOARDING,
  
  [ApplicationSubStage.DOCUMENT_COLLECTION]: ApplicationMainStage.BUSINESS_CASE_REVIEW,
  [ApplicationSubStage.DESKTOP_ANALYSIS]: ApplicationMainStage.BUSINESS_CASE_REVIEW,
  
  [ApplicationSubStage.DATA_VALIDATION]: ApplicationMainStage.DUE_DILIGENCE,
  [ApplicationSubStage.SME_INTERVIEW]: ApplicationMainStage.DUE_DILIGENCE,
  [ApplicationSubStage.SITE_VISIT]: ApplicationMainStage.DUE_DILIGENCE,
  
  [ApplicationSubStage.REPORT_COMPLETION]: ApplicationMainStage.ASSESSMENT_REPORT,
  [ApplicationSubStage.REPORT_QUALITY_CHECK]: ApplicationMainStage.ASSESSMENT_REPORT,
  [ApplicationSubStage.REPORT_REVIEW]: ApplicationMainStage.ASSESSMENT_REPORT,
  
  [ApplicationSubStage.CORPORATE_APPROVAL_1]: ApplicationMainStage.APPLICATION_APPROVAL,
  [ApplicationSubStage.CORPORATE_APPROVAL_2]: ApplicationMainStage.APPLICATION_APPROVAL,
  [ApplicationSubStage.CORPORATE_APPROVAL_3]: ApplicationMainStage.APPLICATION_APPROVAL,
  [ApplicationSubStage.CORPORATE_APPROVAL_4]: ApplicationMainStage.APPLICATION_APPROVAL
};

// Mapping from Main Stages to Sub-Stages
const MainStageToSubStagesMap = {
  [ApplicationMainStage.ONBOARDING]: [
    ApplicationSubStage.BENEFICIARY_REGISTRATION,
    ApplicationSubStage.PRE_SCREENING
  ],
  [ApplicationMainStage.BUSINESS_CASE_REVIEW]: [
    ApplicationSubStage.DOCUMENT_COLLECTION,
    ApplicationSubStage.DESKTOP_ANALYSIS
  ],
  [ApplicationMainStage.DUE_DILIGENCE]: [
    ApplicationSubStage.DATA_VALIDATION,
    ApplicationSubStage.SME_INTERVIEW,
    ApplicationSubStage.SITE_VISIT
  ],
  [ApplicationMainStage.ASSESSMENT_REPORT]: [
    ApplicationSubStage.REPORT_COMPLETION,
    ApplicationSubStage.REPORT_QUALITY_CHECK,
    ApplicationSubStage.REPORT_REVIEW
  ],
  [ApplicationMainStage.APPLICATION_APPROVAL]: [
    ApplicationSubStage.CORPORATE_APPROVAL_1,
    ApplicationSubStage.CORPORATE_APPROVAL_2,
    ApplicationSubStage.CORPORATE_APPROVAL_3,
    ApplicationSubStage.CORPORATE_APPROVAL_4
  ]
};

// Valid status transitions
const ValidStatusTransitions = {
  [StageStatus.NOT_STARTED]: [StageStatus.ACTIVE, StageStatus.SKIPPED, StageStatus.NOT_APPLICABLE],
  [StageStatus.ACTIVE]: [StageStatus.COMPLETED, StageStatus.SKIPPED, StageStatus.NOT_APPLICABLE],
  [StageStatus.COMPLETED]: [StageStatus.ACTIVE], // Can reopen a completed stage
  [StageStatus.SKIPPED]: [StageStatus.ACTIVE, StageStatus.NOT_STARTED],
  [StageStatus.NOT_APPLICABLE]: [StageStatus.NOT_STARTED, StageStatus.ACTIVE]
};

module.exports = {
  ApplicationMainStage,
  ApplicationSubStage,
  StageStatus,
  SubStageToMainStageMap,
  MainStageToSubStagesMap,
  ValidStatusTransitions
};