// Simple MongoDB fix for APP-2025-001 - No comments, just the fix
print("Starting fix for APP-2025-001...");

var app = db.applications.findOne({id: "APP-2025-001"});

if (!app) {
    print("Application APP-2025-001 not found");
    quit(1);
}

print("Found application: " + app.id);

if (!app.stageHierarchy || app.stageHierarchy.length === 0) {
    print("Initializing stage hierarchy...");
    
    var stageHierarchy = [
        {
            mainStage: "ONBOARDING",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "BENEFICIARY_REGISTRATION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "PRE_SCREENING",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "BUSINESS_CASE_REVIEW",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "DOCUMENT_COLLECTION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "DESKTOP_ANALYSIS",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "DUE_DILIGENCE",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "DATA_VALIDATION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "SME_INTERVIEW",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "SITE_VISIT",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "ASSESSMENT_REPORT",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "REPORT_COMPLETION",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "REPORT_QUALITY_CHECK",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "REPORT_REVIEW",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        },
        {
            mainStage: "APPLICATION_APPROVAL",
            status: "NOT_STARTED",
            subStages: [
                {
                    subStage: "CORPORATE_APPROVAL_1",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_2",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_3",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                },
                {
                    subStage: "CORPORATE_APPROVAL_4",
                    status: {
                        status: "NOT_STARTED",
                        startedAt: null,
                        completedAt: null,
                        assignedTo: null,
                        notes: null
                    },
                    history: []
                }
            ]
        }
    ];
    
    var result = db.applications.updateOne(
        {id: "APP-2025-001"},
        {
            $set: {
                stageHierarchy: stageHierarchy,
                currentMainStage: "ONBOARDING",
                currentSubStage: "BENEFICIARY_REGISTRATION",
                currentStageStatus: "NOT_STARTED"
            },
            $push: {
                stageStatusAuditLog: {
                    timestamp: new Date(),
                    mainStage: "ONBOARDING",
                    subStage: "BENEFICIARY_REGISTRATION",
                    fromStatus: null,
                    toStatus: "NOT_STARTED",
                    changedBy: "system",
                    reason: "Stage hierarchy initialization fix",
                    isOverride: false
                }
            }
        }
    );
    
    if (result.modifiedCount === 1) {
        print("SUCCESS: Stage hierarchy initialized!");
        print("Stage hierarchy length: " + stageHierarchy.length);
    } else {
        print("ERROR: Failed to update application");
    }
    
} else {
    print("Stage hierarchy already exists (length: " + app.stageHierarchy.length + ")");
}

print("Fix completed. Refresh the application page.");
