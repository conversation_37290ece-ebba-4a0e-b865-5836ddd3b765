const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Models
const FundingProgramme = require('./models/funding-programme');
const CorporateSponsor = require('./models/corporate-sponsor');

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-app';

async function addFundingProgramme() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if the programme already exists
    console.log('Checking if programme already exists...');
    const existingProgramme = await FundingProgramme.findOne({ 
      name: '20/20 Insight Default Programme'
    });
    
    if (existingProgramme) {
      console.log('Programme already exists:', existingProgramme.name);
      await mongoose.disconnect();
      return;
    }

    // Find a corporate sponsor to associate with the programme
    console.log('Finding a corporate sponsor...');
    let sponsor = await CorporateSponsor.findOne();
    
    if (!sponsor) {
      console.log('No corporate sponsor found. Creating a default one...');
      sponsor = new CorporateSponsor({
        name: '20/20 Insight Default Sponsor',
        description: 'Default corporate sponsor for funding programmes',
        contactPerson: {
          name: 'System Administrator',
          email: '<EMAIL>',
          phone: '************',
          position: 'System Administrator'
        },
        status: 'active'
      });
      
      await sponsor.save();
      console.log('Created default corporate sponsor:', sponsor.name);
    }

    // Create the funding programme
    console.log('Creating the funding programme...');
    const newProgramme = new FundingProgramme({
      name: '20/20 Insight Default Programme',
      description: 'Default funding programme for applications',
      corporateSponsorId: sponsor._id,
      objectives: ['Support SME growth', 'Promote economic development'],
      fundingCriteria: ['Viable business model', 'Growth potential'],
      fundingTerms: 'Standard terms and conditions apply',
      fundingTypes: ['Grant', 'Loan'],
      eligibilityCriteria: ['Registered business', 'Operating for at least 1 year'],
      geographicalCoverage: {
        provinces: ['All provinces'],
        municipalities: ['All municipalities']
      },
      sectors: {
        included: ['All sectors'],
        excluded: []
      },
      fundingPurposes: ['Assets Acquisition', 'Working Capital', 'Business Expansion'],
      budget: {
        totalAmount: 10000000,
        currency: 'ZAR',
        allocated: 0,
        remaining: 10000000
      },
      timeline: {
        startDate: new Date(),
        endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        applicationDeadline: new Date(new Date().setMonth(new Date().getMonth() + 6))
      },
      status: 'active'
    });
    
    await newProgramme.save();
    console.log('Successfully created funding programme:', newProgramme.name);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
addFundingProgramme();