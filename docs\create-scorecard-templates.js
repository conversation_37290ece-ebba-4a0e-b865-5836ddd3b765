// Create default scorecard templates for all sub-stages
print("Creating default scorecard templates...");

var templates = [
  {
    name: "Beneficiary Registration Scorecard",
    category: "BENEFICIARY_REGISTRATION",
    mainStage: "ONBOARDING",
    subStage: "BENEFICIARY_REGISTRATION",
    scope: "Programme Specific",
    description: "Evaluation criteria for beneficiary registration process",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "BR001",
        text: "Complete application form submitted",
        description: "All required fields in application form are completed",
        weight: 5,
        required: true,
        category: "Documentation"
      },
      {
        id: "BR002", 
        text: "Valid identification documents provided",
        description: "Current and valid ID documents submitted",
        weight: 5,
        required: true,
        category: "Identity Verification"
      },
      {
        id: "BR003",
        text: "Eligibility criteria met",
        description: "Applicant meets all programme eligibility requirements",
        weight: 4,
        required: true,
        category: "Eligibility"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Data Validation Scorecard",
    category: "DATA_VALIDATION",
    mainStage: "DUE_DILIGENCE", 
    subStage: "DATA_VALIDATION",
    scope: "Programme Specific",
    description: "Comprehensive data validation evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 75,
    allowCustomCriteria: true,
    requireAllCriteria: true,
    criteria: [
      {
        id: "DV001",
        text: "Identity verification completed",
        description: "All identity documents verified and authenticated",
        weight: 5,
        required: true,
        category: "Identity Verification"
      },
      {
        id: "DV002",
        text: "Business registration verified",
        description: "Business registration documents verified with authorities",
        weight: 4,
        required: true,
        category: "Business Registration"
      },
      {
        id: "DV003",
        text: "Financial information accuracy",
        description: "Financial statements and supporting documents verified",
        weight: 4,
        required: true,
        category: "Financial Verification"
      },
      {
        id: "DV004",
        text: "Tax compliance status",
        description: "Tax registration and compliance status verified",
        weight: 3,
        required: true,
        category: "Tax Compliance"
      },
      {
        id: "DV005",
        text: "Reference verification",
        description: "Business and personal references contacted and verified",
        weight: 3,
        required: false,
        category: "References"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "SME Interview Scorecard",
    category: "SME_INTERVIEW",
    mainStage: "DUE_DILIGENCE",
    subStage: "SME_INTERVIEW", 
    scope: "Programme Specific",
    description: "Subject matter expert interview evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "SME001",
        text: "Business knowledge and understanding",
        description: "Applicant demonstrates clear understanding of their business",
        weight: 4,
        required: true,
        category: "Business Knowledge"
      },
      {
        id: "SME002",
        text: "Market awareness",
        description: "Understanding of target market and competition",
        weight: 3,
        required: true,
        category: "Market Knowledge"
      },
      {
        id: "SME003",
        text: "Financial planning capability",
        description: "Ability to explain financial projections and planning",
        weight: 4,
        required: true,
        category: "Financial Planning"
      },
      {
        id: "SME004",
        text: "Management and operational skills",
        description: "Demonstrated management and operational capabilities",
        weight: 4,
        required: true,
        category: "Management Skills"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Site Visit Scorecard",
    category: "SITE_VISIT",
    mainStage: "DUE_DILIGENCE",
    subStage: "SITE_VISIT",
    scope: "Programme Specific", 
    description: "On-site evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "SV001",
        text: "Business premises suitability",
        description: "Physical location and premises are suitable for business operations",
        weight: 4,
        required: true,
        category: "Infrastructure"
      },
      {
        id: "SV002",
        text: "Equipment and assets verification",
        description: "Verification of declared equipment and business assets",
        weight: 3,
        required: true,
        category: "Assets"
      },
      {
        id: "SV003",
        text: "Operational readiness",
        description: "Business is operationally ready or has clear implementation plan",
        weight: 4,
        required: true,
        category: "Operations"
      },
      {
        id: "SV004",
        text: "Compliance with regulations",
        description: "Business complies with relevant local regulations and permits",
        weight: 3,
        required: true,
        category: "Compliance"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Insert all templates
var insertResult = db.scorecardtemplates.insertMany(templates);

if (insertResult.acknowledged) {
  print("SUCCESS: Created " + insertResult.insertedIds.length + " scorecard templates");
  print("Templates created for:");
  templates.forEach(function(template) {
    print("  - " + template.name + " (" + template.subStage + ")");
  });
} else {
  print("ERROR: Failed to create scorecard templates");
}

// Verify templates were created
var count = db.scorecardtemplates.countDocuments();
print("Total scorecard templates in database: " + count);

print("");
print("Next steps:");
print("1. Refresh the Scorecard Management page");
print("2. Check that sub-stages appear in the Category dropdown");
print("3. Create additional templates as needed");
print("4. Test scorecard functionality in applications");
