const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;

// Import models
const Scorecard = require('../models/scorecard');
const ScorecardVersion = require('../models/scorecard-version');

// Get all scorecards
router.get('/', async (req, res) => {
  try {
    const scorecards = await Scorecard.find();
    res.json(scorecards);
  } catch (err) {
    console.error('Error getting scorecards:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Get scorecards for an application
router.get('/application/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const scorecards = await Scorecard.find({ applicationId });
    res.json(scorecards);
  } catch (err) {
    console.error('Error getting application scorecards:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Get a specific scorecard
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const scorecard = await Scorecard.findById(id);
    
    if (!scorecard) {
      return res.status(404).json({ error: 'Scorecard not found' });
    }
    
    res.json(scorecard);
  } catch (err) {
    console.error('Error getting scorecard:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Create a new scorecard
router.post('/', async (req, res) => {
  try {
    const scorecardData = req.body;
    const scorecard = new Scorecard(scorecardData);
    const savedScorecard = await scorecard.save();
    res.status(201).json(savedScorecard);
  } catch (err) {
    console.error('Error creating scorecard:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Update a scorecard
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const scorecardData = req.body;
    
    // Update the updatedAt timestamp
    scorecardData.updatedAt = new Date();
    
    const updatedScorecard = await Scorecard.findByIdAndUpdate(
      id,
      scorecardData,
      { new: true, runValidators: true }
    );
    
    if (!updatedScorecard) {
      return res.status(404).json({ error: 'Scorecard not found' });
    }
    
    res.json(updatedScorecard);
  } catch (err) {
    console.error('Error updating scorecard:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Delete a scorecard
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const deletedScorecard = await Scorecard.findByIdAndDelete(id);
    
    if (!deletedScorecard) {
      return res.status(404).json({ error: 'Scorecard not found' });
    }
    
    res.json({ message: 'Scorecard deleted successfully' });
  } catch (err) {
    console.error('Error deleting scorecard:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Get scorecard summary for an application
router.get('/summary/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const scorecards = await Scorecard.find({ applicationId });
    
    if (!scorecards || scorecards.length === 0) {
      return res.json({
        applicationId,
        totalScore: 0,
        maxPossibleScore: 0,
        scorePercentage: 0,
        stageScores: []
      });
    }
    
    // Group scorecards by stage
    const stageScores = {};
    scorecards.forEach(scorecard => {
      if (!stageScores[scorecard.stageId]) {
        stageScores[scorecard.stageId] = {
          stageId: scorecard.stageId,
          stageName: scorecard.stageId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          score: 0,
          maxScore: 0,
          percentage: 0,
          status: 'NOT_STARTED',
          scorecards: []
        };
      }
      
      stageScores[scorecard.stageId].scorecards.push(scorecard);
      
      if (scorecard.totalScore) {
        stageScores[scorecard.stageId].score += scorecard.totalScore;
        stageScores[scorecard.stageId].maxScore += scorecard.maxPossibleScore || 100;
      }
      
      // Update stage status based on scorecard status
      if (scorecard.status === 'COMPLETED' && stageScores[scorecard.stageId].status !== 'COMPLETED') {
        stageScores[scorecard.stageId].status = 'COMPLETED';
      } else if (scorecard.status === 'IN_PROGRESS' && stageScores[scorecard.stageId].status === 'NOT_STARTED') {
        stageScores[scorecard.stageId].status = 'IN_PROGRESS';
      }
    });
    
    // Calculate percentages for each stage
    Object.values(stageScores).forEach(stage => {
      if (stage.maxScore > 0) {
        stage.percentage = Math.round((stage.score / stage.maxScore) * 100);
      }
      delete stage.scorecards; // Remove scorecards from response
    });
    
    // Calculate overall score
    const totalScore = Object.values(stageScores).reduce((sum, stage) => sum + stage.score, 0);
    const maxPossibleScore = Object.values(stageScores).reduce((sum, stage) => sum + stage.maxScore, 0);
    const scorePercentage = maxPossibleScore > 0 ? Math.round((totalScore / maxPossibleScore) * 100) : 0;
    
    res.json({
      applicationId,
      totalScore,
      maxPossibleScore,
      scorePercentage,
      stageScores: Object.values(stageScores)
    });
  } catch (err) {
    console.error('Error getting scorecard summary:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Save draft endpoint for auto-save and manual saves
router.post('/:id/save-draft', async (req, res) => {
  try {
    const { id } = req.params;
    const { data, lastKnownVersion, saveType = 'manual' } = req.body;
    const userId = req.headers['x-user-id'] || 'system'; // Get from auth middleware in production
    
    // Find the scorecard
    const scorecard = await Scorecard.findById(id);
    if (!scorecard) {
      return res.status(404).json({ error: 'Scorecard not found' });
    }
    
    // Check for version conflicts
    if (lastKnownVersion && scorecard.currentVersion > lastKnownVersion) {
      // Get the current version data for conflict detection
      const currentVersion = await ScorecardVersion.findOne({
        scorecardId: id,
        versionNumber: scorecard.currentVersion
      });
      
      // Simple conflict detection - check if criteria scores differ
      const conflicts = [];
      if (currentVersion && data.criteria) {
        data.criteria.forEach((criterion, index) => {
          const currentCriterion = currentVersion.data.criteria[index];
          if (currentCriterion && criterion.score !== currentCriterion.score) {
            conflicts.push({
              field: `criteria[${index}].score`,
              yourValue: criterion.score,
              currentValue: currentCriterion.score,
              baseValue: currentCriterion.score
            });
          }
        });
      }
      
      if (conflicts.length > 0) {
        return res.status(409).json({
          success: false,
          error: 'CONFLICT_DETECTED',
          conflicts,
          currentVersion: scorecard.currentVersion,
          yourVersion: lastKnownVersion
        });
      }
    }
    
    // Update scorecard data
    Object.assign(scorecard, {
      ...data,
      lastModifiedBy: userId,
      lastModifiedAt: new Date(),
      status: 'IN_PROGRESS',
      currentVersion: scorecard.currentVersion + 1
    });
    
    await scorecard.save();
    
    // Create version record
    const version = await ScorecardVersion.createVersion(
      scorecard,
      saveType,
      userId,
      `${saveType === 'auto' ? 'Auto-saved' : 'Manually saved'} draft`
    );
    
    res.json({
      success: true,
      version: scorecard.currentVersion,
      savedAt: new Date(),
      message: 'Draft saved successfully'
    });
  } catch (err) {
    console.error('Error saving draft:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Finalize scorecard endpoint
router.post('/:id/finalize', async (req, res) => {
  try {
    const { id } = req.params;
    const { data, lastKnownVersion } = req.body;
    const userId = req.headers['x-user-id'] || 'system';
    
    const scorecard = await Scorecard.findById(id);
    if (!scorecard) {
      return res.status(404).json({ error: 'Scorecard not found' });
    }
    
    // Check for conflicts before finalizing
    if (lastKnownVersion && scorecard.currentVersion > lastKnownVersion) {
      return res.status(409).json({
        success: false,
        error: 'VERSION_MISMATCH',
        message: 'The scorecard has been modified. Please refresh and try again.',
        currentVersion: scorecard.currentVersion,
        yourVersion: lastKnownVersion
      });
    }
    
    // Update and finalize
    Object.assign(scorecard, data);
    await scorecard.finalize(userId);
    
    // Create final version record
    await ScorecardVersion.createVersion(
      scorecard,
      'final',
      userId,
      'Scorecard finalized'
    );
    
    res.json({
      success: true,
      scorecardId: scorecard._id,
      finalVersion: scorecard.currentVersion,
      finalizedAt: scorecard.finalizedAt
    });
  } catch (err) {
    console.error('Error finalizing scorecard:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Get version history endpoint
router.get('/:id/versions', async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10, offset = 0 } = req.query;
    
    const versions = await ScorecardVersion.getVersionHistory(
      id,
      parseInt(limit),
      parseInt(offset)
    );
    
    const total = await ScorecardVersion.countDocuments({ scorecardId: id });
    const scorecard = await Scorecard.findById(id).select('currentVersion');
    
    res.json({
      versions: versions.map(v => ({
        versionNumber: v.versionNumber,
        createdBy: v.createdBy,
        createdAt: v.createdAt,
        saveType: v.metadata.saveType,
        changesSummary: v.metadata.changesSummary
      })),
      total,
      currentVersion: scorecard?.currentVersion || 0
    });
  } catch (err) {
    console.error('Error getting version history:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Get specific version endpoint
router.get('/:id/versions/:versionNumber', async (req, res) => {
  try {
    const { id, versionNumber } = req.params;
    
    const version = await ScorecardVersion.findOne({
      scorecardId: id,
      versionNumber: parseInt(versionNumber)
    });
    
    if (!version) {
      return res.status(404).json({ error: 'Version not found' });
    }
    
    res.json(version);
  } catch (err) {
    console.error('Error getting version:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Restore version endpoint
router.post('/:id/restore/:versionNumber', async (req, res) => {
  try {
    const { id, versionNumber } = req.params;
    const userId = req.headers['x-user-id'] || 'system';
    
    const version = await ScorecardVersion.findOne({
      scorecardId: id,
      versionNumber: parseInt(versionNumber)
    });
    
    if (!version) {
      return res.status(404).json({ error: 'Version not found' });
    }
    
    // Restore the version
    const scorecard = await version.restore();
    scorecard.lastModifiedBy = userId;
    scorecard.currentVersion += 1;
    await scorecard.save();
    
    // Create a new version record for the restore
    await ScorecardVersion.createVersion(
      scorecard,
      'manual',
      userId,
      `Restored from version ${versionNumber}`
    );
    
    res.json({
      success: true,
      restoredVersion: parseInt(versionNumber),
      newVersion: scorecard.currentVersion,
      message: `Version ${versionNumber} restored as version ${scorecard.currentVersion}`
    });
  } catch (err) {
    console.error('Error restoring version:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

module.exports = router;
