#!/bin/bash

echo "Restarting backend server..."

# Kill all Node.js processes
echo "Killing all Node.js processes..."
pkill -f node

# Wait for processes to terminate
echo "Waiting for processes to terminate..."
sleep 2

# Start MongoDB server
echo "Starting MongoDB server..."
node start-mongodb-server.js &

# Wait for MongoDB to start
echo "Waiting for MongoDB to start..."
sleep 5

# Start backend server
echo "Starting backend server..."
npm run dev &

echo "Backend server restarted successfully!"