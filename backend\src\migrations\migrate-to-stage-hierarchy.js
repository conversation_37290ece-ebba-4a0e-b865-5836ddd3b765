/**
 * Migration script to update existing applications with the new stage status fields
 * 
 * This script:
 * 1. Adds the new three-level hierarchy fields to all applications
 * 2. Maps existing stage/substage values to the new hierarchy
 * 3. Sets appropriate default values for new fields
 */

const mongoose = require('mongoose');
const Application = require('../models/application');
const { 
  ApplicationMainStage, 
  ApplicationSubStage, 
  StageStatus,
  SubStageToMainStageMap,
  MainStageToSubStagesMap
} = require('../models/stage-status-enums');

// Map from legacy currentStage to new mainStage and subStage
const legacyStageMapping = {
  'ONBOARDING': {
    mainStage: ApplicationMainStage.ONBOARDING,
    subStage: ApplicationSubStage.BENEFICIARY_REGISTRATION
  },
  'PRE_SCREENING_PENDING': {
    mainStage: ApplicationMainStage.ONBOARDING,
    subStage: ApplicationSubStage.PRE_SCREENING
  },
  'PRE_SCREENING_COMPLETE': {
    mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
    subStage: ApplicationSubStage.DOCUMENT_COLLECTION
  },
  'DOCUMENT_COLLECTION': {
    mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
    subStage: ApplicationSubStage.DOCUMENT_COLLECTION
  },
  'DESKTOP_ANALYSIS': {
    mainStage: ApplicationMainStage.BUSINESS_CASE_REVIEW,
    subStage: ApplicationSubStage.DESKTOP_ANALYSIS
  },
  'DATA_VALIDATION': {
    mainStage: ApplicationMainStage.DUE_DILIGENCE,
    subStage: ApplicationSubStage.DATA_VALIDATION
  },
  'SME_INTERVIEW_SCHEDULED': {
    mainStage: ApplicationMainStage.DUE_DILIGENCE,
    subStage: ApplicationSubStage.SME_INTERVIEW
  },
  'SME_INTERVIEW_COMPLETED': {
    mainStage: ApplicationMainStage.DUE_DILIGENCE,
    subStage: ApplicationSubStage.SITE_VISIT
  },
  'FINAL_REVIEW': {
    mainStage: ApplicationMainStage.ASSESSMENT_REPORT,
    subStage: ApplicationSubStage.REPORT_COMPLETION
  },
  'APPLICATION_APPROVAL': {
    mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
    subStage: ApplicationSubStage.COMMITTEE_REVIEW
  },
  'APPROVED': {
    mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
    subStage: ApplicationSubStage.FINAL_DECISION
  },
  'REJECTED': {
    mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
    subStage: ApplicationSubStage.FINAL_DECISION
  },
  'WITHDRAWN': {
    mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
    subStage: ApplicationSubStage.FINAL_DECISION
  },
  'ON_HOLD': {
    mainStage: ApplicationMainStage.APPLICATION_APPROVAL,
    subStage: ApplicationSubStage.COMMITTEE_REVIEW
  }
};

// Map from legacy status to new StageStatus
const legacyStatusMapping = {
  'pending': StageStatus.NOT_STARTED,
  'in-review': StageStatus.ACTIVE,
  'approved': StageStatus.COMPLETED,
  'rejected': StageStatus.COMPLETED,
  'withdrawn-by-applicant': StageStatus.SKIPPED,
  'on-hold': StageStatus.ACTIVE,
  'sent-back': StageStatus.ACTIVE,
  'PENDING': StageStatus.NOT_STARTED,
  'IN_PROGRESS': StageStatus.ACTIVE,
  'COMPLETED': StageStatus.COMPLETED
};

/**
 * Initialize the stage hierarchy for an application
 */
function initializeStageHierarchy() {
  const stageHierarchy = [];
  
  // Create main stages with default substages
  Object.values(ApplicationMainStage).forEach(mainStage => {
    const mainStageObj = {
      mainStage,
      status: StageStatus.NOT_STARTED,
      subStages: []
    };
    
    // Add all substages for this main stage
    MainStageToSubStagesMap[mainStage].forEach(subStage => {
      mainStageObj.subStages.push({
        subStage,
        status: {
          status: StageStatus.NOT_STARTED
        },
        history: []
      });
    });
    
    stageHierarchy.push(mainStageObj);
  });
  
  return stageHierarchy;
}

/**
 * Update the stage status for a specific substage
 */
function updateSubStageStatus(stageHierarchy, mainStage, subStage, status, assignedTo, notes) {
  // Find the main stage
  const mainStageObj = stageHierarchy.find(ms => ms.mainStage === mainStage);
  if (!mainStageObj) return;
  
  // Find the substage
  const subStageObj = mainStageObj.subStages.find(ss => ss.subStage === subStage);
  if (!subStageObj) return;
  
  // Update the status
  subStageObj.status = {
    status,
    startedAt: status === StageStatus.ACTIVE ? new Date() : undefined,
    completedAt: status === StageStatus.COMPLETED ? new Date() : undefined,
    assignedTo,
    notes
  };
  
  // Add to history
  subStageObj.history.push({
    ...subStageObj.status,
    timestamp: new Date()
  });
  
  // Update main stage status based on substages
  updateMainStageStatus(stageHierarchy, mainStage);
}

/**
 * Update the main stage status based on its substages
 */
function updateMainStageStatus(stageHierarchy, mainStage) {
  const mainStageObj = stageHierarchy.find(ms => ms.mainStage === mainStage);
  if (!mainStageObj) return;
  
  const subStages = mainStageObj.subStages;
  
  // If all substages are completed, mark main stage as completed
  if (subStages.every(ss => ss.status.status === StageStatus.COMPLETED)) {
    mainStageObj.status = StageStatus.COMPLETED;
    return;
  }
  
  // If any substage is active, mark main stage as active
  if (subStages.some(ss => ss.status.status === StageStatus.ACTIVE)) {
    mainStageObj.status = StageStatus.ACTIVE;
    return;
  }
  
  // If any substage is completed but not all, mark main stage as active
  if (subStages.some(ss => ss.status.status === StageStatus.COMPLETED)) {
    mainStageObj.status = StageStatus.ACTIVE;
    return;
  }
  
  // If all substages are skipped or not applicable, mark main stage as skipped
  if (subStages.every(ss => 
    ss.status.status === StageStatus.SKIPPED || 
    ss.status.status === StageStatus.NOT_APPLICABLE
  )) {
    mainStageObj.status = StageStatus.SKIPPED;
    return;
  }
  
  // Default to not started
  mainStageObj.status = StageStatus.NOT_STARTED;
}

/**
 * Run the migration
 */
async function runMigration() {
  try {
    console.log('Starting migration to stage hierarchy...');
    
    // Get all applications
    const applications = await Application.find({});
    console.log(`Found ${applications.length} applications to migrate`);
    
    let migratedCount = 0;
    
    // Process each application
    for (const app of applications) {
      try {
        // Initialize the stage hierarchy
        const stageHierarchy = initializeStageHierarchy();
        
        // Map current stage and substage to new hierarchy
        let currentMainStage = ApplicationMainStage.ONBOARDING;
        let currentSubStage = ApplicationSubStage.BENEFICIARY_REGISTRATION;
        let currentStageStatus = StageStatus.NOT_STARTED;
        
        // Map from legacy currentStage
        if (app.currentStage && legacyStageMapping[app.currentStage]) {
          currentMainStage = legacyStageMapping[app.currentStage].mainStage;
          currentSubStage = app.substage || legacyStageMapping[app.currentStage].subStage;
        }
        
        // Map from legacy status
        if (app.status && legacyStatusMapping[app.status]) {
          currentStageStatus = legacyStatusMapping[app.status];
        }
        
        // Update the current substage status
        updateSubStageStatus(
          stageHierarchy, 
          currentMainStage, 
          currentSubStage, 
          currentStageStatus,
          app.assignedTo,
          'Migrated from legacy stage'
        );
        
        // Set previous stages as completed
        const mainStages = Object.values(ApplicationMainStage);
        const currentMainStageIndex = mainStages.indexOf(currentMainStage);
        
        // Mark previous main stages as completed
        for (let i = 0; i < currentMainStageIndex; i++) {
          const prevMainStage = mainStages[i];
          const prevMainStageObj = stageHierarchy.find(ms => ms.mainStage === prevMainStage);
          
          if (prevMainStageObj) {
            prevMainStageObj.status = StageStatus.COMPLETED;
            
            // Mark all substages as completed
            prevMainStageObj.subStages.forEach(ss => {
              ss.status = {
                status: StageStatus.COMPLETED,
                completedAt: new Date(app.submissionDate || Date.now()),
                notes: 'Automatically marked as completed during migration'
              };
              
              ss.history.push({
                ...ss.status,
                timestamp: new Date()
              });
            });
          }
        }
        
        // Update the application with new fields
        app.currentMainStage = currentMainStage;
        app.currentSubStage = currentSubStage;
        app.currentStageStatus = currentStageStatus;
        app.stageHierarchy = stageHierarchy;
        
        // Add initial audit log entry
        app.stageStatusAuditLog = [{
          timestamp: new Date(),
          mainStage: currentMainStage,
          subStage: currentSubStage,
          fromStatus: StageStatus.NOT_STARTED,
          toStatus: currentStageStatus,
          changedBy: 'system',
          reason: 'Initial migration to stage hierarchy',
          isOverride: false
        }];
        
        // Save the updated application
        await app.save();
        migratedCount++;
        
        if (migratedCount % 10 === 0) {
          console.log(`Migrated ${migratedCount} applications...`);
        }
      } catch (err) {
        console.error(`Error migrating application ${app.id}:`, err);
      }
    }
    
    console.log(`Migration completed. Successfully migrated ${migratedCount} out of ${applications.length} applications.`);
  } catch (err) {
    console.error('Migration failed:', err);
  }
}

module.exports = { runMigration };

// Run the migration if this script is executed directly
if (require.main === module) {
  // Connect to MongoDB
  const dbConfig = require('../config/database-config');
  mongoose.connect(dbConfig.url, dbConfig.options)
    .then(() => {
      console.log('Connected to MongoDB');
      return runMigration();
    })
    .then(() => {
      console.log('Migration completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('Migration failed:', err);
      process.exit(1);
    });
}