const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const mongoose = require('mongoose');
const { readConfig } = require('../config/database-config');

const BACKUPS_DIR = path.resolve(__dirname, '../../../backups');
const LOGS_DIR = path.resolve(__dirname, '../../../logs');

// Ensure backup and log directories exist
if (!fs.existsSync(BACKUPS_DIR)) fs.mkdirSync(BACKUPS_DIR, { recursive: true });
if (!fs.existsSync(LOGS_DIR)) fs.mkdirSync(LOGS_DIR, { recursive: true });

/**
 * Create a MongoDB backup
 * @returns {Promise<Object>} Backup result
 */
async function createMongoDBBackup() {
  const config = readConfig();
  
  if (config.mode !== 'persistent') {
    return {
      success: false,
      message: 'Backups only available in persistent mode'
    };
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupName = `backup-${timestamp}`;
  const backupPath = path.join(BACKUPS_DIR, backupName);
  const logFile = path.join(LOGS_DIR, `backup-${timestamp}.log`);

  try {
    // Create backup using mongodump
    await new Promise((resolve, reject) => {
      exec(`mongodump --uri="${config.persistentUri}" --out="${backupPath}"`, 
        (error, stdout, stderr) => {
          fs.writeFileSync(logFile, `Mongodump output:\n${stdout}\n\nErrors:\n${stderr}`);
          if (error) reject(error);
          else resolve();
        });
    });

    // Compress backup
    await new Promise((resolve, reject) => {
      exec(`zip -r ${backupPath}.zip ${backupPath}`, 
        (error, stdout, stderr) => {
          fs.appendFileSync(logFile, `\n\nZip output:\n${stdout}\n\nErrors:\n${stderr}`);
          if (error) reject(error);
          else resolve();
        });
    });

    // Remove uncompressed backup
    fs.rmSync(backupPath, { recursive: true, force: true });

    return {
      success: true,
      backupPath: `${backupPath}.zip`,
      logFile,
      message: 'Backup created successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      logFile,
      message: 'Backup failed'
    };
  }
}

/**
 * Schedule regular backups (daily at 2 AM)
 */
function scheduleBackups() {
  // Implement actual scheduling logic here
  console.log('Scheduled backups initialized (would run daily at 2 AM)');
}

module.exports = {
  createMongoDBBackup,
  scheduleBackups
};
