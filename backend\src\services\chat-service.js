const ChatRoom = require('../models/chat-room');
const ChatParticipant = require('../models/chat-participant');
const ChatMessage = require('../models/chat-message');
const User = require('../models/user');
const NotificationService = require('./notification-service');
const WebSocketService = require('./websocket-service');

/**
 * Chat Service
 * Core business logic for managing chat functionality
 */
class ChatService {
  /**
   * Create a new chat room
   * @param {Object} roomData - Room data
   * @param {String} createdBy - User ID who created the room
   * @returns {Promise<Object>} Created room
   */
  async createRoom(roomData, createdBy) {
    try {
      const { name, type, description, participants, relatedEntity } = roomData;

      // Validate room type
      if (!['DIRECT', 'GROUP', 'COMMITTEE', 'APPLICATION', 'PROGRAMME'].includes(type)) {
        throw new Error('Invalid room type');
      }

      // For direct messages, use findOrCreateDirectRoom
      if (type === 'DIRECT') {
        if (!participants || participants.length !== 2) {
          throw new Error('Direct message rooms must have exactly 2 participants');
        }
        return await ChatRoom.findOrCreateDirectRoom(
          participants[0],
          participants[1],
          createdBy
        );
      }

      // Create room
      const room = new ChatRoom({
        name,
        type,
        description,
        relatedEntity,
        createdBy
      });

      await room.save();

      // Add creator as admin
      await this.addParticipant(room._id, createdBy, 'ADMIN', createdBy);

      // Add other participants
      if (participants && participants.length > 0) {
        for (const userId of participants) {
          if (userId !== createdBy) {
            await this.addParticipant(room._id, userId, 'MEMBER', createdBy);
          }
        }
      }

      // Send system message
      await this.sendSystemMessage(room._id, 'ROOM_CREATED', {
        createdBy: createdBy,
        roomName: name
      });

      // Send notifications to participants
      await this.notifyRoomCreation(room, createdBy);

      return room;
    } catch (error) {
      console.error('Error creating room:', error);
      throw error;
    }
  }

  /**
   * Add participant to room
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID to add
   * @param {String} role - Participant role
   * @param {String} addedBy - User ID who added the participant
   * @returns {Promise<Object>} Created participant
   */
  async addParticipant(roomId, userId, role = 'MEMBER', addedBy) {
    try {
      // Check if participant already exists
      const existingParticipant = await ChatParticipant.findOne({
        roomId,
        userId
      });

      if (existingParticipant) {
        if (existingParticipant.isActive) {
          throw new Error('User is already a participant');
        }
        // Reactivate participant
        existingParticipant.isActive = true;
        existingParticipant.isBanned = false;
        existingParticipant.joinedAt = new Date();
        existingParticipant.role = role;
        await existingParticipant.save();
        return existingParticipant;
      }

      // Create new participant
      const participant = new ChatParticipant({
        roomId,
        userId,
        role,
        addedBy,
        joinedAt: new Date()
      });

      await participant.save();

      // Send system message
      const user = await User.findById(userId);
      await this.sendSystemMessage(roomId, 'USER_ADDED', {
        userId,
        username: user.username,
        addedBy
      });

      // Notify user they were added
      await this.notifyUserAdded(roomId, userId, addedBy);

      return participant;
    } catch (error) {
      console.error('Error adding participant:', error);
      throw error;
    }
  }

  /**
   * Remove participant from room
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID to remove
   * @param {String} removedBy - User ID who removed the participant
   * @returns {Promise<Object>} Updated participant
   */
  async removeParticipant(roomId, userId, removedBy) {
    try {
      const participant = await ChatParticipant.findOne({ roomId, userId });
      
      if (!participant) {
        throw new Error('Participant not found');
      }

      participant.isActive = false;
      participant.leftAt = new Date();
      await participant.save();

      // Send system message
      const user = await User.findById(userId);
      await this.sendSystemMessage(roomId, 'USER_REMOVED', {
        userId,
        username: user.username,
        removedBy
      });

      return participant;
    } catch (error) {
      console.error('Error removing participant:', error);
      throw error;
    }
  }

  /**
   * Send a message
   * @param {Object} messageData - Message data
   * @param {String} senderId - Sender user ID
   * @returns {Promise<Object>} Created message
   */
  async sendMessage(messageData, senderId) {
    try {
      const { roomId, content, attachments, replyTo, mentions } = messageData;

      // Verify sender is participant
      const participant = await ChatParticipant.findOne({
        roomId,
        userId: senderId,
        isActive: true
      });

      if (!participant) {
        throw new Error('You are not a participant in this room');
      }

      if (!participant.permissions.canSendMessages) {
        throw new Error('You do not have permission to send messages');
      }

      // Create message
      const message = new ChatMessage({
        roomId,
        senderId,
        content,
        attachments,
        replyTo,
        mentions
      });

      await message.save();

      // Populate sender info
      await message.populate('senderId', 'username firstName lastName profilePicture');

      // Send real-time notification via WebSocket
      await this.broadcastMessage(roomId, message);

      // Send notifications for mentions
      if (mentions && mentions.length > 0) {
        await this.notifyMentions(message, mentions);
      }

      // Send push notifications to offline users
      await this.notifyOfflineUsers(roomId, message, senderId);

      return message;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send system message
   * @param {String} roomId - Room ID
   * @param {String} type - System message type
   * @param {Object} data - System message data
   * @returns {Promise<Object>} Created message
   */
  async sendSystemMessage(roomId, type, data) {
    try {
      const message = new ChatMessage({
        roomId,
        senderId: data.userId || data.addedBy || data.removedBy || data.createdBy,
        content: {
          type: 'SYSTEM',
          text: this.generateSystemMessageText(type, data)
        },
        systemMessage: {
          type,
          data
        }
      });

      await message.save();
      await this.broadcastMessage(roomId, message);

      return message;
    } catch (error) {
      console.error('Error sending system message:', error);
      throw error;
    }
  }

  /**
   * Generate system message text
   * @param {String} type - System message type
   * @param {Object} data - System message data
   * @returns {String} Message text
   */
  generateSystemMessageText(type, data) {
    switch (type) {
      case 'ROOM_CREATED':
        return `Room "${data.roomName}" was created`;
      case 'USER_JOINED':
        return `${data.username} joined the room`;
      case 'USER_LEFT':
        return `${data.username} left the room`;
      case 'USER_ADDED':
        return `${data.username} was added to the room`;
      case 'USER_REMOVED':
        return `${data.username} was removed from the room`;
      case 'ROOM_NAME_CHANGED':
        return `Room name changed from "${data.oldName}" to "${data.newName}"`;
      default:
        return 'System message';
    }
  }

  /**
   * Broadcast message to room participants via WebSocket
   * @param {String} roomId - Room ID
   * @param {Object} message - Message object
   * @returns {Promise<void>}
   */
  async broadcastMessage(roomId, message) {
    try {
      // Get all active participants
      const participants = await ChatParticipant.find({
        roomId,
        isActive: true
      });

      const userIds = participants.map(p => p.userId.toString());

      // Send via WebSocket
      await WebSocketService.sendToUsers(userIds, {
        type: 'NEW_CHAT_MESSAGE',
        data: {
          roomId: roomId.toString(),
          message: {
            id: message.id,
            _id: message._id,
            content: message.content,
            senderId: message.senderId,
            attachments: message.attachments,
            replyTo: message.replyTo,
            mentions: message.mentions,
            createdAt: message.createdAt,
            formattedTime: message.formattedTime
          }
        }
      });
    } catch (error) {
      console.error('Error broadcasting message:', error);
    }
  }

  /**
   * Notify room creation
   * @param {Object} room - Room object
   * @param {String} createdBy - Creator user ID
   * @returns {Promise<void>}
   */
  async notifyRoomCreation(room, createdBy) {
    try {
      const participants = await ChatParticipant.find({
        roomId: room._id,
        userId: { $ne: createdBy },
        isActive: true
      });

      const creator = await User.findById(createdBy);

      for (const participant of participants) {
        await NotificationService.createNotification({
          title: 'New Chat Room',
          message: `${creator.username} added you to "${room.name}"`,
          type: 'CUSTOM',
          category: 'GENERAL',
          priority: 'MEDIUM',
          deliveryMethods: {
            inApp: true,
            email: true,
            push: true
          },
          targetAudience: 'SPECIFIC_USERS',
          targetUsers: [participant.userId],
          relatedEntity: {
            entityType: 'CHAT_ROOM',
            entityId: room._id.toString(),
            entityData: {
              roomName: room.name,
              roomType: room.type
            }
          },
          actions: [{
            label: 'Open Chat',
            action: 'NAVIGATE',
            url: `/chat/room/${room._id}`,
            style: 'PRIMARY'
          }]
        }, createdBy);
      }
    } catch (error) {
      console.error('Error notifying room creation:', error);
    }
  }

  /**
   * Notify user added to room
   * @param {String} roomId - Room ID
   * @param {String} userId - Added user ID
   * @param {String} addedBy - User who added them
   * @returns {Promise<void>}
   */
  async notifyUserAdded(roomId, userId, addedBy) {
    try {
      const room = await ChatRoom.findById(roomId);
      const addedByUser = await User.findById(addedBy);

      await NotificationService.createNotification({
        title: 'Added to Chat',
        message: `${addedByUser.username} added you to "${room.name}"`,
        type: 'CUSTOM',
        category: 'GENERAL',
        priority: 'MEDIUM',
        deliveryMethods: {
          inApp: true,
          email: true,
          push: true
        },
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: [userId],
        relatedEntity: {
          entityType: 'CHAT_ROOM',
          entityId: roomId,
          entityData: {
            roomName: room.name,
            roomType: room.type
          }
        },
        actions: [{
          label: 'Open Chat',
          action: 'NAVIGATE',
          url: `/chat/room/${roomId}`,
          style: 'PRIMARY'
        }]
      }, addedBy);
    } catch (error) {
      console.error('Error notifying user added:', error);
    }
  }

  /**
   * Notify mentions
   * @param {Object} message - Message object
   * @param {Array} mentions - Array of mentions
   * @returns {Promise<void>}
   */
  async notifyMentions(message, mentions) {
    try {
      const room = await ChatRoom.findById(message.roomId);
      const sender = await User.findById(message.senderId);

      for (const mention of mentions) {
        await NotificationService.createNotification({
          title: 'You were mentioned',
          message: `${sender.username} mentioned you in "${room.name}": ${message.content.text.substring(0, 100)}...`,
          type: 'CUSTOM',
          category: 'GENERAL',
          priority: 'HIGH',
          deliveryMethods: {
            inApp: true,
            email: true,
            push: true
          },
          targetAudience: 'SPECIFIC_USERS',
          targetUsers: [mention.userId],
          relatedEntity: {
            entityType: 'CHAT_MESSAGE',
            entityId: message._id.toString(),
            entityData: {
              roomId: room._id,
              roomName: room.name,
              messagePreview: message.content.text.substring(0, 100)
            }
          },
          actions: [{
            label: 'View Message',
            action: 'NAVIGATE',
            url: `/chat/room/${room._id}?messageId=${message._id}`,
            style: 'PRIMARY'
          }]
        }, message.senderId);
      }
    } catch (error) {
      console.error('Error notifying mentions:', error);
    }
  }

  /**
   * Notify offline users about new message
   * @param {String} roomId - Room ID
   * @param {Object} message - Message object
   * @param {String} senderId - Sender ID
   * @returns {Promise<void>}
   */
  async notifyOfflineUsers(roomId, message, senderId) {
    try {
      const room = await ChatRoom.findById(roomId);
      const sender = await User.findById(senderId);

      // Get participants who have notifications enabled and are not online
      const participants = await ChatParticipant.find({
        roomId,
        userId: { $ne: senderId },
        isActive: true,
        'preferences.muteNotifications': false
      });

      const offlineUserIds = [];
      for (const participant of participants) {
        const isOnline = WebSocketService.getUserConnectionInfo(participant.userId.toString()).isConnected;
        if (!isOnline) {
          offlineUserIds.push(participant.userId);
        }
      }

      if (offlineUserIds.length > 0) {
        // Determine room display name
        let roomDisplayName = room.name;
        if (room.type === 'DIRECT') {
          roomDisplayName = sender.username;
        }

        await NotificationService.createNotification({
          title: `New message from ${roomDisplayName}`,
          message: `${sender.username}: ${message.content.text.substring(0, 100)}${message.content.text.length > 100 ? '...' : ''}`,
          type: 'CUSTOM',
          category: 'GENERAL',
          priority: 'MEDIUM',
          deliveryMethods: {
            inApp: true,
            email: true,
            push: true,
            sms: room.type === 'DIRECT' // SMS only for direct messages
          },
          targetAudience: 'SPECIFIC_USERS',
          targetUsers: offlineUserIds,
          relatedEntity: {
            entityType: 'CHAT_MESSAGE',
            entityId: message._id.toString(),
            entityData: {
              roomId: room._id,
              roomName: room.name,
              roomType: room.type,
              messagePreview: message.content.text.substring(0, 100)
            }
          },
          actions: [{
            label: 'Reply',
            action: 'NAVIGATE',
            url: `/chat/room/${room._id}`,
            style: 'PRIMARY'
          }]
        }, senderId);
      }
    } catch (error) {
      console.error('Error notifying offline users:', error);
    }
  }

  /**
   * Get user's chat rooms
   * @param {String} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} User's rooms
   */
  async getUserRooms(userId, options = {}) {
    try {
      const { includeArchived = false, type = null } = options;

      const participants = await ChatParticipant.getUserRooms(userId, includeArchived);

      let rooms = participants.map(p => {
        const room = p.roomId;
        return {
          ...room.toObject(),
          participant: {
            role: p.role,
            unreadCount: p.unreadCount,
            lastSeenAt: p.lastSeenAt,
            preferences: p.preferences
          }
        };
      });

      // Filter by type if specified
      if (type) {
        rooms = rooms.filter(r => r.type === type);
      }

      // For direct messages, set display name to other user's name
      for (const room of rooms) {
        if (room.type === 'DIRECT') {
          const otherUserId = room.directMessageUsers.find(id => id.toString() !== userId);
          const otherUser = await User.findById(otherUserId).select('username firstName lastName profilePicture');
          room.displayName = otherUser ? `${otherUser.firstName} ${otherUser.lastName}`.trim() || otherUser.username : 'Unknown User';
          room.otherUser = otherUser;
        }
      }

      return rooms;
    } catch (error) {
      console.error('Error getting user rooms:', error);
      throw error;
    }
  }

  /**
   * Get room messages
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID requesting messages
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Messages
   */
  async getRoomMessages(roomId, userId, options = {}) {
    try {
      // Verify user is participant
      const participant = await ChatParticipant.findOne({
        roomId,
        userId,
        isActive: true
      });

      if (!participant) {
        throw new Error('You are not a participant in this room');
      }

      const messages = await ChatMessage.getRoomMessages(roomId, options);

      // Mark messages as read
      if (messages.length > 0 && participant.unreadCount > 0) {
        await participant.markMessagesAsRead(messages[0]._id);
      }

      return messages;
    } catch (error) {
      console.error('Error getting room messages:', error);
      throw error;
    }
  }

  /**
   * Search messages in a room
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID
   * @param {String} searchTerm - Search term
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Search results
   */
  async searchMessages(roomId, userId, searchTerm, options = {}) {
    try {
      // Verify user is participant
      const participant = await ChatParticipant.findOne({
        roomId,
        userId,
        isActive: true
      });

      if (!participant) {
        throw new Error('You are not a participant in this room');
      }

      return await ChatMessage.searchMessages(roomId, searchTerm, options);
    } catch (error) {
      console.error('Error searching messages:', error);
      throw error;
    }
  }

  /**
   * Update room settings
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID
   * @param {Object} updates - Settings updates
   * @returns {Promise<Object>} Updated room
   */
  async updateRoomSettings(roomId, userId, updates) {
    try {
      // Verify user has permission
      const participant = await ChatParticipant.findOne({
        roomId,
        userId,
        isActive: true
      });

      if (!participant || !participant.permissions.canEditRoomInfo) {
        throw new Error('You do not have permission to edit room settings');
      }

      const room = await ChatRoom.findById(roomId);
      
      // Update allowed fields
      const allowedFields = ['name', 'description', 'settings'];
      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          room[field] = updates[field];
        }
      }

      await room.save();

      // Send system message if name changed
      if (updates.name && updates.name !== room.name) {
        await this.sendSystemMessage(roomId, 'ROOM_NAME_CHANGED', {
          oldName: room.name,
          newName: updates.name,
          changedBy: userId
        });
      }

      return room;
    } catch (error) {
      console.error('Error updating room settings:', error);
      throw error;
    }
  }

  /**
   * Get room participants
   * @param {String} roomId - Room ID
   * @param {String} userId - User ID requesting
   * @returns {Promise<Array>} Participants
   */
  async getRoomParticipants(roomId, userId) {
    try {
      // Verify user is participant
      const isParticipant = await ChatParticipant.findOne({
        roomId,
        userId,
        isActive: true
      });

      if (!isParticipant) {
        throw new Error('You are not a participant in this room');
      }

      return await ChatParticipant.getRoomParticipants(roomId);
    } catch (error) {
      console.error('Error getting room participants:', error);
      throw error;
    }
  }
}

module.exports = new ChatService();