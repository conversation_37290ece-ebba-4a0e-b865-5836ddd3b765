const NotificationService = require('./notification-service');
const NotificationTemplate = require('../models/notification-template');
const Application = require('../models/application');
const User = require('../models/user');
const WebSocketService = require('./websocket-service');

/**
 * Application Event Service
 * Handles application workflow notifications and event processing
 */
class ApplicationEventService {
  constructor() {
    this.eventHandlers = new Map();
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers for different application events
   */
  setupEventHandlers() {
    this.eventHandlers.set('STATUS_CHANGE', this.handleStatusChange.bind(this));
    this.eventHandlers.set('STAGE_TRANSITION', this.handleStageTransition.bind(this));
    this.eventHandlers.set('DOCUMENT_REQUIRED', this.handleDocumentRequired.bind(this));
    this.eventHandlers.set('DEADLINE_APPROACHING', this.handleDeadlineApproaching.bind(this));
    this.eventHandlers.set('APPROVAL_REQUIRED', this.handleApprovalRequired.bind(this));
    this.eventHandlers.set('INTERVIEW_SCHEDULED', this.handleInterviewScheduled.bind(this));
    this.eventHandlers.set('SITE_VISIT_SCHEDULED', this.handleSiteVisitScheduled.bind(this));
  }

  /**
   * Process application event and trigger notifications
   * @param {Object} event - Application event data
   * @param {String} triggeredBy - User ID who triggered the event
   * @returns {Promise<Object>} Processing result
   */
  async processEvent(event, triggeredBy) {
    try {
      console.log(`Processing application event: ${event.eventType} for application ${event.applicationId}`);

      // Validate event data
      if (!event.applicationId || !event.eventType) {
        throw new Error('Application ID and event type are required');
      }

      // Get application details
      const application = await Application.findOne({ id: event.applicationId })
        .populate('programmeId')
        .populate('corporateSponsorId')
        .lean();

      if (!application) {
        throw new Error(`Application ${event.applicationId} not found`);
      }

      // Get event handler
      const handler = this.eventHandlers.get(event.eventType);
      if (!handler) {
        throw new Error(`No handler found for event type: ${event.eventType}`);
      }

      // Process the event
      const result = await handler(event, application, triggeredBy);

      // Emit real-time event via WebSocket
      WebSocketService.emitApplicationEvent(event.applicationId, {
        eventType: event.eventType,
        eventData: event.eventData,
        timestamp: event.timestamp || new Date(),
        applicationId: event.applicationId
      });

      return {
        success: true,
        message: 'Event processed successfully',
        notificationsSent: result.notificationsSent || 0,
        recipients: result.recipients || []
      };
    } catch (error) {
      console.error('Error processing application event:', error);
      throw error;
    }
  }

  /**
   * Handle application status change event
   */
  async handleStatusChange(event, application, triggeredBy) {
    const { oldStatus, newStatus } = event.eventData;
    
    // Find appropriate template
    const template = await NotificationTemplate.findOne({
      type: 'APPLICATION_STATUS_CHANGE',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for APPLICATION_STATUS_CHANGE');
      return { notificationsSent: 0, recipients: [] };
    }

    // Prepare template variables
    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      oldStatus: this.formatStatus(oldStatus),
      newStatus: this.formatStatus(newStatus),
      programmeName: application.programmeId?.name || 'N/A',
      sponsorName: application.corporateSponsorId?.name || 'N/A',
      statusChangeDate: new Date().toLocaleDateString(),
      dashboardUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}`
    };

    // Determine target users (applicant and relevant staff)
    const targetUsers = await this.getTargetUsers(application, 'STATUS_CHANGE');

    // Create notification
    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: this.getStatusChangePriority(newStatus),
        relatedEntity: {
          entityType: 'APPLICATION',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            businessName: variables.businessName,
            status: newStatus
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle stage transition event
   */
  async handleStageTransition(event, application, triggeredBy) {
    const { oldStage, newStage } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'APPLICATION_STATUS_CHANGE', // Reuse status change template
      isActive: true
    });

    if (!template) {
      console.warn('No template found for stage transition');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      oldStage: this.formatStage(oldStage),
      newStage: this.formatStage(newStage),
      programmeName: application.programmeId?.name || 'N/A',
      sponsorName: application.corporateSponsorId?.name || 'N/A',
      transitionDate: new Date().toLocaleDateString(),
      dashboardUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}`
    };

    const targetUsers = await this.getTargetUsers(application, 'STAGE_TRANSITION');

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: 'MEDIUM',
        relatedEntity: {
          entityType: 'APPLICATION',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            businessName: variables.businessName,
            currentStage: newStage
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle document required event
   */
  async handleDocumentRequired(event, application, triggeredBy) {
    const { documentType, deadline } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'DOCUMENT_REQUIRED',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for DOCUMENT_REQUIRED');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      documentType: documentType,
      deadline: deadline ? new Date(deadline).toLocaleDateString() : 'Not specified',
      programmeName: application.programmeId?.name || 'N/A',
      uploadUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}/documents`
    };

    // Primarily target the applicant
    const targetUsers = await this.getApplicantUsers(application);

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: 'HIGH',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: true,
          push: true
        },
        relatedEntity: {
          entityType: 'DOCUMENT',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            documentType: documentType,
            deadline: deadline
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle deadline approaching event
   */
  async handleDeadlineApproaching(event, application, triggeredBy) {
    const { deadlineType, deadline, daysRemaining } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'DEADLINE_REMINDER',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for DEADLINE_REMINDER');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      deadlineType: deadlineType,
      deadline: new Date(deadline).toLocaleDateString(),
      daysRemaining: daysRemaining,
      urgencyLevel: daysRemaining <= 3 ? 'URGENT' : daysRemaining <= 7 ? 'HIGH' : 'MEDIUM',
      actionUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}`
    };

    const targetUsers = await this.getApplicantUsers(application);

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: daysRemaining <= 3 ? 'URGENT' : 'HIGH',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: daysRemaining <= 3,
          push: true
        },
        relatedEntity: {
          entityType: 'APPLICATION',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            deadlineType: deadlineType,
            deadline: deadline,
            daysRemaining: daysRemaining
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle approval required event
   */
  async handleApprovalRequired(event, application, triggeredBy) {
    const { approvalType, assignedTo } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'APPROVAL_REQUIRED',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for APPROVAL_REQUIRED');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      approvalType: approvalType,
      programmeName: application.programmeId?.name || 'N/A',
      requestedAmount: application.financialInfo?.requestedAmount || application.fundingAmount || 'N/A',
      reviewUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}/review`
    };

    // Target specific approvers or get approver role users
    let targetUsers = [];
    if (assignedTo && assignedTo.length > 0) {
      targetUsers = await User.find({
        _id: { $in: assignedTo },
        isActive: true
      });
    } else {
      targetUsers = await this.getApproverUsers(application);
    }

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: 'HIGH',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: false,
          push: true
        },
        relatedEntity: {
          entityType: 'APPLICATION',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            approvalType: approvalType,
            businessName: variables.businessName
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle interview scheduled event
   */
  async handleInterviewScheduled(event, application, triggeredBy) {
    const { interviewType, scheduledDate, participants } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'INTERVIEW_SCHEDULED',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for INTERVIEW_SCHEDULED');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      interviewType: interviewType,
      scheduledDate: new Date(scheduledDate).toLocaleDateString(),
      scheduledTime: new Date(scheduledDate).toLocaleTimeString(),
      programmeName: application.programmeId?.name || 'N/A',
      interviewUrl: `${process.env.FRONTEND_URL || 'http://localhost:4200'}/applications/${application.id}/interview`
    };

    // Target applicant and participants
    const applicantUsers = await this.getApplicantUsers(application);
    let participantUsers = [];
    if (participants && participants.length > 0) {
      participantUsers = await User.find({
        _id: { $in: participants },
        isActive: true
      });
    }

    const targetUsers = [...applicantUsers, ...participantUsers];

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: 'HIGH',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: true,
          push: true
        },
        relatedEntity: {
          entityType: 'INTERVIEW',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            interviewType: interviewType,
            scheduledDate: scheduledDate
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Handle site visit scheduled event
   */
  async handleSiteVisitScheduled(event, application, triggeredBy) {
    const { scheduledDate, visitType, participants } = event.eventData;
    
    const template = await NotificationTemplate.findOne({
      type: 'SITE_VISIT_SCHEDULED',
      isActive: true
    });

    if (!template) {
      console.warn('No template found for SITE_VISIT_SCHEDULED');
      return { notificationsSent: 0, recipients: [] };
    }

    const variables = {
      applicationId: application.id,
      applicantName: `${application.personalInfo?.firstName || ''} ${application.personalInfo?.lastName || ''}`.trim(),
      businessName: application.businessInfo?.legalName || 'N/A',
      visitType: visitType,
      scheduledDate: new Date(scheduledDate).toLocaleDateString(),
      scheduledTime: new Date(scheduledDate).toLocaleTimeString(),
      businessAddress: this.formatAddress(application.businessInfo?.address),
      programmeName: application.programmeId?.name || 'N/A'
    };

    // Target applicant and participants
    const applicantUsers = await this.getApplicantUsers(application);
    let participantUsers = [];
    if (participants && participants.length > 0) {
      participantUsers = await User.find({
        _id: { $in: participants },
        isActive: true
      });
    }

    const targetUsers = [...applicantUsers, ...participantUsers];

    const notification = await NotificationService.createFromTemplate(
      template._id,
      variables,
      {
        targetAudience: 'SPECIFIC_USERS',
        targetUsers: targetUsers.map(user => user._id),
        priority: 'HIGH',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: true,
          push: true
        },
        relatedEntity: {
          entityType: 'SITE_VISIT',
          entityId: application.id,
          entityData: {
            applicationId: application.id,
            visitType: visitType,
            scheduledDate: scheduledDate
          }
        }
      },
      triggeredBy
    );

    return {
      notificationsSent: 1,
      recipients: targetUsers.map(user => ({
        userId: user._id,
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
      }))
    };
  }

  /**
   * Get target users based on application and event type
   */
  async getTargetUsers(application, eventType) {
    const applicantUsers = await this.getApplicantUsers(application);
    
    switch (eventType) {
      case 'STATUS_CHANGE':
      case 'STAGE_TRANSITION':
        // Include applicant and relevant staff
        const staffUsers = await this.getRelevantStaffUsers(application);
        return [...applicantUsers, ...staffUsers];
      
      case 'DOCUMENT_REQUIRED':
      case 'DEADLINE_APPROACHING':
        // Primarily applicant
        return applicantUsers;
      
      case 'APPROVAL_REQUIRED':
        // Approvers
        return await this.getApproverUsers(application);
      
      default:
        return applicantUsers;
    }
  }

  /**
   * Get applicant users for an application
   */
  async getApplicantUsers(application) {
    // This would typically be based on the application's user association
    // For now, we'll use a simple approach based on email or user ID
    const users = [];
    
    if (application.personalInfo?.email) {
      const user = await User.findOne({
        email: application.personalInfo.email,
        isActive: true
      });
      if (user) users.push(user);
    }

    return users;
  }

  /**
   * Get relevant staff users for an application
   */
  async getRelevantStaffUsers(application) {
    return await User.find({
      $or: [
        { roles: { $in: ['analyst', 'reviewer'] } },
        { role: { $in: ['analyst', 'reviewer'] } }
      ],
      isActive: true
    }).limit(5); // Limit to avoid too many notifications
  }

  /**
   * Get approver users for an application
   */
  async getApproverUsers(application) {
    return await User.find({
      $or: [
        { roles: { $in: ['approver', 'manager', 'admin'] } },
        { role: { $in: ['approver', 'manager', 'admin'] } }
      ],
      isActive: true
    }).limit(10);
  }

  /**
   * Format status for display
   */
  formatStatus(status) {
    if (!status) return 'Unknown';
    
    return status
      .replace(/[_-]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Format stage for display
   */
  formatStage(stage) {
    if (!stage || typeof stage !== 'object') return 'Unknown';
    
    const mainStage = stage.mainStage || stage.currentMainStage || 'Unknown';
    const subStage = stage.subStage || stage.currentSubStage || '';
    
    let formatted = this.formatStatus(mainStage);
    if (subStage) {
      formatted += ` - ${this.formatStatus(subStage)}`;
    }
    
    return formatted;
  }

  /**
   * Format address for display
   */
  formatAddress(address) {
    if (!address) return 'Address not provided';
    
    const parts = [
      address.street,
      address.city,
      address.province,
      address.postalCode
    ].filter(Boolean);
    
    return parts.join(', ') || 'Address not provided';
  }

  /**
   * Get priority level for status changes
   */
  getStatusChangePriority(status) {
    const highPriorityStatuses = ['approved', 'rejected', 'withdrawn'];
    const mediumPriorityStatuses = ['in-review', 'pending'];
    
    if (highPriorityStatuses.includes(status?.toLowerCase())) {
      return 'HIGH';
    } else if (mediumPriorityStatuses.includes(status?.toLowerCase())) {
      return 'MEDIUM';
    }
    
    return 'LOW';
  }

  /**
   * Bulk process multiple events
   */
  async bulkProcessEvents(events, triggeredBy) {
    const results = [];
    
    for (const event of events) {
      try {
        const result = await this.processEvent(event, triggeredBy);
        results.push({
          eventId: event.id || `${event.applicationId}-${event.eventType}`,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          eventId: event.id || `${event.applicationId}-${event.eventType}`,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Get event statistics
   */
  async getEventStatistics(applicationIds) {
    // This would typically query a dedicated events table
    // For now, return mock statistics
    return {
      totalEvents: applicationIds.length * 5,
      eventsByType: {
        STATUS_CHANGE: applicationIds.length * 2,
        STAGE_TRANSITION: applicationIds.length * 2,
        DOCUMENT_REQUIRED: applicationIds.length * 1
      },
      notificationsSent: applicationIds.length * 8,
      lastProcessed: new Date()
    };
  }
}

module.exports = new ApplicationEventService();