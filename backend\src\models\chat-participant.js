const mongoose = require('mongoose');

const chatParticipantSchema = new mongoose.Schema({
  roomId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatRoom',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  role: {
    type: String,
    enum: ['ADMIN', 'MODERATOR', 'MEMBER'],
    default: 'MEMBER'
  },
  // Permissions
  permissions: {
    canSendMessages: {
      type: Boolean,
      default: true
    },
    canDeleteOwnMessages: {
      type: Boolean,
      default: true
    },
    canDeleteOthersMessages: {
      type: Boolean,
      default: false
    },
    canAddParticipants: {
      type: Boolean,
      default: false
    },
    canRemoveParticipants: {
      type: Boolean,
      default: false
    },
    canEditRoomInfo: {
      type: Boolean,
      default: false
    }
  },
  // User preferences for this room
  preferences: {
    muteNotifications: {
      type: Boolean,
      default: false
    },
    notificationSound: {
      type: Boolean,
      default: true
    },
    showMessagePreview: {
      type: Boolean,
      default: true
    }
  },
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isBanned: {
    type: Boolean,
    default: false
  },
  bannedReason: String,
  bannedAt: Date,
  bannedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Activity tracking
  joinedAt: {
    type: Date,
    default: Date.now
  },
  leftAt: Date,
  lastSeenAt: {
    type: Date,
    default: Date.now
  },
  lastReadMessageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatMessage'
  },
  unreadCount: {
    type: Number,
    default: 0
  },
  // Metadata
  addedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index for unique participant per room
chatParticipantSchema.index({ roomId: 1, userId: 1 }, { unique: true });
chatParticipantSchema.index({ userId: 1, isActive: 1 });
chatParticipantSchema.index({ roomId: 1, isActive: 1 });
chatParticipantSchema.index({ userId: 1, unreadCount: 1 });

// Pre-save middleware
chatParticipantSchema.pre('save', function(next) {
  // Update permissions based on role
  if (this.isModified('role')) {
    switch (this.role) {
      case 'ADMIN':
        this.permissions = {
          canSendMessages: true,
          canDeleteOwnMessages: true,
          canDeleteOthersMessages: true,
          canAddParticipants: true,
          canRemoveParticipants: true,
          canEditRoomInfo: true
        };
        break;
      case 'MODERATOR':
        this.permissions = {
          canSendMessages: true,
          canDeleteOwnMessages: true,
          canDeleteOthersMessages: true,
          canAddParticipants: true,
          canRemoveParticipants: false,
          canEditRoomInfo: false
        };
        break;
      case 'MEMBER':
      default:
        // Keep default permissions
        break;
    }
  }
  
  this.updatedAt = Date.now();
  next();
});

// Method to update last seen
chatParticipantSchema.methods.updateLastSeen = async function() {
  this.lastSeenAt = new Date();
  await this.save();
};

// Method to mark messages as read
chatParticipantSchema.methods.markMessagesAsRead = async function(lastMessageId) {
  this.lastReadMessageId = lastMessageId;
  this.unreadCount = 0;
  this.lastSeenAt = new Date();
  await this.save();
};

// Method to increment unread count
chatParticipantSchema.methods.incrementUnreadCount = async function() {
  this.unreadCount += 1;
  await this.save();
};

// Static method to get user's active rooms
chatParticipantSchema.statics.getUserRooms = async function(userId, includeArchived = false) {
  const query = {
    userId: userId,
    isActive: true,
    isBanned: false
  };
  
  const participants = await this.find(query)
    .populate({
      path: 'roomId',
      match: includeArchived ? {} : { isArchived: false },
      populate: {
        path: 'lastMessage',
        populate: {
          path: 'senderId',
          select: 'username firstName lastName profilePicture'
        }
      }
    })
    .sort('-roomId.lastActivity');
  
  // Filter out null rooms (archived if not included)
  return participants.filter(p => p.roomId !== null);
};

// Static method to get room participants
chatParticipantSchema.statics.getRoomParticipants = async function(roomId, activeOnly = true) {
  const query = { roomId: roomId };
  if (activeOnly) {
    query.isActive = true;
    query.isBanned = false;
  }
  
  return await this.find(query)
    .populate('userId', 'username email firstName lastName profilePicture role organizationType')
    .sort('joinedAt');
};

const ChatParticipant = mongoose.model('ChatParticipant', chatParticipantSchema);

module.exports = ChatParticipant;