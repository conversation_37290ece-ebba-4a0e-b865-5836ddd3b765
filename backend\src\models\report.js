const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const reportColumnSchema = new Schema({
  field: { type: String, required: true },
  header: { type: String, required: true },
  dataType: { 
    type: String, 
    enum: ['string', 'number', 'date', 'boolean', 'currency'],
    default: 'string'
  },
  sortable: { type: Boolean, default: true },
  filterable: { type: Boolean, default: true },
  visible: { type: Boolean, default: true },
  width: { type: String },
  format: { type: String }
});

const reportFilterSchema = new Schema({
  startDate: { type: Date },
  endDate: { type: Date },
  programmeId: { type: Schema.Types.ObjectId, ref: 'FundingProgramme' },
  corporateSponsorId: { type: Schema.Types.ObjectId, ref: 'CorporateSponsor' },
  status: { type: String },
  region: { type: String },
  fundingAmount: {
    min: { type: Number },
    max: { type: Number }
  },
  customFields: { type: Map, of: Schema.Types.Mixed }
});

const reportDataSchema = new Schema({
  columns: [reportColumnSchema],
  rows: [{ type: Schema.Types.Mixed }],
  summary: { type: Map, of: Schema.Types.Mixed }
});

const reportSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  type: { 
    type: String, 
    enum: ['status', 'processing-time', 'approval-rates', 'funding-distribution', 'regional-analysis', 'custom'],
    required: true
  },
  filters: { 
    type: reportFilterSchema,
    default: {}
  },
  columns: { 
    type: [reportColumnSchema],
    default: []
  },
  data: { 
    type: reportDataSchema
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  lastGeneratedAt: { 
    type: Date
  },
  status: { 
    type: String, 
    enum: ['draft', 'generated', 'scheduled', 'error'],
    default: 'draft'
  },
  format: { 
    type: String, 
    enum: ['pdf', 'excel', 'csv'],
    default: 'pdf'
  },
  isTemplate: { 
    type: Boolean,
    default: false
  },
  isPublic: { 
    type: Boolean,
    default: false
  },
  tags: [{ 
    type: String,
    trim: true
  }]
}, { timestamps: true });

// Add text index for search functionality
reportSchema.index({ 
  name: 'text', 
  description: 'text', 
  tags: 'text'
});

// Virtual for URL
reportSchema.virtual('url').get(function() {
  return `/reports/${this._id}`;
});

module.exports = mongoose.model('Report', reportSchema);
