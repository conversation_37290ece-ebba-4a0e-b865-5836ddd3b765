# MongoDB One-Liner Fix for APP-2025-001

## EASIEST FIX - Copy and paste this single command into MongoDB shell:

```javascript
db.applications.updateOne({id: "APP-2025-001"}, {$set: {stageHierarchy: [{mainStage: "ONBOARDING", status: "NOT_STARTED", subStages: [{subStage: "BENEFICIARY_REGISTRATION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "PRE_SCREENING", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "BUSINESS_CASE_REVIEW", status: "NOT_STARTED", subStages: [{subStage: "DOCUMENT_COLLECTION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "DESKTOP_ANALYSIS", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "DUE_DILIGENCE", status: "NOT_STARTED", subStages: [{subStage: "DATA_VALIDATION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "SME_INTERVIEW", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "SITE_VISIT", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "ASSESSMENT_REPORT", status: "NOT_STARTED", subStages: [{subStage: "REPORT_COMPLETION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "REPORT_QUALITY_CHECK", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "REPORT_REVIEW", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "APPLICATION_APPROVAL", status: "NOT_STARTED", subStages: [{subStage: "CORPORATE_APPROVAL_1", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_2", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_3", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_4", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}], currentMainStage: "ONBOARDING", currentSubStage: "BENEFICIARY_REGISTRATION", currentStageStatus: "NOT_STARTED"}})
```

## USAGE:

1. Connect to MongoDB:
   ```bash
   mongo your-database-name
   ```

2. Copy and paste the one-liner command above and press Enter

3. You should see output like:
   ```
   { "acknowledged" : true, "matchedCount" : 1, "modifiedCount" : 1 }
   ```

4. Refresh the application page - the Stage Manager tab should now work!

## Alternative - Use the simple script:

```bash
mongo your-database-name fix-app-2025-001-simple.js
```

## Verify the fix worked:

```javascript
db.applications.findOne({id: "APP-2025-001"}, {stageHierarchy: 1, currentMainStage: 1, currentSubStage: 1})
```

This should show the stage hierarchy is now populated.
