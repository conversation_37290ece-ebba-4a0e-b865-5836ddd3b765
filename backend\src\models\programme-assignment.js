const mongoose = require('mongoose');

const programmeAssignmentSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  programmeId: {
    type: String,
    required: true,
    ref: 'FundingProgramme'
  },
  assigneeType: {
    type: String,
    enum: ['user', 'serviceProvider'],
    required: true
  },
  assigneeId: {
    type: String,
    required: true
  },
  role: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  assignedBy: String,
  assignedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to generate programme assignment ID
programmeAssignmentSchema.pre('save', async function(next) {
  if (!this.id) {
    const count = await this.constructor.countDocuments();
    const nextNumber = (count + 1).toString().padStart(3, '0');
    this.id = `PA-2025-${nextNumber}`;
  }
  this.updatedAt = Date.now();
  next();
});

const ProgrammeAssignment = mongoose.model('ProgrammeAssignment', programmeAssignmentSchema);

module.exports = ProgrammeAssignment;
