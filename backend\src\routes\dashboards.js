const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Dashboard = require('../models/dashboard');
const Report = require('../models/report');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Get all dashboards
router.get('/', authenticateToken, async (req, res) => {
  try {
    const query = {};
    
    // Handle public filter
    if (req.query.isPublic !== undefined) {
      query.isPublic = req.query.isPublic === 'true';
    }
    
    // Add user filter if not admin
    if (!req.user.roles.includes('admin')) {
      query.$or = [
        { createdBy: req.user.id },
        { isPublic: true }
      ];
    }
    
    const dashboards = await Dashboard.find(query)
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(parseInt(req.query.limit) || 100)
      .skip(parseInt(req.query.skip) || 0);
    
    const total = await Dashboard.countDocuments(query);
    
    res.json({
      dashboards,
      total,
      limit: parseInt(req.query.limit) || 100,
      skip: parseInt(req.query.skip) || 0
    });
  } catch (err) {
    console.error('Error fetching dashboards:', err);
    res.status(500).json({ message: 'Error fetching dashboards', error: err.message });
  }
});

// Get default dashboard
router.get('/default', authenticateToken, async (req, res) => {
  try {
    // Try to find user's default dashboard
    let dashboard = await Dashboard.findOne({
      createdBy: req.user.id,
      isDefault: true
    }).populate('createdBy', 'firstName lastName');
    
    // If no default dashboard found, try to find any dashboard created by the user
    if (!dashboard) {
      dashboard = await Dashboard.findOne({
        createdBy: req.user.id
      }).populate('createdBy', 'firstName lastName');
    }
    
    // If still no dashboard found, try to find a public dashboard
    if (!dashboard) {
      dashboard = await Dashboard.findOne({
        isPublic: true
      }).populate('createdBy', 'firstName lastName');
    }
    
    if (!dashboard) {
      return res.status(404).json({ message: 'No dashboard found' });
    }
    
    res.json(dashboard);
  } catch (err) {
    console.error('Error fetching default dashboard:', err);
    res.status(500).json({ message: 'Error fetching default dashboard', error: err.message });
  }
});

// Get a specific dashboard by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id)
      .populate('createdBy', 'firstName lastName')
      .populate('widgets.reportId', 'name type');
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has access to this dashboard
    if (!dashboard.isPublic && dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to access this dashboard' });
    }
    
    res.json(dashboard);
  } catch (err) {
    console.error('Error fetching dashboard:', err);
    res.status(500).json({ message: 'Error fetching dashboard', error: err.message });
  }
});

// Create a new dashboard
router.post('/', authenticateToken, async (req, res) => {
  try {
    const newDashboard = new Dashboard({
      ...req.body,
      createdBy: req.user.id
    });
    
    const savedDashboard = await newDashboard.save();
    
    res.status(201).json(savedDashboard);
  } catch (err) {
    console.error('Error creating dashboard:', err);
    res.status(400).json({ message: 'Error creating dashboard', error: err.message });
  }
});

// Update an existing dashboard
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // Update the dashboard
    const updatedDashboard = await Dashboard.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    res.json(updatedDashboard);
  } catch (err) {
    console.error('Error updating dashboard:', err);
    res.status(400).json({ message: 'Error updating dashboard', error: err.message });
  }
});

// Delete a dashboard
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to delete this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to delete this dashboard' });
    }
    
    await Dashboard.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Dashboard deleted successfully' });
  } catch (err) {
    console.error('Error deleting dashboard:', err);
    res.status(500).json({ message: 'Error deleting dashboard', error: err.message });
  }
});

// Set a dashboard as default
router.put('/:id/set-default', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // Set as default
    dashboard.isDefault = true;
    
    const updatedDashboard = await dashboard.save();
    
    res.json(updatedDashboard);
  } catch (err) {
    console.error('Error setting default dashboard:', err);
    res.status(500).json({ message: 'Error setting default dashboard', error: err.message });
  }
});

// Add a widget to a dashboard
router.post('/:id/widgets', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // If widget has a report ID, verify it exists
    if (req.body.reportId) {
      const reportExists = await Report.exists({ _id: req.body.reportId });
      if (!reportExists) {
        return res.status(400).json({ message: 'Report not found' });
      }
    }
    
    // Add widget
    const widget = {
      ...req.body,
      createdBy: req.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    dashboard.widgets.push(widget);
    
    const updatedDashboard = await dashboard.save();
    
    res.status(201).json({
      message: 'Widget added successfully',
      dashboard: updatedDashboard,
      widget: updatedDashboard.widgets[updatedDashboard.widgets.length - 1]
    });
  } catch (err) {
    console.error('Error adding widget:', err);
    res.status(400).json({ message: 'Error adding widget', error: err.message });
  }
});

// Update a widget
router.put('/:id/widgets/:widgetId', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // Find widget
    const widgetIndex = dashboard.widgets.findIndex(w => w._id.toString() === req.params.widgetId);
    
    if (widgetIndex === -1) {
      return res.status(404).json({ message: 'Widget not found' });
    }
    
    // If widget has a report ID, verify it exists
    if (req.body.reportId && req.body.reportId !== dashboard.widgets[widgetIndex].reportId?.toString()) {
      const reportExists = await Report.exists({ _id: req.body.reportId });
      if (!reportExists) {
        return res.status(400).json({ message: 'Report not found' });
      }
    }
    
    // Update widget
    dashboard.widgets[widgetIndex] = {
      ...dashboard.widgets[widgetIndex].toObject(),
      ...req.body,
      updatedAt: new Date()
    };
    
    const updatedDashboard = await dashboard.save();
    
    res.json({
      message: 'Widget updated successfully',
      dashboard: updatedDashboard,
      widget: updatedDashboard.widgets[widgetIndex]
    });
  } catch (err) {
    console.error('Error updating widget:', err);
    res.status(400).json({ message: 'Error updating widget', error: err.message });
  }
});

// Delete a widget
router.delete('/:id/widgets/:widgetId', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // Find widget
    const widgetIndex = dashboard.widgets.findIndex(w => w._id.toString() === req.params.widgetId);
    
    if (widgetIndex === -1) {
      return res.status(404).json({ message: 'Widget not found' });
    }
    
    // Remove widget
    dashboard.widgets.splice(widgetIndex, 1);
    
    const updatedDashboard = await dashboard.save();
    
    res.json({
      message: 'Widget deleted successfully',
      dashboard: updatedDashboard
    });
  } catch (err) {
    console.error('Error deleting widget:', err);
    res.status(500).json({ message: 'Error deleting widget', error: err.message });
  }
});

// Update widget positions
router.put('/:id/widget-positions', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to update this dashboard
    if (dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this dashboard' });
    }
    
    // Update positions
    const { positions } = req.body;
    
    if (!positions || !Array.isArray(positions)) {
      return res.status(400).json({ message: 'Positions array is required' });
    }
    
    // Update each widget position
    positions.forEach(positionUpdate => {
      const widgetIndex = dashboard.widgets.findIndex(w => w._id.toString() === positionUpdate.id);
      
      if (widgetIndex !== -1) {
        dashboard.widgets[widgetIndex].position = positionUpdate.position;
      }
    });
    
    const updatedDashboard = await dashboard.save();
    
    res.json({
      message: 'Widget positions updated successfully',
      dashboard: updatedDashboard
    });
  } catch (err) {
    console.error('Error updating widget positions:', err);
    res.status(400).json({ message: 'Error updating widget positions', error: err.message });
  }
});

// Refresh widget data
router.post('/:id/widgets/:widgetId/refresh', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Find widget
    const widgetIndex = dashboard.widgets.findIndex(w => w._id.toString() === req.params.widgetId);
    
    if (widgetIndex === -1) {
      return res.status(404).json({ message: 'Widget not found' });
    }
    
    const widget = dashboard.widgets[widgetIndex];
    
    // Generate widget data
    let widgetData;
    
    if (widget.reportId) {
      // If widget is based on a report, get report data
      const report = await Report.findById(widget.reportId);
      
      if (!report) {
        return res.status(404).json({ message: 'Report not found' });
      }
      
      // If report is not generated, generate it
      if (report.status !== 'generated' || !report.data) {
        // Generate report data based on type
        let reportData;
        
        switch (report.type) {
          case 'status':
            reportData = await generateStatusReport(report.filters);
            break;
          case 'processing-time':
            reportData = await generateProcessingTimeReport(report.filters);
            break;
          case 'approval-rates':
            reportData = await generateApprovalRatesReport(report.filters);
            break;
          case 'funding-distribution':
            reportData = await generateFundingDistributionReport(report.filters);
            break;
          case 'regional-analysis':
            reportData = await generateRegionalAnalysisReport(report.filters);
            break;
          case 'custom':
            reportData = await generateCustomReport(report.filters, report.columns);
            break;
          default:
            return res.status(400).json({ message: 'Invalid report type' });
        }
        
        // Update report with generated data
        report.data = reportData;
        report.status = 'generated';
        report.lastGeneratedAt = new Date();
        
        await report.save();
      }
      
      // Transform report data to widget data
      widgetData = transformReportDataToWidgetData(report.data, widget.type, widget.config);
    } else {
      // If widget is not based on a report, generate data directly
      widgetData = await generateWidgetData(widget.type, widget.filters, widget.config);
    }
    
    // Update widget data
    dashboard.widgets[widgetIndex].data = widgetData;
    dashboard.widgets[widgetIndex].lastRefreshed = new Date();
    
    const updatedDashboard = await dashboard.save();
    
    res.json({
      message: 'Widget refreshed successfully',
      widget: updatedDashboard.widgets[widgetIndex]
    });
  } catch (err) {
    console.error('Error refreshing widget:', err);
    res.status(500).json({ message: 'Error refreshing widget', error: err.message });
  }
});

// Clone a dashboard
router.post('/:id/clone', authenticateToken, async (req, res) => {
  try {
    const dashboard = await Dashboard.findById(req.params.id);
    
    if (!dashboard) {
      return res.status(404).json({ message: 'Dashboard not found' });
    }
    
    // Check if user has permission to clone this dashboard
    if (!dashboard.isPublic && dashboard.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to clone this dashboard' });
    }
    
    // Create a new dashboard based on the original
    const newDashboard = new Dashboard({
      name: req.body.name || `${dashboard.name} (Copy)`,
      description: dashboard.description,
      widgets: dashboard.widgets,
      layout: dashboard.layout,
      isDefault: false,
      isPublic: false,
      createdBy: req.user.id
    });
    
    const savedDashboard = await newDashboard.save();
    
    res.status(201).json(savedDashboard);
  } catch (err) {
    console.error('Error cloning dashboard:', err);
    res.status(500).json({ message: 'Error cloning dashboard', error: err.message });
  }
});

// Helper functions for widget data generation
// These are placeholders and would be implemented similarly to the ones in reports.js
async function generateStatusReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateProcessingTimeReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateApprovalRatesReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateFundingDistributionReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateRegionalAnalysisReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateCustomReport(filters, columns) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns, rows: [] };
}

// Helper function to transform report data to widget data
function transformReportDataToWidgetData(reportData, widgetType, widgetConfig) {
  // This would transform report data to the format expected by the widget
  // This is a placeholder implementation
  
  if (widgetType === 'chart') {
    // Transform to chart data
    const series = [];
    
    if (reportData.rows && reportData.rows.length > 0) {
      // For simple charts, use the first column as labels and second column as values
      if (reportData.columns.length >= 2) {
        const labelField = reportData.columns[0].field;
        const valueField = reportData.columns[1].field;
        
        const dataPoints = reportData.rows.map(row => ({
          label: row[labelField],
          value: row[valueField]
        }));
        
        series.push({
          name: reportData.columns[1].header,
          data: dataPoints
        });
      }
    }
    
    return { series };
  } else if (widgetType === 'metric') {
    // Transform to metric data
    let singleValue = null;
    let trend = null;
    let trendDirection = 'neutral';
    
    if (reportData.summary) {
      // Use the first summary value as the metric
      const firstKey = Object.keys(reportData.summary)[0];
      if (firstKey) {
        singleValue = reportData.summary[firstKey];
      }
    }
    
    return { singleValue, trend, trendDirection };
  } else if (widgetType === 'table') {
    // Transform to table data
    return {
      tableData: {
        columns: reportData.columns.map(col => ({
          field: col.field,
          header: col.header
        })),
        rows: reportData.rows
      }
    };
  }
  
  return {};
}

// Helper function to generate widget data directly
async function generateWidgetData(widgetType, filters, config) {
  // This would generate data for widgets that are not based on reports
  // This is a placeholder implementation
  
  if (widgetType === 'chart') {
    // Generate chart data
    const dataPoints = [
      { label: 'Category 1', value: 30 },
      { label: 'Category 2', value: 50 },
      { label: 'Category 3', value: 20 }
    ];
    
    return {
      series: [{
        name: 'Sample Data',
        data: dataPoints
      }]
    };
  } else if (widgetType === 'metric') {
    // Generate metric data
    return {
      singleValue: 75,
      trend: 5,
      trendDirection: 'up'
    };
  } else if (widgetType === 'table') {
    // Generate table data
    return {
      tableData: {
        columns: [
          { field: 'name', header: 'Name' },
          { field: 'value', header: 'Value' }
        ],
        rows: [
          { name: 'Item 1', value: 10 },
          { name: 'Item 2', value: 20 },
          { name: 'Item 3', value: 30 }
        ]
      }
    };
  }
  
  return {};
}

module.exports = router;
