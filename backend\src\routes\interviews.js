const express = require('express');
const router = express.Router();
const Interview = require('../models/interview');
const { authenticateToken } = require('../middleware/auth');

// Get all interviews with pagination
router.get('/', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const interviews = await Interview.find()
      .sort({ scheduledDate: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Interview.countDocuments();
    
    res.json({
      success: true,
      data: interviews,
      total: total,
      page: page,
      limit: limit
    });
  } catch (error) {
    console.error('Error fetching interviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interviews',
      error: error.message
    });
  }
});

// Handle scheduler route specifically to avoid treating it as an ID
router.get('/scheduler', async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Interviews scheduler endpoint'
    });
  } catch (error) {
    console.error('Error accessing interviews scheduler:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to access interviews scheduler',
      error: error.message
    });
  }
});

// Get interviews for a specific application
router.get('/application/:applicationId', async (req, res) => {
  try {
    const applicationId = req.params.applicationId;
    const interviews = await Interview.find({ applicationId });
    
    res.json({
      success: true,
      data: interviews
    });
  } catch (error) {
    console.error(`Error fetching interviews for application ${req.params.applicationId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interviews for application',
      error: error.message
    });
  }
});

// Get a specific interview by ID
router.get('/:id', async (req, res) => {
  try {
    const interview = await Interview.findById(req.params.id);
    
    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }
    
    res.json({
      success: true,
      data: interview
    });
  } catch (error) {
    console.error(`Error fetching interview ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interview',
      error: error.message
    });
  }
});

// Create a new interview
router.post('/', async (req, res) => {
  try {
    const interview = new Interview(req.body);
    const savedInterview = await interview.save();
    
    res.status(201).json({
      success: true,
      data: savedInterview
    });
  } catch (error) {
    console.error('Error creating interview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create interview',
      error: error.message
    });
  }
});

// Update an interview
router.put('/:id', async (req, res) => {
  try {
    const interview = await Interview.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }
    
    res.json({
      success: true,
      data: interview
    });
  } catch (error) {
    console.error(`Error updating interview ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to update interview',
      error: error.message
    });
  }
});

// Delete an interview
router.delete('/:id', async (req, res) => {
  try {
    const interview = await Interview.findByIdAndDelete(req.params.id);
    
    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Interview deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting interview ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete interview',
      error: error.message
    });
  }
});


module.exports = router;
