const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const approvalStepSchema = new Schema({
  step: { 
    type: String,
    required: true
  },
  status: { 
    type: String, 
    enum: ['pending', 'in-progress', 'completed', 'rejected'],
    default: 'pending'
  },
  assignedTo: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  startDate: { type: Date },
  completionDate: { type: Date },
  notes: { type: String },
  attachments: [{ type: String }]
});

const committeeMeetingReferenceSchema = new Schema({
  meetingId: { 
    type: Schema.Types.ObjectId, 
    ref: 'CommitteeMeeting',
    required: true
  },
  status: { 
    type: String, 
    enum: ['scheduled', 'presented', 'approved', 'rejected', 'deferred'],
    default: 'scheduled'
  },
  presentationDate: { type: Date },
  notes: { type: String }
});

const approvalWorkflowSchema = new Schema({
  applicationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Application',
    required: true
  },
  programmeId: { 
    type: Schema.Types.ObjectId, 
    ref: 'FundingProgramme',
    required: true
  },
  currentStep: { 
    type: String, 
    enum: [
      'ANALYST_REPORT',
      'INSIGHT_MANAGER_REVIEW',
      'CORPORATE_MANAGER_REVIEW',
      'COMMITTEE_SCHEDULING',
      'COMMITTEE_SUBMISSION',
      'COMMITTEE_MEETING',
      'RECOMMENDATION',
      'FINAL_APPROVAL',
      'AWARD_LETTER',
      'CONDITIONS_FULFILLMENT',
      'AGREEMENT_SIGNING',
      'DISBURSEMENT',
      'RECEIPT_CONFIRMATION'
    ],
    default: 'ANALYST_REPORT'
  },
  steps: [approvalStepSchema],
  committeeMeetings: [committeeMeetingReferenceSchema],
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { timestamps: true });

// Virtual for URL
approvalWorkflowSchema.virtual('url').get(function() {
  return `/approval-workflows/${this._id}`;
});

// Method to advance to the next step
approvalWorkflowSchema.methods.advanceToNextStep = function() {
  const stepOrder = [
    'ANALYST_REPORT',
    'INSIGHT_MANAGER_REVIEW',
    'CORPORATE_MANAGER_REVIEW',
    'COMMITTEE_SCHEDULING',
    'COMMITTEE_SUBMISSION',
    'COMMITTEE_MEETING',
    'RECOMMENDATION',
    'FINAL_APPROVAL',
    'AWARD_LETTER',
    'CONDITIONS_FULFILLMENT',
    'AGREEMENT_SIGNING',
    'DISBURSEMENT',
    'RECEIPT_CONFIRMATION'
  ];
  
  const currentIndex = stepOrder.indexOf(this.currentStep);
  if (currentIndex < stepOrder.length - 1) {
    this.currentStep = stepOrder[currentIndex + 1];
    
    // Add the new step to the steps array if it doesn't exist
    const stepExists = this.steps.some(step => step.step === this.currentStep);
    if (!stepExists) {
      this.steps.push({
        step: this.currentStep,
        status: 'pending'
      });
    }
  }
  
  return this;
};

module.exports = mongoose.model('ApprovalWorkflow', approvalWorkflowSchema);
