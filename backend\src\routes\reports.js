const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Report = require('../models/report');
const Application = require('../models/application');
const FundingProgramme = require('../models/funding-programme');
const CorporateSponsor = require('../models/corporate-sponsor');
const { authenticateToken, authorizeR<PERSON>s, addEntityFilter, checkEntityAccess } = require('../middleware/auth');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const { Parser } = require('json2csv');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Get all reports
router.get('/', authenticateToken, async (req, res) => {
  try {
    const query = {};
    
    // Handle search
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }
    
    // Handle type filter
    if (req.query.type && ['status', 'processing-time', 'approval-rates', 'funding-distribution', 'regional-analysis', 'custom'].includes(req.query.type)) {
      query.type = req.query.type;
    }
    
    // Handle template filter
    if (req.query.isTemplate !== undefined) {
      query.isTemplate = req.query.isTemplate === 'true';
    }
    
    // Handle public filter
    if (req.query.isPublic !== undefined) {
      query.isPublic = req.query.isPublic === 'true';
    }
    
    // Handle date range filter
    if (req.query.startDate && req.query.endDate) {
      query.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    } else if (req.query.startDate) {
      query.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      query.createdAt = { $lte: new Date(req.query.endDate) };
    }
    
    // Handle tags filter
    if (req.query.tags) {
      const tags = req.query.tags.split(',');
      query.tags = { $in: tags };
    }
    
    // Add user filter if not admin
    if (!req.user || !req.user.roles || !req.user.roles.includes('admin')) {
      query.$or = [
        { createdBy: req.user ? req.user.id : null },
        { isPublic: true }
      ];
    }
    
    const reports = await Report.find(query)
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(parseInt(req.query.limit) || 100)
      .skip(parseInt(req.query.skip) || 0);
    
    const total = await Report.countDocuments(query);
    
    res.json({
      reports,
      total,
      limit: parseInt(req.query.limit) || 100,
      skip: parseInt(req.query.skip) || 0
    });
  } catch (err) {
    console.error('Error fetching reports:', err);
    res.status(500).json({ message: 'Error fetching reports', error: err.message });
  }
});

// Get a specific report by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id)
      .populate('createdBy', 'firstName lastName');
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has access to this report
    if (!report.isPublic && report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to access this report' });
    }
    
    res.json(report);
  } catch (err) {
    console.error('Error fetching report:', err);
    res.status(500).json({ message: 'Error fetching report', error: err.message });
  }
});

// Create a new report
router.post('/', authenticateToken, async (req, res) => {
  try {
    const newReport = new Report({
      ...req.body,
      createdBy: req.user.id
    });
    
    const savedReport = await newReport.save();
    
    res.status(201).json(savedReport);
  } catch (err) {
    console.error('Error creating report:', err);
    res.status(400).json({ message: 'Error creating report', error: err.message });
  }
});

// Update an existing report
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has permission to update this report
    if (report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this report' });
    }
    
    const updatedReport = await Report.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    res.json(updatedReport);
  } catch (err) {
    console.error('Error updating report:', err);
    res.status(400).json({ message: 'Error updating report', error: err.message });
  }
});

// Delete a report
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has permission to delete this report
    if (report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to delete this report' });
    }
    
    await Report.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Report deleted successfully' });
  } catch (err) {
    console.error('Error deleting report:', err);
    res.status(500).json({ message: 'Error deleting report', error: err.message });
  }
});

// Generate a report
router.post('/:id/generate', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has permission to generate this report
    if (!report.isPublic && report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to generate this report' });
    }
    
    // Generate report data based on type
    let reportData;
    
    switch (report.type) {
      case 'status':
        reportData = await generateStatusReport(report.filters);
        break;
      case 'processing-time':
        reportData = await generateProcessingTimeReport(report.filters);
        break;
      case 'approval-rates':
        reportData = await generateApprovalRatesReport(report.filters);
        break;
      case 'funding-distribution':
        reportData = await generateFundingDistributionReport(report.filters);
        break;
      case 'regional-analysis':
        reportData = await generateRegionalAnalysisReport(report.filters);
        break;
      case 'custom':
        reportData = await generateCustomReport(report.filters, report.columns);
        break;
      default:
        return res.status(400).json({ message: 'Invalid report type' });
    }
    
    // Update report with generated data
    report.data = reportData;
    report.status = 'generated';
    report.lastGeneratedAt = new Date();
    
    await report.save();
    
    res.json(report);
  } catch (err) {
    console.error('Error generating report:', err);
    res.status(500).json({ message: 'Error generating report', error: err.message });
  }
});

// Executive Dashboard Metrics endpoint
router.post('/executive-dashboard', authenticateToken, addEntityFilter, checkEntityAccess, async (req, res) => {
  try {
    const filters = req.body;
    
    // Apply entity-based filtering first
    const query = req.entityFilter || {};
    
    if (filters.programmeId) {
      query.programmeId = filters.programmeId;
    }
    
    if (filters.corporateSponsorId) {
      query.corporateSponsorId = filters.corporateSponsorId;
    }
    
    // Apply other filters
    if (filters.startDate && filters.endDate) {
      query.createdAt = {
        $gte: new Date(filters.startDate),
        $lte: new Date(filters.endDate)
      };
    } else if (filters.startDate) {
      query.createdAt = { $gte: new Date(filters.startDate) };
    } else if (filters.endDate) {
      query.createdAt = { $lte: new Date(filters.endDate) };
    }
    
    if (filters.status) {
      query.status = filters.status;
    }
    
    if (filters.province) {
      query['location.province'] = filters.province;
    }
    
    if (filters.sector) {
      query.sector = filters.sector;
    }
    
    if (filters.bbbeeLevel) {
      query.bbbeeLevel = filters.bbbeeLevel;
    }
    
    // Generate dashboard metrics based on filters and user role
    const userRole = filters.userRole || 'executive';
    const timePeriod = filters.timePeriod || 'monthly';
    
    // Generate dashboard metrics using real database queries
    const dashboardMetrics = await generateExecutiveDashboardMetrics(query, userRole, timePeriod);
    
    res.json(dashboardMetrics);
  } catch (err) {
    console.error('Error generating executive dashboard metrics:', err);
    res.status(500).json({ message: 'Error generating executive dashboard metrics', error: err.message });
  }
});

// Export a report
router.get('/:id/export/:format', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has permission to export this report
    if (!report.isPublic && report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to export this report' });
    }
    
    // Check if report has been generated
    if (report.status !== 'generated' || !report.data) {
      return res.status(400).json({ message: 'Report has not been generated yet' });
    }
    
    const format = req.params.format;
    
    if (!['pdf', 'excel', 'csv'].includes(format)) {
      return res.status(400).json({ message: 'Invalid export format' });
    }
    
    // Export report based on format
    switch (format) {
      case 'pdf':
        await exportReportToPdf(report, res);
        break;
      case 'excel':
        await exportReportToExcel(report, res);
        break;
      case 'csv':
        await exportReportToCsv(report, res);
        break;
    }
  } catch (err) {
    console.error('Error exporting report:', err);
    res.status(500).json({ message: 'Error exporting report', error: err.message });
  }
});

// Get report templates
router.get('/templates', authenticateToken, async (req, res) => {
  try {
    const query = { isTemplate: true };
    
    // Handle type filter
    if (req.query.type && ['status', 'processing-time', 'approval-rates', 'funding-distribution', 'regional-analysis', 'custom'].includes(req.query.type)) {
      query.type = req.query.type;
    }
    
    const templates = await Report.find(query)
      .sort({ name: 1 });
    
    res.json(templates);
  } catch (err) {
    console.error('Error fetching report templates:', err);
    res.status(500).json({ message: 'Error fetching report templates', error: err.message });
  }
});

// Clone a report
router.post('/:id/clone', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Check if user has permission to clone this report
    if (!report.isPublic && report.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to clone this report' });
    }
    
    // Create a new report based on the original
    const newReport = new Report({
      name: req.body.name || `${report.name} (Copy)`,
      description: report.description,
      type: report.type,
      filters: report.filters,
      columns: report.columns,
      format: report.format,
      isTemplate: false,
      isPublic: false,
      tags: report.tags,
      createdBy: req.user.id,
      status: 'draft'
    });
    
    const savedReport = await newReport.save();
    
    res.status(201).json(savedReport);
  } catch (err) {
    console.error('Error cloning report:', err);
    res.status(500).json({ message: 'Error cloning report', error: err.message });
  }
});

// Helper function to generate executive dashboard metrics
async function generateExecutiveDashboardMetrics(query, userRole, timePeriod) {
  try {
    // Build aggregation pipeline for applications
    const matchStage = { ...query };
    
    // Get total applications count
    const totalApplications = await Application.countDocuments(matchStage);
    
    // Get status counts
    const statusCounts = await Application.aggregate([
      { $match: matchStage },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    
    // Process status counts
    const statusMap = {};
    statusCounts.forEach(item => {
      statusMap[item._id] = item.count;
    });
    
    const approvedApplications = statusMap['approved'] || 0;
    const rejectedApplications = statusMap['rejected'] || 0;
    const pendingApplications = (statusMap['pending'] || 0) + (statusMap['in-review'] || 0);
    const approvalRate = totalApplications > 0 ? (approvedApplications / totalApplications) * 100 : 0;
    
    // Get funding totals for approved applications
    const fundingStats = await Application.aggregate([
      { $match: { ...matchStage, status: 'approved' } },
      {
        $group: {
          _id: null,
          totalFunding: { $sum: '$fundingAmount' },
          avgFunding: { $avg: '$fundingAmount' },
          count: { $sum: 1 }
        }
      }
    ]);
    
    const totalFundingApproved = fundingStats.length > 0 ? fundingStats[0].totalFunding || 0 : 0;
    const avgFundingAmount = fundingStats.length > 0 ? fundingStats[0].avgFunding || 0 : 0;
    
    // Calculate processing time (mock for now - would need actual stage tracking)
    const avgProcessingTime = 14.5;
    
    // Generate KPIs
    const kpis = {
      totalApplications,
      approvedApplications,
      rejectedApplications,
      pendingApplications,
      approvalRate: Math.round(approvalRate * 100) / 100,
      totalFundingApproved,
      avgFundingAmount: Math.round(avgFundingAmount),
      avgProcessingTime,
      overallBudgetUtilization: 65.0, // Would calculate from programme budgets
      applicationTrend: 12.5, // Would calculate from historical data
      fundingTrend: 8.3, // Would calculate from historical data
      approvalRateTrend: 5.2 // Would calculate from historical data
    };
  
    // Get programme performance from database
    const programmePerformance = await Application.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'fundingprogrammes',
          localField: 'programmeId',
          foreignField: '_id',
          as: 'programme'
        }
      },
      { $unwind: '$programme' },
      {
        $group: {
          _id: '$programmeId',
          programmeName: { $first: '$programme.name' },
          applications: { $sum: 1 },
          approved: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
          rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } },
          pending: { $sum: { $cond: [{ $in: ['$status', ['pending', 'in-review']] }, 1, 0] } },
          fundingApproved: {
            $sum: {
              $cond: [
                { $eq: ['$status', 'approved'] },
                '$fundingAmount',
                0
              ]
            }
          }
        }
      },
      {
        $addFields: {
          programmeId: { $toString: '$_id' },
          approvalRate: {
            $cond: [
              { $gt: ['$applications', 0] },
              { $multiply: [{ $divide: ['$approved', '$applications'] }, 100] },
              0
            ]
          },
          budgetUtilization: 65.0, // Would calculate from actual programme budget
          avgProcessingTime: 15.2 // Would calculate from actual stage data
        }
      }
    ]);
    
    // Generate trends (mock data for now - would use historical data)
    const trends = [];
    const periods = timePeriod === 'monthly'
      ? ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
      : timePeriod === 'quarterly'
        ? ['Q1', 'Q2', 'Q3', 'Q4']
        : ['2022', '2023', '2024', '2025'];
    
    periods.forEach(period => {
      trends.push({
        period,
        applications: Math.floor(Math.random() * 100) + 50,
        approved: Math.floor(Math.random() * 50) + 20,
        approvalRate: Math.random() * 50 + 30,
        fundingApproved: Math.random() * 5000000 + 1000000,
        avgFundingAmount: Math.random() * 100000 + 50000,
        avgProcessingTime: Math.random() * 20 + 5
      });
    });
  
    // Get sector distribution
    const sectorDistribution = await Application.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$businessInfo.industry',
          applications: { $sum: 1 },
          approved: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
          fundingApproved: {
            $sum: {
              $cond: [
                { $eq: ['$status', 'approved'] },
                '$fundingAmount',
                0
              ]
            }
          }
        }
      },
      {
        $addFields: {
          sector: { $ifNull: ['$_id', 'Other'] },
          approvalRate: {
            $cond: [
              { $gt: ['$applications', 0] },
              { $multiply: [{ $divide: ['$approved', '$applications'] }, 100] },
              0
            ]
          }
        }
      },
      { $sort: { applications: -1 } },
      { $limit: 10 }
    ]);
    
    // Generate application status breakdown
    const applicationStatus = [
      { status: 'Approved', count: approvedApplications, percentage: totalApplications > 0 ? (approvedApplications / totalApplications) * 100 : 0 },
      { status: 'Rejected', count: rejectedApplications, percentage: totalApplications > 0 ? (rejectedApplications / totalApplications) * 100 : 0 },
      { status: 'Pending', count: pendingApplications, percentage: totalApplications > 0 ? (pendingApplications / totalApplications) * 100 : 0 }
    ];
    
    // Mock funding type distribution (would need to be added to schema)
    const fundingTypeDistribution = [
      { fundingType: 'Grant', applications: Math.floor(totalApplications * 0.6), approved: Math.floor(approvedApplications * 0.6), approvalRate: approvalRate, fundingApproved: Math.floor(totalFundingApproved * 0.6) },
      { fundingType: 'Loan', applications: Math.floor(totalApplications * 0.4), approved: Math.floor(approvedApplications * 0.4), approvalRate: approvalRate, fundingApproved: Math.floor(totalFundingApproved * 0.4) }
    ];
    
    // Filter programme performance based on programmeId if provided
    if (query.programmeId) {
      const filteredProgrammes = programmePerformance.filter(p => p.programmeId === query.programmeId.toString());
      if (filteredProgrammes.length > 0) {
        return {
          kpis: {
            ...kpis,
            totalApplications: filteredProgrammes[0].applications,
            approvedApplications: filteredProgrammes[0].approved,
            rejectedApplications: filteredProgrammes[0].rejected,
            pendingApplications: filteredProgrammes[0].pending,
            approvalRate: filteredProgrammes[0].approvalRate,
            totalFundingApproved: filteredProgrammes[0].fundingApproved,
            avgProcessingTime: filteredProgrammes[0].avgProcessingTime
          },
          trends,
          programmePerformance: filteredProgrammes,
          applicationStatus,
          sectorDistribution,
          fundingTypeDistribution
        };
      }
    }
    
    // Return all data
    return {
      kpis,
      trends,
      programmePerformance,
      applicationStatus,
      sectorDistribution,
      fundingTypeDistribution
    };
    
  } catch (error) {
    console.error('Error generating executive dashboard metrics:', error);
    // Return fallback data in case of error
    return {
      kpis: {
        totalApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        pendingApplications: 0,
        approvalRate: 0,
        totalFundingApproved: 0,
        avgFundingAmount: 0,
        avgProcessingTime: 0,
        overallBudgetUtilization: 0,
        applicationTrend: 0,
        fundingTrend: 0,
        approvalRateTrend: 0
      },
      trends: [],
      programmePerformance: [],
      applicationStatus: [],
      sectorDistribution: [],
      fundingTypeDistribution: []
    };
  }
}

// Helper functions for report generation
async function generateStatusReport(filters) {
  // Implementation would query the database for application status data
  // This is a placeholder implementation
  const columns = [
    { field: 'status', header: 'Status', dataType: 'string' },
    { field: 'count', header: 'Count', dataType: 'number' },
    { field: 'percentage', header: 'Percentage', dataType: 'number', format: '0.0%' }
  ];
  
  const rows = [
    { status: 'Submitted', count: 120, percentage: 0.3 },
    { status: 'In Review', count: 80, percentage: 0.2 },
    { status: 'Approved', count: 150, percentage: 0.375 },
    { status: 'Rejected', count: 50, percentage: 0.125 }
  ];
  
  const summary = {
    totalApplications: 400,
    approvalRate: 0.375,
    rejectionRate: 0.125
  };
  
  return { columns, rows, summary };
}

async function generateProcessingTimeReport(filters) {
  // Implementation would query the database for application processing time data
  // This is a placeholder implementation
  const columns = [
    { field: 'stage', header: 'Stage', dataType: 'string' },
    { field: 'averageTime', header: 'Average Time (days)', dataType: 'number' },
    { field: 'minTime', header: 'Minimum Time (days)', dataType: 'number' },
    { field: 'maxTime', header: 'Maximum Time (days)', dataType: 'number' }
  ];
  
  const rows = [
    { stage: 'Submission to Screening', averageTime: 2.5, minTime: 1, maxTime: 5 },
    { stage: 'Screening to Review', averageTime: 5.3, minTime: 3, maxTime: 10 },
    { stage: 'Review to Decision', averageTime: 7.8, minTime: 4, maxTime: 15 },
    { stage: 'Decision to Disbursement', averageTime: 12.4, minTime: 7, maxTime: 25 }
  ];
  
  const summary = {
    totalAverageTime: 28.0,
    fastestApplication: 15,
    slowestApplication: 55
  };
  
  return { columns, rows, summary };
}

async function generateApprovalRatesReport(filters) {
  // Implementation would query the database for approval rate data
  // This is a placeholder implementation
  const columns = [
    { field: 'programme', header: 'Programme', dataType: 'string' },
    { field: 'totalApplications', header: 'Total Applications', dataType: 'number' },
    { field: 'approvedApplications', header: 'Approved', dataType: 'number' },
    { field: 'approvalRate', header: 'Approval Rate', dataType: 'number', format: '0.0%' }
  ];
  
  const rows = [
    { programme: 'Small Business Grant', totalApplications: 150, approvedApplications: 75, approvalRate: 0.5 },
    { programme: 'Innovation Fund', totalApplications: 80, approvedApplications: 32, approvalRate: 0.4 },
    { programme: 'Expansion Loan', totalApplications: 120, approvedApplications: 72, approvalRate: 0.6 },
    { programme: 'Research Grant', totalApplications: 50, approvedApplications: 15, approvalRate: 0.3 }
  ];
  
  const summary = {
    totalApplications: 400,
    totalApproved: 194,
    overallApprovalRate: 0.485
  };
  
  return { columns, rows, summary };
}

async function generateFundingDistributionReport(filters) {
  // Implementation would query the database for funding distribution data
  // This is a placeholder implementation
  const columns = [
    { field: 'fundingRange', header: 'Funding Range', dataType: 'string' },
    { field: 'count', header: 'Count', dataType: 'number' },
    { field: 'totalAmount', header: 'Total Amount', dataType: 'currency' },
    { field: 'percentage', header: 'Percentage', dataType: 'number', format: '0.0%' }
  ];
  
  const rows = [
    { fundingRange: 'R0 - R50,000', count: 45, totalAmount: 1500000, percentage: 0.15 },
    { fundingRange: 'R50,001 - R100,000', count: 30, totalAmount: 2250000, percentage: 0.225 },
    { fundingRange: 'R100,001 - R250,000', count: 25, totalAmount: 3750000, percentage: 0.375 },
    { fundingRange: 'R250,001 - R500,000', count: 10, totalAmount: 2500000, percentage: 0.25 }
  ];
  
  const summary = {
    totalFunding: 10000000,
    averageFunding: 90909.09,
    medianFunding: 75000
  };
  
  return { columns, rows, summary };
}

async function generateRegionalAnalysisReport(filters) {
  // Implementation would query the database for regional analysis data
  // This is a placeholder implementation
  const columns = [
    { field: 'region', header: 'Region', dataType: 'string' },
    { field: 'applications', header: 'Applications', dataType: 'number' },
    { field: 'approved', header: 'Approved', dataType: 'number' },
    { field: 'totalFunding', header: 'Total Funding', dataType: 'currency' }
  ];
  
  const rows = [
    { region: 'Gauteng', applications: 120, approved: 60, totalFunding: 4500000 },
    { region: 'Western Cape', applications: 80, approved: 40, totalFunding: 3000000 },
    { region: 'KwaZulu-Natal', applications: 60, approved: 25, totalFunding: 1875000 },
    { region: 'Eastern Cape', applications: 40, approved: 15, totalFunding: 1125000 },
    { region: 'Other Provinces', applications: 100, approved: 30, totalFunding: 2250000 }
  ];
  
  const summary = {
    totalApplications: 400,
    totalApproved: 170,
    totalFunding: 12750000
  };
  
  return { columns, rows, summary };
}

async function generateCustomReport(filters, columns) {
  // Implementation would query the database based on custom filters and columns
  // This is a placeholder implementation
  const rows = [];
  
  // Generate some random data based on the columns
  for (let i = 0; i < 10; i++) {
    const row = {};
    
    columns.forEach(column => {
      switch (column.dataType) {
        case 'string':
          row[column.field] = `Sample ${column.field} ${i + 1}`;
          break;
        case 'number':
          row[column.field] = Math.floor(Math.random() * 100);
          break;
        case 'date':
          const date = new Date();
          date.setDate(date.getDate() - Math.floor(Math.random() * 30));
          row[column.field] = date;
          break;
        case 'boolean':
          row[column.field] = Math.random() > 0.5;
          break;
        case 'currency':
          row[column.field] = Math.floor(Math.random() * 10000) * 100;
          break;
      }
    });
    
    rows.push(row);
  }
  
  return { columns, rows };
}

// Helper functions for report export
async function exportReportToPdf(report, res) {
  const doc = new PDFDocument();
  
  // Set response headers
  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Content-Disposition', `attachment; filename=${report.name.replace(/\s+/g, '_')}.pdf`);
  
  // Pipe the PDF to the response
  doc.pipe(res);
  
  // Add report title and description
  doc.fontSize(24).text(report.name, { align: 'center' });
  doc.moveDown();
  
  if (report.description) {
    doc.fontSize(12).text(report.description, { align: 'center' });
    doc.moveDown();
  }
  
  // Add generation date
  doc.fontSize(10).text(`Generated on: ${new Date().toLocaleString()}`, { align: 'right' });
  doc.moveDown();
  
  // Add table headers
  const tableTop = 200;
  const columnCount = report.data.columns.length;
  const columnWidth = 500 / columnCount;
  
  report.data.columns.forEach((column, i) => {
    doc.fontSize(10).text(column.header, 50 + (i * columnWidth), tableTop, { width: columnWidth, align: 'center' });
  });
  
  doc.moveTo(50, tableTop + 20).lineTo(550, tableTop + 20).stroke();
  
  // Add table rows
  let rowTop = tableTop + 30;
  
  report.data.rows.forEach((row, rowIndex) => {
    report.data.columns.forEach((column, colIndex) => {
      let value = row[column.field];
      
      // Format value based on data type
      if (column.dataType === 'date' && value) {
        value = new Date(value).toLocaleDateString();
      } else if (column.dataType === 'currency' && value) {
        value = `R ${value.toLocaleString()}`;
      } else if (column.dataType === 'number' && column.format && column.format.includes('%')) {
        value = `${(value * 100).toFixed(1)}%`;
      }
      
      doc.fontSize(8).text(value !== undefined && value !== null ? value.toString() : '', 
        50 + (colIndex * columnWidth), 
        rowTop, 
        { width: columnWidth, align: 'center' }
      );
    });
    
    rowTop += 20;
    
    // Add a new page if we're running out of space
    if (rowTop > 700) {
      doc.addPage();
      rowTop = 50;
    }
  });
  
  // Add summary if available
  if (report.data.summary) {
    doc.addPage();
    doc.fontSize(16).text('Summary', { align: 'center' });
    doc.moveDown();
    
    Object.entries(report.data.summary).forEach(([key, value]) => {
      let formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      let formattedValue = value;
      
      // Format value if it looks like a percentage
      if (typeof value === 'number' && key.toLowerCase().includes('rate')) {
        formattedValue = `${(value * 100).toFixed(1)}%`;
      } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
        formattedValue = `R ${value.toLocaleString()}`;
      }
      
      doc.fontSize(12).text(`${formattedKey}: ${formattedValue}`);
      doc.moveDown(0.5);
    });
  }
  
  // Finalize the PDF
  doc.end();
}

async function exportReportToExcel(report, res) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Report');
  
  // Add headers
  const headers = report.data.columns.map(column => column.header);
  worksheet.addRow(headers);
  
  // Format header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).alignment = { horizontal: 'center' };
  
  // Add data rows
  report.data.rows.forEach(row => {
    const values = report.data.columns.map(column => {
      let value = row[column.field];
      
      // Format value based on data type
      if (column.dataType === 'date' && value) {
        return new Date(value);
      }
      
      return value;
    });
    
    worksheet.addRow(values);
  });
  
  // Format columns
  report.data.columns.forEach((column, index) => {
    const excelColumn = worksheet.getColumn(index + 1);
    
    if (column.dataType === 'currency') {
      excelColumn.numFmt = 'R#,##0.00';
    } else if (column.dataType === 'number' && column.format && column.format.includes('%')) {
      excelColumn.numFmt = '0.0%';
    } else if (column.dataType === 'date') {
      excelColumn.numFmt = 'yyyy-mm-dd';
    }
  });
  
  // Add summary if available
  if (report.data.summary) {
    worksheet.addRow([]);
    worksheet.addRow(['Summary']);
    worksheet.getCell(`A${worksheet.rowCount}`).font = { bold: true, size: 14 };
    
    Object.entries(report.data.summary).forEach(([key, value]) => {
      let formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      worksheet.addRow([formattedKey, value]);
      
      // Format value if it looks like a percentage or currency
      const cell = worksheet.getCell(`B${worksheet.rowCount}`);
      if (typeof value === 'number' && key.toLowerCase().includes('rate')) {
        cell.numFmt = '0.0%';
      } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
        cell.numFmt = 'R#,##0.00';
      }
    });
  }
  
  // Set response headers
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', `attachment; filename=${report.name.replace(/\s+/g, '_')}.xlsx`);
  
  // Write to response
  await workbook.xlsx.write(res);
}

async function exportReportToCsv(report, res) {
  // Prepare data for CSV
  const fields = report.data.columns.map(column => ({
    label: column.header,
    value: column.field
  }));
  
  // Format data
  const data = report.data.rows.map(row => {
    const formattedRow = { ...row };
    
    report.data.columns.forEach(column => {
      if (column.dataType === 'date' && formattedRow[column.field]) {
        formattedRow[column.field] = new Date(formattedRow[column.field]).toLocaleDateString();
      } else if (column.dataType === 'currency' && formattedRow[column.field]) {
        formattedRow[column.field] = `R ${formattedRow[column.field].toLocaleString()}`;
      } else if (column.dataType === 'number' && column.format && column.format.includes('%') && formattedRow[column.field]) {
        formattedRow[column.field] = `${(formattedRow[column.field] * 100).toFixed(1)}%`;
      }
    });
    
    return formattedRow;
  });
  
  // Create CSV
  const json2csvParser = new Parser({ fields });
  const csv = json2csvParser.parse(data);
  
  // Add summary if available
  let csvWithSummary = csv;
  if (report.data.summary) {
    csvWithSummary += '\n\nSummary\n';
    
    Object.entries(report.data.summary).forEach(([key, value]) => {
      let formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      let formattedValue = value;
      
      // Format value if it looks like a percentage or currency
      if (typeof value === 'number' && key.toLowerCase().includes('rate')) {
        formattedValue = `${(value * 100).toFixed(1)}%`;
      } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
        formattedValue = `R ${value.toLocaleString()}`;
      }
      
      csvWithSummary += `${formattedKey},${formattedValue}\n`;
    });
  }
  
  // Set response headers
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename=${report.name.replace(/\s+/g, '_')}.csv`);
  
  // Send CSV
  res.send(csvWithSummary);
}

module.exports = router;
