const mongoose = require('mongoose');
const NotificationService = require('./services/notification-service');
const NotificationTemplate = require('./models/notification-template');
const ApplicationEventService = require('./services/application-event-service');
const User = require('./models/user');
const Application = require('./models/application');

/**
 * Notification System Testing Script
 * Comprehensive testing suite for the notification system
 */

class NotificationSystemTester {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * Run all notification system tests
   */
  async runAllTests() {
    console.log('🚀 Starting Notification System Tests...\n');

    try {
      await this.testDatabaseConnection();
      await this.testNotificationTemplates();
      await this.testNotificationCreation();
      await this.testApplicationEvents();
      await this.testNotificationDelivery();
      await this.testUserNotifications();
      await this.testNotificationAnalytics();
      await this.testSystemPerformance();

      this.printTestSummary();
      return this.getTestResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      throw error;
    }
  }

  /**
   * Test database connection and models
   */
  async testDatabaseConnection() {
    console.log('📊 Testing Database Connection...');

    await this.runTest('Database Connection', async () => {
      const state = mongoose.connection.readyState;
      if (state !== 1) {
        throw new Error(`Database not connected. State: ${state}`);
      }
      return { connected: true, state };
    });

    await this.runTest('Notification Model', async () => {
      const count = await mongoose.connection.db.collection('notifications').countDocuments();
      return { collectionExists: true, documentCount: count };
    });

    await this.runTest('User Notification Model', async () => {
      const count = await mongoose.connection.db.collection('usernotifications').countDocuments();
      return { collectionExists: true, documentCount: count };
    });

    await this.runTest('Notification Template Model', async () => {
      const count = await mongoose.connection.db.collection('notificationtemplates').countDocuments();
      return { collectionExists: true, documentCount: count };
    });
  }

  /**
   * Test notification templates
   */
  async testNotificationTemplates() {
    console.log('📝 Testing Notification Templates...');

    await this.runTest('Template Creation', async () => {
      const template = new NotificationTemplate({
        name: 'Test Template',
        description: 'Test template for system testing',
        type: 'SYSTEM_ANNOUNCEMENT',
        category: 'SYSTEM',
        variables: [
          { name: 'testVar', type: 'string', required: true, description: 'Test variable' }
        ],
        content: {
          inApp: {
            title: 'Test: {{testVar}}',
            message: 'This is a test notification with {{testVar}}'
          }
        },
        defaultSettings: {
          priority: 'MEDIUM',
          deliveryMethods: { inApp: true, email: false, sms: false, push: false },
          targetAudience: 'ALL_USERS'
        },
        isActive: true,
        createdBy: new mongoose.Types.ObjectId()
      });

      await template.save();
      return { templateId: template._id, name: template.name };
    });

    await this.runTest('Template Variable Validation', async () => {
      const template = await NotificationTemplate.findOne({ name: 'Test Template' });
      if (!template) throw new Error('Test template not found');

      const validationErrors = template.validateVariables({ testVar: 'Hello World' });
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      return { validationPassed: true };
    });

    await this.runTest('Template Rendering', async () => {
      const template = await NotificationTemplate.findOne({ name: 'Test Template' });
      if (!template) throw new Error('Test template not found');

      const rendered = template.render({ testVar: 'Hello World' });
      
      if (!rendered.inApp || !rendered.inApp.title.includes('Hello World')) {
        throw new Error('Template rendering failed');
      }

      return { rendered: rendered.inApp };
    });
  }

  /**
   * Test notification creation and processing
   */
  async testNotificationCreation() {
    console.log('🔔 Testing Notification Creation...');

    let testNotificationId;

    await this.runTest('Basic Notification Creation', async () => {
      const notification = await NotificationService.createNotification({
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'SYSTEM_ANNOUNCEMENT',
        category: 'SYSTEM',
        priority: 'MEDIUM',
        targetAudience: 'ALL_USERS',
        deliveryMethods: { inApp: true, email: false, sms: false, push: false }
      }, new mongoose.Types.ObjectId());

      testNotificationId = notification._id;
      return { notificationId: notification._id, status: notification.status };
    });

    await this.runTest('Template-based Notification Creation', async () => {
      const template = await NotificationTemplate.findOne({ name: 'Test Template' });
      if (!template) throw new Error('Test template not found');

      const notification = await NotificationService.createFromTemplate(
        template._id,
        { testVar: 'Template Test' },
        { priority: 'HIGH' },
        new mongoose.Types.ObjectId()
      );

      return { notificationId: notification._id, templateUsed: template.name };
    });

    await this.runTest('Notification Processing', async () => {
      if (!testNotificationId) throw new Error('No test notification to process');

      const result = await NotificationService.processNotification(testNotificationId);
      return { processed: result.success, recipients: result.recipients?.length || 0 };
    });
  }

  /**
   * Test application event integration
   */
  async testApplicationEvents() {
    console.log('📋 Testing Application Events...');

    // Create a test application if needed
    let testApplication = await Application.findOne().limit(1);
    if (!testApplication) {
      console.log('No applications found, skipping application event tests');
      return;
    }

    await this.runTest('Status Change Event', async () => {
      const event = {
        applicationId: testApplication.id,
        eventType: 'STATUS_CHANGE',
        eventData: {
          oldStatus: 'pending',
          newStatus: 'in-review'
        },
        timestamp: new Date()
      };

      const result = await ApplicationEventService.processEvent(event, new mongoose.Types.ObjectId());
      return { success: result.success, notificationsSent: result.notificationsSent };
    });

    await this.runTest('Document Required Event', async () => {
      const event = {
        applicationId: testApplication.id,
        eventType: 'DOCUMENT_REQUIRED',
        eventData: {
          documentType: 'Financial Statements',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        },
        timestamp: new Date()
      };

      const result = await ApplicationEventService.processEvent(event, new mongoose.Types.ObjectId());
      return { success: result.success, notificationsSent: result.notificationsSent };
    });

    await this.runTest('Deadline Approaching Event', async () => {
      const event = {
        applicationId: testApplication.id,
        eventType: 'DEADLINE_APPROACHING',
        eventData: {
          deadlineType: 'Document Submission',
          deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          daysRemaining: 3
        },
        timestamp: new Date()
      };

      const result = await ApplicationEventService.processEvent(event, new mongoose.Types.ObjectId());
      return { success: result.success, notificationsSent: result.notificationsSent };
    });
  }

  /**
   * Test notification delivery mechanisms
   */
  async testNotificationDelivery() {
    console.log('📤 Testing Notification Delivery...');

    await this.runTest('In-App Delivery', async () => {
      // This would test the in-app notification delivery
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 100));
      return { delivered: true, method: 'in-app' };
    });

    await this.runTest('Email Delivery Simulation', async () => {
      // This would test email delivery
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 200));
      return { delivered: true, method: 'email' };
    });

    await this.runTest('WebSocket Delivery', async () => {
      // This would test WebSocket delivery
      // For now, we'll simulate it
      await new Promise(resolve => setTimeout(resolve, 150));
      return { delivered: true, method: 'websocket' };
    });
  }

  /**
   * Test user notification functionality
   */
  async testUserNotifications() {
    console.log('👤 Testing User Notifications...');

    // Get a test user
    let testUser = await User.findOne().limit(1);
    if (!testUser) {
      console.log('No users found, skipping user notification tests');
      return;
    }

    await this.runTest('Get User Notifications', async () => {
      const notifications = await NotificationService.getUserNotifications(testUser._id);
      return { userId: testUser._id, notificationCount: notifications.length };
    });

    await this.runTest('Get Unread Notifications', async () => {
      const unreadNotifications = await NotificationService.getUnreadNotifications(testUser._id);
      return { userId: testUser._id, unreadCount: unreadNotifications.length };
    });

    await this.runTest('Mark Notification as Read', async () => {
      const notifications = await NotificationService.getUserNotifications(testUser._id, {}, 1);
      if (notifications.length === 0) {
        return { message: 'No notifications to mark as read' };
      }

      const result = await NotificationService.markAsRead(testUser._id, notifications[0]._id);
      return { marked: true, notificationId: notifications[0]._id };
    });
  }

  /**
   * Test notification analytics
   */
  async testNotificationAnalytics() {
    console.log('📈 Testing Notification Analytics...');

    await this.runTest('Notification Statistics', async () => {
      const notifications = await mongoose.connection.db.collection('notifications')
        .find().limit(5).toArray();
      
      if (notifications.length === 0) {
        return { message: 'No notifications found for analytics' };
      }

      const stats = await NotificationService.getNotificationStats(notifications[0]._id);
      return { notificationId: notifications[0]._id, stats };
    });

    await this.runTest('System Analytics', async () => {
      const totalNotifications = await mongoose.connection.db.collection('notifications').countDocuments();
      const totalUserNotifications = await mongoose.connection.db.collection('usernotifications').countDocuments();
      const totalTemplates = await mongoose.connection.db.collection('notificationtemplates').countDocuments();

      return {
        totalNotifications,
        totalUserNotifications,
        totalTemplates
      };
    });
  }

  /**
   * Test system performance
   */
  async testSystemPerformance() {
    console.log('⚡ Testing System Performance...');

    await this.runTest('Bulk Notification Creation', async () => {
      const startTime = Date.now();
      const notifications = [];

      for (let i = 0; i < 10; i++) {
        const notification = await NotificationService.createNotification({
          title: `Bulk Test Notification ${i + 1}`,
          message: `This is bulk test notification number ${i + 1}`,
          type: 'SYSTEM_ANNOUNCEMENT',
          category: 'SYSTEM',
          priority: 'LOW',
          targetAudience: 'ALL_USERS',
          deliveryMethods: { inApp: true, email: false, sms: false, push: false }
        }, new mongoose.Types.ObjectId());

        notifications.push(notification._id);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        notificationsCreated: notifications.length,
        duration: `${duration}ms`,
        averageTime: `${Math.round(duration / notifications.length)}ms per notification`
      };
    });

    await this.runTest('Database Query Performance', async () => {
      const startTime = Date.now();
      
      await mongoose.connection.db.collection('notifications').find().limit(100).toArray();
      await mongoose.connection.db.collection('usernotifications').find().limit(100).toArray();
      await mongoose.connection.db.collection('notificationtemplates').find().toArray();

      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        queriesExecuted: 3,
        totalDuration: `${duration}ms`,
        averageQueryTime: `${Math.round(duration / 3)}ms`
      };
    });
  }

  /**
   * Run a single test
   */
  async runTest(testName, testFunction) {
    this.totalTests++;
    
    try {
      const startTime = Date.now();
      const result = await testFunction();
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.passedTests++;
      this.testResults.push({
        name: testName,
        status: 'PASSED',
        duration: `${duration}ms`,
        result
      });

      console.log(`  ✅ ${testName} - ${duration}ms`);
      
    } catch (error) {
      this.failedTests++;
      this.testResults.push({
        name: testName,
        status: 'FAILED',
        error: error.message,
        stack: error.stack
      });

      console.log(`  ❌ ${testName} - ${error.message}`);
    }
  }

  /**
   * Print test summary
   */
  printTestSummary() {
    console.log('\n📊 Test Summary:');
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.failedTests}`);
    console.log(`Success Rate: ${Math.round((this.passedTests / this.totalTests) * 100)}%`);

    if (this.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`);
        });
    }

    console.log('\n🎉 Testing completed!');
  }

  /**
   * Get test results
   */
  getTestResults() {
    return {
      summary: {
        total: this.totalTests,
        passed: this.passedTests,
        failed: this.failedTests,
        successRate: Math.round((this.passedTests / this.totalTests) * 100)
      },
      tests: this.testResults
    };
  }

  /**
   * Clean up test data
   */
  async cleanup() {
    console.log('🧹 Cleaning up test data...');

    try {
      // Remove test notifications
      await mongoose.connection.db.collection('notifications').deleteMany({
        title: { $regex: /^(Test|Bulk Test)/ }
      });

      // Remove test templates
      await NotificationTemplate.deleteMany({
        name: { $regex: /^Test/ }
      });

      // Remove test user notifications
      await mongoose.connection.db.collection('usernotifications').deleteMany({
        'notification.title': { $regex: /^(Test|Bulk Test)/ }
      });

      console.log('✅ Test data cleaned up');

    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}

/**
 * Specific test functions for different components
 */

/**
 * Test notification template functionality
 */
async function testNotificationTemplates() {
  const tester = new NotificationSystemTester();
  console.log('🧪 Testing Notification Templates Only...\n');

  await tester.testNotificationTemplates();
  tester.printTestSummary();
  return tester.getTestResults();
}

/**
 * Test application event processing
 */
async function testApplicationEvents() {
  const tester = new NotificationSystemTester();
  console.log('🧪 Testing Application Events Only...\n');

  await tester.testApplicationEvents();
  tester.printTestSummary();
  return tester.getTestResults();
}

/**
 * Test notification delivery
 */
async function testNotificationDelivery() {
  const tester = new NotificationSystemTester();
  console.log('🧪 Testing Notification Delivery Only...\n');

  await tester.testNotificationDelivery();
  tester.printTestSummary();
  return tester.getTestResults();
}

/**
 * Performance testing
 */
async function testPerformance() {
  const tester = new NotificationSystemTester();
  console.log('🧪 Testing System Performance Only...\n');

  await tester.testSystemPerformance();
  tester.printTestSummary();
  return tester.getTestResults();
}
}

/**
 * Run tests if called directly
 */
if (require.main === module) {
  const connectDB = require('./config/database');
  
  connectDB().then(async () => {
    const tester = new NotificationSystemTester();
    
    try {
      const results = await tester.runAllTests();
      
      // Optionally clean up test data
      if (process.argv.includes('--cleanup')) {
        await tester.cleanup();
      }
      
      // Exit with appropriate code
      process.exit(results.summary.failed > 0 ? 1 : 0);
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  });
}

module.exports = {
  NotificationSystemTester,
  testNotificationTemplates,
  testApplicationEvents,
  testNotificationDelivery,
  testPerformance
};