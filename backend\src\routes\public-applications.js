const express = require('express');
const router = express.Router();
const Application = require('../models/application');

// Public endpoint for applications (no authentication required)

// Update application
router.put('/:id', async (req, res) => {
  try {
    // Get the current application state for audit trail
    const currentApplication = await Application.findOne({ id: req.params.id }).lean();
    
    if (!currentApplication) {
      return res.status(404).json({ message: 'Application not found' });
    }

    const updatedApplication = await Application.findOneAndUpdate(
      { id: req.params.id },
      req.body,
      { new: true, runValidators: true }
    )
    .populate('programmeId')  // Populate program data
    .populate('corporateSponsorId')  // Populate sponsor data
    .lean();
    
    // Log audit trail if stage-related fields changed
    const stageFieldsChanged =
      req.body.currentMainStage !== undefined ||
      req.body.currentSubStage !== undefined ||
      req.body.currentStageStatus !== undefined ||
      req.body.status !== undefined;

    if (stageFieldsChanged) {
      try {
        await AuditTrailService.logStageTransition({
          applicationId: req.params.id,
          applicationObjectId: updatedApplication._id,
          from: {
            mainStage: currentApplication.currentMainStage,
            subStage: currentApplication.currentSubStage,
            stageStatus: currentApplication.currentStageStatus,
            applicationStatus: currentApplication.status
          },
          to: {
            mainStage: updatedApplication.currentMainStage,
            subStage: updatedApplication.currentSubStage,
            stageStatus: updatedApplication.currentStageStatus,
            applicationStatus: updatedApplication.status
          },
          context: {
            userId: req.user?.id || 'unknown',
            userName: req.user?.name || 'Unknown User',
            reason: 'Application update',
            notes: `Application updated via PUT /applications/${req.params.id}`,
            isSystemGenerated: false
          }
        });
      } catch (auditError) {
        console.error('Failed to log audit trail:', auditError);
        // Don't fail the request if audit logging fails
      }
    }
    
    res.json(updatedApplication);
  } catch (err) {
    next(err);
  }
});
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 1000, stage, status, programmeId, corporateSponsorId } = req.query;
    const filter = {};
    
    if (stage) {
      if (stage.includes(',')) {
        const stages = stage.split(',');
        filter.currentMainStage = { $in: stages };
      } else {
        filter.currentMainStage = stage;
      }
    }
    if (status) filter.status = status;
    if (programmeId) filter.programmeId = programmeId;
    if (corporateSponsorId) filter.corporateSponsorId = corporateSponsorId;

    console.log('Public applications filter:', filter);
    
    const applications = await Application.find(filter)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();

    console.log(`Found ${applications.length} public applications`);
    
    const count = await Application.countDocuments(filter);

    res.json({
      total: count,
      page: Number(page),
      totalPages: Math.ceil(count / limit),
      data: applications.map(app => ({
        ...app,
        currentMainStage: app.currentMainStage,
        currentSubStage: app.currentSubStage,
        currentStageStatus: app.currentStageStatus,
        businessName: app.businessInfo?.legalName,
        applicantName: `${app.personalInfo?.firstName || ''} ${app.personalInfo?.lastName || ''}`,
        programmeId: app.programmeId,
        corporateSponsorId: app.corporateSponsorId,
        requestedAmount: app.financialInfo?.requestedAmount || app.requestedAmount || app.financialInfo?.fundingAmount || app.fundingAmount
      }))
    });
  } catch (err) {
    console.error('Failed to fetch public applications:', err);
    res.status(500).json({ message: 'Failed to load applications' });
  }
});

// Get filter options endpoint - returns actual values from database
router.get('/filter-options', async (req, res, next) => {
  try {
    console.log('Fetching filter options from actual database data (public endpoint)');
    
    // Get all applications to analyze actual data
    const allApplications = await Application.find({})
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();
    
    console.log(`Analyzing ${allApplications.length} applications for filter options`);
    
    // Extract unique status values from actual data
    const statusValues = new Set();
    const mainStageValues = new Set();
    const subStageValues = new Set();
    
    allApplications.forEach(app => {
      // Collect actual status values
      if (app.status) {
        statusValues.add(app.status);
      }
      
      // Collect actual main stage values
      if (app.currentMainStage) {
        mainStageValues.add(app.currentMainStage);
      }
      
      // Collect actual sub stage values
      if (app.currentSubStage) {
        subStageValues.add(app.currentSubStage);
      }
    });
    
    // Convert sets to arrays and sort
    const uniqueStatuses = Array.from(statusValues).sort();
    const uniqueMainStages = Array.from(mainStageValues).sort();
    const uniqueSubStages = Array.from(subStageValues).sort();
    
    // Map actual status values to display-friendly values
    const statusOptions = uniqueStatuses.map(status => {
      let displayValue = status;
      
      // Map backend status values to frontend display values
      switch (status.toLowerCase()) {
        case 'pending':
        case 'submitted':
        case 'draft':
          displayValue = 'Pending';
          break;
        case 'in-review':
        case 'under-review':
        case 'under_review':
        case 'in_review':
          displayValue = 'In Review';
          break;
        case 'approved':
        case 'accepted':
          displayValue = 'Approved';
          break;
        case 'rejected':
        case 'declined':
          displayValue = 'Rejected';
          break;
        case 'withdrawn-by-applicant':
        case 'withdrawn_by_applicant':
        case 'withdrawn':
          displayValue = 'Withdrawn';
          break;
        case 'on-hold':
        case 'on_hold':
          displayValue = 'On Hold';
          break;
        case 'sent-back':
        case 'sent_back':
          displayValue = 'Sent Back';
          break;
        default:
          // Capitalize first letter and replace underscores/hyphens with spaces
          displayValue = status.charAt(0).toUpperCase() +
                        status.slice(1).toLowerCase().replace(/[_-]/g, ' ');
      }
      
      return {
        value: status,
        display: displayValue
      };
    });
    
    // Map actual main stage values to display-friendly values
    const mainStageOptions = uniqueMainStages.map(stage => {
      let displayValue = stage;
      
      // Map backend stage values to frontend display values
      switch (stage) {
        case 'ONBOARDING':
          displayValue = 'Onboarding';
          break;
        case 'BUSINESS_CASE_REVIEW':
          displayValue = 'Business Case Review';
          break;
        case 'DUE_DILIGENCE':
          displayValue = 'Due Diligence';
          break;
        case 'ASSESSMENT_REPORT':
          displayValue = 'Assessment Report';
          break;
        case 'APPLICATION_APPROVAL':
          displayValue = 'Application Approval';
          break;
        default:
          // Format as title case with spaces
          displayValue = stage.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
      }
      
      return {
        value: stage,
        display: displayValue
      };
    });
    
    // Map actual sub stage values to display-friendly values
    const subStageOptions = uniqueSubStages.map(subStage => {
      let displayValue = subStage;
      
      // Format as title case with spaces
      displayValue = subStage.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(' ');
      
      return {
        value: subStage,
        display: displayValue
      };
    });
    
    // Get active corporate sponsors
    const CorporateSponsor = require('../models/corporate-sponsor');
    const activeSponsors = await CorporateSponsor.find({ status: 'active' })
      .select('_id name')
      .sort({ name: 1 });
    
    const sponsorOptions = activeSponsors.map(sponsor => ({
      value: sponsor._id.toString(),
      display: sponsor.name
    }));
    
    // Get active funding programmes
    const FundingProgramme = require('../models/funding-programme');
    const activePrograms = await FundingProgramme.find({ status: 'active' })
      .select('_id name')
      .sort({ name: 1 });
    
    const programOptions = activePrograms.map(program => ({
      value: program._id.toString(),
      display: program.name
    }));
    
    const filterOptions = {
      statuses: statusOptions,
      mainStages: mainStageOptions,
      subStages: subStageOptions,
      corporateSponsors: sponsorOptions,
      programmes: programOptions,
      metadata: {
        totalApplications: allApplications.length,
        uniqueStatusCount: uniqueStatuses.length,
        uniqueMainStageCount: uniqueMainStages.length,
        uniqueSubStageCount: uniqueSubStages.length,
        activeSponsorCount: activeSponsors.length,
        activeProgramCount: activePrograms.length,
        generatedAt: new Date().toISOString()
      }
    };
    
    console.log('Filter options generated (public):', {
      statusCount: statusOptions.length,
      mainStageCount: mainStageOptions.length,
      subStageCount: subStageOptions.length,
      sponsorCount: sponsorOptions.length,
      programCount: programOptions.length
    });
    
    res.json(filterOptions);
  } catch (err) {
    console.error('Error fetching filter options (public):', err);
    res.status(500).json({
      message: 'Failed to load filter options',
      error: err.message
    });
  }
});

module.exports = router;