# Scorecard Data Missing Fix

## Problem Description
The Stage Manager is now working and showing the stage hierarchy, but the scorecard section shows empty fields with only column headers (CRITERIA, WEIGHT, SCORE) but no actual scorecard criteria or evaluation fields.

From the screenshot, I can see:
- ✅ Stage Manager is working properly
- ✅ Stage hierarchy is displayed correctly
- ✅ Due Diligence substeps are showing
- ❌ Scorecard tab shows empty criteria table

## Root Cause Analysis

The scorecard data is likely missing because:

1. **Scorecard template not assigned** to the application
2. **Scorecard criteria not initialized** for the current stage
3. **Database missing scorecard template data**
4. **API endpoint not returning scorecard information**
5. **Frontend not loading scorecard data properly**

## Diagnostic Steps

### Step 1: Check if Scorecard Templates Exist
```bash
# Connect to MongoDB and check for scorecard templates
mongo your-database-name

# Check if scorecard templates exist
db.scorecardtemplates.find().pretty()

# Check if there are any scorecard templates for Due Diligence
db.scorecardtemplates.find({stage: "DUE_DILIGENCE"}).pretty()
```

### Step 2: Check Application Scorecard Assignment
```bash
# Check if APP-2025-001 has scorecard data
db.applications.findOne({id: "APP-2025-001"}, {scorecard: 1, scorecardTemplate: 1})
```

### Step 3: Test Scorecard API Endpoint
```bash
# Test if scorecard endpoint exists and returns data
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001/scorecard" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Or test the scores endpoint mentioned in the applications.js
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001/scores" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Fixes

### Fix 1: Create Default Scorecard Template
If no scorecard templates exist, create a default one:

```javascript
// MongoDB script to create default scorecard template
db.scorecardtemplates.insertOne({
  name: "Due Diligence Data Validation Scorecard",
  stage: "DUE_DILIGENCE",
  subStage: "DATA_VALIDATION",
  version: "1.0",
  status: "active",
  criteria: [
    {
      id: "DV001",
      category: "Identity Verification",
      text: "Valid ID document provided and verified",
      description: "Check if the applicant has provided a valid, current ID document",
      weight: 5,
      required: true,
      flags: ["critical"],
      scoringGuidelines: {
        5: "All documents provided, verified, and current",
        4: "Documents provided and verified, minor issues",
        3: "Documents provided but verification pending",
        2: "Documents provided but have issues",
        1: "Documents missing or invalid"
      }
    },
    {
      id: "DV002", 
      category: "Business Registration",
      text: "Business registration documents verified",
      description: "Verify business registration with relevant authorities",
      weight: 4,
      required: true,
      flags: ["critical"],
      scoringGuidelines: {
        5: "Registration verified and current",
        4: "Registration verified with minor discrepancies",
        3: "Registration pending verification",
        2: "Registration has issues",
        1: "Registration invalid or missing"
      }
    },
    {
      id: "DV003",
      category: "Financial Information",
      text: "Financial statements accuracy verified",
      description: "Cross-check financial statements with supporting documents",
      weight: 4,
      required: true,
      scoringGuidelines: {
        5: "All financial data verified and consistent",
        4: "Financial data mostly accurate",
        3: "Some discrepancies in financial data",
        2: "Significant financial data issues",
        1: "Financial data unreliable"
      }
    },
    {
      id: "DV004",
      category: "Tax Compliance",
      text: "Tax compliance status verified",
      description: "Verify tax registration and compliance status",
      weight: 3,
      required: true,
      scoringGuidelines: {
        5: "Fully tax compliant",
        4: "Minor tax compliance issues",
        3: "Some tax compliance concerns",
        2: "Significant tax issues",
        1: "Non-compliant with tax requirements"
      }
    },
    {
      id: "DV005",
      category: "References",
      text: "Business and personal references verified",
      description: "Contact and verify provided references",
      weight: 3,
      required: false,
      scoringGuidelines: {
        5: "All references verified positively",
        4: "Most references verified positively",
        3: "Mixed reference feedback",
        2: "Concerning reference feedback",
        1: "Negative or no reference verification"
      }
    }
  ],
  createdAt: new Date(),
  updatedAt: new Date()
});
```

### Fix 2: Assign Scorecard Template to Application
```javascript
// Assign the scorecard template to APP-2025-001
db.applications.updateOne(
  {id: "APP-2025-001"},
  {
    $set: {
      scorecardTemplate: "Due Diligence Data Validation Scorecard",
      scorecard: {
        templateName: "Due Diligence Data Validation Scorecard",
        stage: "DUE_DILIGENCE",
        subStage: "DATA_VALIDATION",
        status: "in_progress",
        scores: [
          {
            criteriaId: "DV001",
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null
          },
          {
            criteriaId: "DV002", 
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null
          },
          {
            criteriaId: "DV003",
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null
          },
          {
            criteriaId: "DV004",
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null
          },
          {
            criteriaId: "DV005",
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null
          }
        ],
        overallScore: null,
        completedAt: null
      }
    }
  }
);
```

### Fix 3: Add Scorecard API Endpoint (if missing)
Check if this endpoint exists in `backend/src/routes/applications.js`. If not, add it:

```javascript
// GET /api/v1/applications/:id/scorecard
router.get('/:id/scorecard', authenticateToken, async (req, res) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .populate('scorecardTemplate')
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // If no scorecard assigned, try to get default template for current stage
    if (!application.scorecard && application.currentMainStage && application.currentSubStage) {
      const defaultTemplate = await ScorecardTemplate.findOne({
        stage: application.currentMainStage,
        subStage: application.currentSubStage,
        status: 'active'
      });
      
      if (defaultTemplate) {
        // Initialize scorecard with default template
        const scorecard = {
          templateName: defaultTemplate.name,
          stage: defaultTemplate.stage,
          subStage: defaultTemplate.subStage,
          status: 'not_started',
          scores: defaultTemplate.criteria.map(criteria => ({
            criteriaId: criteria.id,
            category: criteria.category,
            text: criteria.text,
            description: criteria.description,
            weight: criteria.weight,
            required: criteria.required,
            score: null,
            comment: "",
            scoredBy: null,
            scoredAt: null,
            scoringGuidelines: criteria.scoringGuidelines
          })),
          overallScore: null,
          completedAt: null
        };
        
        // Save the initialized scorecard to the application
        await Application.updateOne(
          { id: req.params.id },
          { $set: { scorecard: scorecard } }
        );
        
        return res.json(scorecard);
      }
    }
    
    if (!application.scorecard) {
      return res.status(404).json({ message: 'No scorecard found for this application' });
    }
    
    res.json(application.scorecard);
  } catch (error) {
    console.error('Error fetching scorecard:', error);
    res.status(500).json({ message: 'Failed to load scorecard' });
  }
});

// PUT /api/v1/applications/:id/scorecard
router.put('/:id/scorecard', authenticateToken, async (req, res) => {
  try {
    const { scores, overallScore, status } = req.body;
    
    const updateData = {
      'scorecard.scores': scores,
      'scorecard.status': status || 'in_progress',
      'scorecard.lastUpdated': new Date()
    };
    
    if (overallScore !== undefined) {
      updateData['scorecard.overallScore'] = overallScore;
    }
    
    if (status === 'completed') {
      updateData['scorecard.completedAt'] = new Date();
    }
    
    const result = await Application.updateOne(
      { id: req.params.id },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    res.json({ success: true, message: 'Scorecard updated successfully' });
  } catch (error) {
    console.error('Error updating scorecard:', error);
    res.status(500).json({ message: 'Failed to update scorecard' });
  }
});
```

### Fix 4: Frontend Component Check
The frontend scorecard component should be making the correct API call:

```javascript
// Check in browser console if scorecard data is being loaded
fetch('/api/v1/applications/APP-2025-001/scorecard')
  .then(response => response.json())
  .then(data => {
    console.log('Scorecard data:', data);
  })
  .catch(error => {
    console.error('Scorecard API error:', error);
  });
```

### Fix 5: Quick Database Fix for APP-2025-001
Run this MongoDB command to add scorecard data directly:

```javascript
// Quick fix - add scorecard data to APP-2025-001
db.applications.updateOne(
  {id: "APP-2025-001"},
  {
    $set: {
      scorecard: {
        templateName: "Due Diligence Data Validation Scorecard",
        stage: "DUE_DILIGENCE",
        subStage: "DATA_VALIDATION", 
        status: "in_progress",
        scores: [
          {
            criteriaId: "DV001",
            category: "Identity Verification",
            text: "Valid ID document provided and verified",
            weight: 5,
            required: true,
            score: null,
            comment: ""
          },
          {
            criteriaId: "DV002",
            category: "Business Registration", 
            text: "Business registration documents verified",
            weight: 4,
            required: true,
            score: null,
            comment: ""
          },
          {
            criteriaId: "DV003",
            category: "Financial Information",
            text: "Financial statements accuracy verified", 
            weight: 4,
            required: true,
            score: null,
            comment: ""
          },
          {
            criteriaId: "DV004",
            category: "Tax Compliance",
            text: "Tax compliance status verified",
            weight: 3,
            required: true,
            score: null,
            comment: ""
          }
        ],
        overallScore: null,
        completedAt: null,
        lastUpdated: new Date()
      }
    }
  }
);
```

## Verification Steps

After applying the fix:

1. **Refresh the application page**
2. **Navigate to APP-2025-001 → Application Stages → Scorecard tab**
3. **Check if scorecard criteria are now visible**
4. **Verify you can see the CRITERIA, WEIGHT, and SCORE columns with actual data**

Expected result: The scorecard should show evaluation criteria like:
- Identity Verification (Weight: 5)
- Business Registration (Weight: 4)
- Financial Information (Weight: 4)
- Tax Compliance (Weight: 3)

## Additional Notes

- The scorecard system appears to be designed to work with templates
- Each stage/sub-stage combination should have its own scorecard template
- The frontend component expects scorecard data in a specific format
- Scores can be updated and saved through the interface once data is present

If the issue persists, check the browser console for JavaScript errors when loading the scorecard tab.
