@echo off
echo Restarting backend server...

REM Kill all Node.js processes
echo Killing all Node.js processes...
taskkill /F /IM node.exe

REM Wait for processes to terminate
timeout /t 2

REM Start MongoDB server
echo Starting MongoDB server...
start /B node start-mongodb-server.js

REM Wait for MongoDB to start
timeout /t 5

REM Start backend server
echo Starting backend server...
start /B npm run dev

echo Backend server restarted successfully!