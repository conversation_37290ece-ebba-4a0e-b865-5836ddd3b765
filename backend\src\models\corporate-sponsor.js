const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const addressSchema = new Schema({
  street: { type: String },
  city: { type: String },
  province: { type: String },
  postalCode: { type: String },
  country: { type: String, default: 'South Africa' }
});

const contactPersonSchema = new Schema({
  name: { type: String },
  email: { type: String },
  phone: { type: String },
  position: { type: String }
});

const corporateSponsorSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  logo: { 
    type: String 
  },
  contactPerson: contactPersonSchema,
  address: addressSchema,
  website: { 
    type: String,
    trim: true
  },
  industry: {
    type: String,
    trim: true
  },
  totalFunding: {
    type: Number,
    default: 0
  },
  activePrograms: {
    type: Number,
    default: 0
  },
  totalBeneficiaries: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { timestamps: true });

// Add text index for search functionality
corporateSponsorSchema.index({ 
  name: 'text', 
  description: 'text', 
  industry: 'text' 
});

// Virtual for URL
corporateSponsorSchema.virtual('url').get(function() {
  return `/corporate-sponsors/${this._id}`;
});

module.exports = mongoose.model('CorporateSponsor', corporateSponsorSchema);
