/**
 * Migration script to add template versioning to existing interviews
 * 
 * This script updates all existing interviews to include templateId and templateVersion fields
 * which are required for the new question-answer joining functionality.
 */

const mongoose = require('mongoose');
const Interview = require('../models/interview');
const { QuestionnaireTemplate } = require('../questionnaire-template-model');

// Default template ID to use if no specific mapping is available
const DEFAULT_TEMPLATE_ID = 'template-default';
const DEFAULT_VERSION = '1.0.0';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    const connectionString = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app';
    await mongoose.connect(connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Create default template if it doesn't exist
async function ensureDefaultTemplateExists() {
  try {
    let template = await QuestionnaireTemplate.findOne({ id: DEFAULT_TEMPLATE_ID });
    
    if (!template) {
      console.log('Creating default template...');
      
      template = new QuestionnaireTemplate({
        name: 'Default Interview Template',
        description: 'Default template created during migration',
        version: DEFAULT_VERSION,
        isActive: true,
        createdBy: 'System',
        applicableIndustries: ['All'],
        applicableFundingTypes: ['All'],
        applicableLifecycleStages: ['All'],
        sections: [
          {
            id: 'section1',
            title: 'General Information',
            description: 'Basic information about the company',
            order: 1,
            questions: [
              {
                id: 'q1',
                text: 'What is the company name?',
                type: 'OPEN_ENDED',
                required: true
              },
              {
                id: 'q2',
                text: 'What industry does the company operate in?',
                type: 'MULTIPLE_CHOICE',
                required: true,
                options: ['Technology', 'Manufacturing', 'Agriculture', 'Finance', 'Other']
              }
            ],
            isRequired: true
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await template.save();
      console.log('Default template created successfully');
    } else {
      console.log('Default template already exists');
    }
    
    return template;
  } catch (error) {
    console.error('Error ensuring default template exists:', error);
    throw error;
  }
}

// Update interviews to include template information
async function updateInterviews() {
  try {
    // Get default template
    const defaultTemplate = await ensureDefaultTemplateExists();
    
    // Find all interviews without templateId
    const interviews = await Interview.find({ 
      $or: [
        { templateId: { $exists: false } },
        { templateId: null }
      ]
    });
    
    console.log(`Found ${interviews.length} interviews to update`);
    
    // Update each interview
    let updatedCount = 0;
    for (const interview of interviews) {
      interview.templateId = DEFAULT_TEMPLATE_ID;
      interview.templateVersion = DEFAULT_VERSION;
      await interview.save();
      updatedCount++;
      
      if (updatedCount % 100 === 0) {
        console.log(`Updated ${updatedCount} interviews...`);
      }
    }
    
    console.log(`Successfully updated ${updatedCount} interviews with template information`);
  } catch (error) {
    console.error('Error updating interviews:', error);
    throw error;
  }
}

// Validate the migration
async function validateMigration() {
  try {
    const totalCount = await Interview.countDocuments();
    const missingTemplateCount = await Interview.countDocuments({ 
      $or: [
        { templateId: { $exists: false } },
        { templateId: null }
      ]
    });
    
    if (missingTemplateCount > 0) {
      console.warn(`Warning: ${missingTemplateCount} out of ${totalCount} interviews still missing template information`);
    } else {
      console.log(`Validation successful: All ${totalCount} interviews have template information`);
    }
  } catch (error) {
    console.error('Error validating migration:', error);
    throw error;
  }
}

// Run the migration
async function runMigration() {
  try {
    await connectToDatabase();
    await updateInterviews();
    await validateMigration();
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Execute the migration
runMigration();
