/**
 * Stage Status Service
 * 
 * Provides functionality for managing application stage statuses,
 * including status transitions, validation, and administrative overrides.
 */

const Application = require('../models/application');
const { 
  ApplicationMainStage, 
  ApplicationSubStage, 
  StageStatus,
  SubStageToMainStageMap,
  MainStageToSubStagesMap,
  ValidStatusTransitions
} = require('../models/stage-status-enums');

class StageStatusService {
  /**
   * Get the current stage status for an application
   * 
   * @param {string} applicationId - The application ID
   * @returns {Promise<Object>} The current stage status
   */
  async getCurrentStageStatus(applicationId) {
    const application = await Application.findOne({ id: applicationId });
    
    if (!application) {
      throw new Error('Application not found');
    }
    
    return {
      mainStage: application.currentMainStage,
      subStage: application.currentSubStage,
      status: application.currentStageStatus
    };
  }
  
  /**
   * Get the full stage hierarchy for an application
   * 
   * @param {string} applicationId - The application ID
   * @returns {Promise<Array>} The stage hierarchy
   */
  async getStageHierarchy(applicationId) {
    const application = await Application.findOne({ id: applicationId });
    
    if (!application) {
      throw new Error('Application not found');
    }
    
    return application.stageHierarchy || [];
  }
  
  /**
   * Update the stage status for an application
   * 
   * @param {string} applicationId - The application ID
   * @param {string} mainStage - The main stage
   * @param {string} subStage - The substage
   * @param {string} status - The new status
   * @param {string} userId - The user making the change
   * @param {string} notes - Notes about the change
   * @returns {Promise<Object>} The updated application
   */
  async updateStageStatus(applicationId, mainStage, subStage, status, userId, notes) {
    // Validate the inputs
    if (!Object.values(ApplicationMainStage).includes(mainStage)) {
      throw new Error(`Invalid main stage: ${mainStage}`);
    }
    
    if (!Object.values(ApplicationSubStage).includes(subStage)) {
      throw new Error(`Invalid substage: ${subStage}`);
    }
    
    if (!Object.values(StageStatus).includes(status)) {
      throw new Error(`Invalid status: ${status}`);
    }
    
    // Verify that the substage belongs to the main stage
    if (SubStageToMainStageMap[subStage] !== mainStage) {
      throw new Error(`Substage ${subStage} does not belong to main stage ${mainStage}`);
    }
    
    // Get the application
    const application = await Application.findOne({ id: applicationId });
    
    if (!application) {
      throw new Error('Application not found');
    }
    
    // Initialize stage hierarchy if it doesn't exist
    if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
      application.stageHierarchy = this._initializeStageHierarchy();
    }
    
    // Get the current status of the substage
    const currentStatus = this._getSubStageStatus(application.stageHierarchy, mainStage, subStage);
    
    // Validate the status transition
    if (!this._isValidStatusTransition(currentStatus, status)) {
      throw new Error(`Invalid status transition from ${currentStatus} to ${status}`);
    }
    
    // Update the status
    this._updateSubStageStatus(
      application.stageHierarchy,
      mainStage,
      subStage,
      status,
      userId,
      notes
    );
    
    // Update the current stage and status if this is the active stage
    if (status === StageStatus.ACTIVE) {
      application.currentMainStage = mainStage;
      application.currentSubStage = subStage;
      application.currentStageStatus = status;
    }
    
    // Add to audit log
    application.stageStatusAuditLog.push({
      timestamp: new Date(),
      mainStage,
      subStage,
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason: notes,
      isOverride: false
    });
    
    // Save the application
    await application.save();
    
    return application;
  }
  
  /**
   * Perform an administrative override of a stage status
   * 
   * @param {string} applicationId - The application ID
   * @param {string} mainStage - The main stage
   * @param {string} subStage - The substage
   * @param {string} status - The new status
   * @param {string} userId - The user making the override
   * @param {string} reason - The reason for the override
   * @returns {Promise<Object>} The updated application
   */
  async adminOverrideStageStatus(applicationId, mainStage, subStage, status, userId, reason) {
    // Validate the inputs
    if (!Object.values(ApplicationMainStage).includes(mainStage)) {
      throw new Error(`Invalid main stage: ${mainStage}`);
    }
    
    if (!Object.values(ApplicationSubStage).includes(subStage)) {
      throw new Error(`Invalid substage: ${subStage}`);
    }
    
    if (!Object.values(StageStatus).includes(status)) {
      throw new Error(`Invalid status: ${status}`);
    }
    
    if (!reason) {
      throw new Error('A reason is required for administrative overrides');
    }
    
    // Get the application
    const application = await Application.findOne({ id: applicationId });
    
    if (!application) {
      throw new Error('Application not found');
    }
    
    // Initialize stage hierarchy if it doesn't exist
    if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
      application.stageHierarchy = this._initializeStageHierarchy();
    }
    
    // Get the current status of the substage
    const currentStatus = this._getSubStageStatus(application.stageHierarchy, mainStage, subStage);
    
    // Update the status (no validation for admin override)
    this._updateSubStageStatus(
      application.stageHierarchy,
      mainStage,
      subStage,
      status,
      userId,
      reason,
      true // isOverride
    );
    
    // Update the current stage and status if this is the active stage
    if (status === StageStatus.ACTIVE) {
      application.currentMainStage = mainStage;
      application.currentSubStage = subStage;
      application.currentStageStatus = status;
    }
    
    // Add to audit log
    application.stageStatusAuditLog.push({
      timestamp: new Date(),
      mainStage,
      subStage,
      fromStatus: currentStatus,
      toStatus: status,
      changedBy: userId,
      reason,
      isOverride: true
    });
    
    // Save the application
    await application.save();
    
    return application;
  }
  
  /**
   * Get the audit log for an application
   * 
   * @param {string} applicationId - The application ID
   * @returns {Promise<Array>} The audit log
   */
  async getStageStatusAuditLog(applicationId) {
    const application = await Application.findOne({ id: applicationId });
    
    if (!application) {
      throw new Error('Application not found');
    }
    
    return application.stageStatusAuditLog || [];
  }
  
  /**
   * Initialize the stage hierarchy
   * 
   * @returns {Array} The initialized stage hierarchy
   * @private
   */
  _initializeStageHierarchy() {
    const stageHierarchy = [];
    
    // Create main stages with default substages
    Object.values(ApplicationMainStage).forEach(mainStage => {
      const mainStageObj = {
        mainStage,
        status: StageStatus.NOT_STARTED,
        subStages: []
      };
      
      // Add all substages for this main stage
      MainStageToSubStagesMap[mainStage].forEach(subStage => {
        mainStageObj.subStages.push({
          subStage,
          status: {
            status: StageStatus.NOT_STARTED
          },
          history: []
        });
      });
      
      stageHierarchy.push(mainStageObj);
    });
    
    return stageHierarchy;
  }
  
  /**
   * Get the current status of a substage
   * 
   * @param {Array} stageHierarchy - The stage hierarchy
   * @param {string} mainStage - The main stage
   * @param {string} subStage - The substage
   * @returns {string} The current status
   * @private
   */
  _getSubStageStatus(stageHierarchy, mainStage, subStage) {
    // Find the main stage
    const mainStageObj = stageHierarchy.find(ms => ms.mainStage === mainStage);
    if (!mainStageObj) return StageStatus.NOT_STARTED;
    
    // Find the substage
    const subStageObj = mainStageObj.subStages.find(ss => ss.subStage === subStage);
    if (!subStageObj) return StageStatus.NOT_STARTED;
    
    return subStageObj.status.status;
  }
  
  /**
   * Update the status of a substage
   * 
   * @param {Array} stageHierarchy - The stage hierarchy
   * @param {string} mainStage - The main stage
   * @param {string} subStage - The substage
   * @param {string} status - The new status
   * @param {string} assignedTo - The user assigned to the stage
   * @param {string} notes - Notes about the change
   * @param {boolean} isOverride - Whether this is an administrative override
   * @private
   */
  _updateSubStageStatus(stageHierarchy, mainStage, subStage, status, assignedTo, notes, isOverride = false) {
    // Find the main stage
    const mainStageObj = stageHierarchy.find(ms => ms.mainStage === mainStage);
    if (!mainStageObj) return;
    
    // Find the substage
    const subStageObj = mainStageObj.subStages.find(ss => ss.subStage === subStage);
    if (!subStageObj) return;
    
    // Update the status
    subStageObj.status = {
      status,
      startedAt: status === StageStatus.ACTIVE ? new Date() : subStageObj.status.startedAt,
      completedAt: status === StageStatus.COMPLETED ? new Date() : subStageObj.status.completedAt,
      assignedTo,
      notes,
      overrideReason: isOverride ? notes : undefined,
      overrideBy: isOverride ? assignedTo : undefined,
      overrideDate: isOverride ? new Date() : undefined
    };
    
    // Add to history
    subStageObj.history.push({
      ...subStageObj.status,
      timestamp: new Date()
    });
    
    // Update main stage status based on substages
    this._updateMainStageStatus(stageHierarchy, mainStage);
  }
  
  /**
   * Update the main stage status based on its substages
   * 
   * @param {Array} stageHierarchy - The stage hierarchy
   * @param {string} mainStage - The main stage
   * @private
   */
  _updateMainStageStatus(stageHierarchy, mainStage) {
    const mainStageObj = stageHierarchy.find(ms => ms.mainStage === mainStage);
    if (!mainStageObj) return;
    
    const subStages = mainStageObj.subStages;
    
    // If all substages are completed, mark main stage as completed
    if (subStages.every(ss => ss.status.status === StageStatus.COMPLETED)) {
      mainStageObj.status = StageStatus.COMPLETED;
      return;
    }
    
    // If any substage is active, mark main stage as active
    if (subStages.some(ss => ss.status.status === StageStatus.ACTIVE)) {
      mainStageObj.status = StageStatus.ACTIVE;
      return;
    }
    
    // If any substage is completed but not all, mark main stage as active
    if (subStages.some(ss => ss.status.status === StageStatus.COMPLETED)) {
      mainStageObj.status = StageStatus.ACTIVE;
      return;
    }
    
    // If all substages are skipped or not applicable, mark main stage as skipped
    if (subStages.every(ss => 
      ss.status.status === StageStatus.SKIPPED || 
      ss.status.status === StageStatus.NOT_APPLICABLE
    )) {
      mainStageObj.status = StageStatus.SKIPPED;
      return;
    }
    
    // Default to not started
    mainStageObj.status = StageStatus.NOT_STARTED;
  }
  
  /**
   * Check if a status transition is valid
   * 
   * @param {string} fromStatus - The current status
   * @param {string} toStatus - The new status
   * @returns {boolean} Whether the transition is valid
   * @private
   */
  _isValidStatusTransition(fromStatus, toStatus) {
    // Same status is always valid
    if (fromStatus === toStatus) return true;
    
    // Check if the transition is valid
    const validTransitions = ValidStatusTransitions[fromStatus] || [];
    return validTransitions.includes(toStatus);
  }
}

module.exports = new StageStatusService();