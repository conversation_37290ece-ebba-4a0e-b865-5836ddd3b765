# Funding Screening App Backend

This is the backend API server for the Funding Screening Application. It provides RESTful API endpoints for managing funding applications.

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or Atlas)

## Setup

1. Install dependencies:

```bash
npm install
```

2. Configure environment variables:

The application uses environment variables for configuration. These are stored in the `.env` file. Make sure to update the values as needed:

```
PORT=3000
MONGODB_URI=mongodb://localhost:27017/funding-screening-app
```

## Database Seeding

To populate the database with sample data, run:

```bash
npm run seed
```

This will clear any existing data and insert sample applications.

## Running the Server

### Development Mode

To run the server in development mode with auto-restart on file changes:

```bash
npm run dev
```

### Production Mode

To run the server in production mode:

```bash
npm start
```

## API Endpoints

### Applications

- `GET /api/applications` - Get all applications
  - Query parameters:
    - `status` - Filter by status (pending, in-review, approved, rejected)
    - `stage` - Filter by current stage

- `GET /api/applications/:id` - Get application by ID

- `POST /api/applications` - Create a new application

- `PUT /api/applications/:id` - Update an application

- `DELETE /api/applications/:id` - Delete an application

- `PUT /api/applications/:id/stage` - Update application stage
  - Request body:
    - `stage` - New stage
    - `notes` - Array of notes to add to the stage

## Project Structure

```
backend/
  ├── src/
  │   ├── models/         # Database models
  │   ├── routes/         # API routes
  │   ├── seed.js         # Database seeding script
  │   └── server.js       # Main server file
  ├── .env                # Environment variables
  ├── package.json        # Project dependencies and scripts
  └── README.md           # Project documentation
