# Administrator User Management Manual
## Screening Portal Application

### Version: 1.0
### Date: January 2025
### Target Audience: System Administrators

---

## Table of Contents

1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Getting Started](#getting-started)
4. [User Creation Guide](#user-creation-guide)
5. [Role Management](#role-management)
6. [User Management Operations](#user-management-operations)
7. [Entity Associations](#entity-associations)
8. [Permission System](#permission-system)
9. [Security & Best Practices](#security--best-practices)
10. [API Reference](#api-reference)
11. [Troubleshooting](#troubleshooting)

---

## Introduction

This manual provides comprehensive guidance for system administrators on managing users, roles, and permissions within the Screening Portal application. The system implements a sophisticated role-based access control (RBAC) system with entity-specific filtering to ensure data security and appropriate access levels.

### Key Features
- **Multi-tier Role System**: Support for various organizational roles
- **Entity-based Access Control**: Users can be linked to specific corporate sponsors, programmes, or service providers
- **Fine-grained Permissions**: Resource and action-based permission system
- **Dual Authentication**: Legacy role support with modern JWT-based authentication
- **Audit Trail**: Complete tracking of user creation and modifications

---

## System Overview

### Architecture Components
- **User Model**: Core user information and associations
- **Role Model**: Predefined roles with associated permissions
- **Permission Model**: Fine-grained access control definitions
- **Entity Models**: Corporate Sponsors, Funding Programmes, Service Providers
- **Authentication Middleware**: JWT-based security with role validation

### Organization Types
1. **20/20 Insight**: Internal organization users
2. **Corporate Sponsor**: External funding organization users
3. **Service Provider**: Third-party service provider users
4. **SME**: Small and Medium Enterprise applicants

---

## Getting Started

### Prerequisites
- Admin or System Administrator role
- Valid JWT authentication token
- Access to the admin panel or API endpoints

### Default Admin Credentials
```
Username: <EMAIL>
Email: <EMAIL>
Password: Admin123!
Role: SYSTEM_ADMINISTRATOR
```

**⚠️ IMPORTANT: Change default passwords immediately after first login**

### Accessing User Management in the UI

#### Navigation Path
1. **Login** to the application using admin credentials
2. **Navigate** to User Management via:
   - **Primary URL**: `/user-management`
   - **Alternative URL**: `/users` (redirects to user-management)
   - **Menu**: Look for "User Management" in the admin section of the sidebar

#### User Management Interface Features
- **Create User Button**: Located in the top-right header section with "+" icon
- **Search and Filters**: Filter users by name, email, role, status, and organization type
- **User Table**: Displays all users with their details and action buttons
- **Actions Available**:
  - View user details (eye icon)
  - Edit user (edit icon)
  - Toggle user status (activate/deactivate)
  - Delete user (delete icon)
  - View programme assignments

#### Required Permissions
- User must have `admin` or `super-admin` role
- Protected by `RoleBasedAuthGuard`
- Requires authentication via `AuthGuard`

---

## User Creation Guide

### Step-by-Step User Creation

#### 1. Access User Creation Interface
- **UI Method**: Click "Create User" button in User Management page
- **API Endpoint**: `POST /api/admin/users`
- **Required Headers**: 
  ```
  Authorization: Bearer <your-jwt-token>
  Content-Type: application/json
  ```

#### 2. Required Information
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| username | String | Yes | Unique username (min 3 chars) |
| email | String | Yes | Valid email address |
| password | String | Yes | Password (min 8 chars) |
| firstName | String | Yes | User's first name |
| lastName | String | Yes | User's last name |
| role | String | No | Legacy role (default: READ_ONLY_USER) |
| roles | Array | No | New role system (default: ['user']) |
| organizationType | String | No | Organization type (default: '20/20Insight') |

#### 3. Example User Creation Request
```json
{
  "username": "john.doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "role": "LOAN_OFFICER",
  "roles": ["loan-officer", "user"],
  "organizationType": "20/20Insight",
  "permissions": [
    "create:applications",
    "read:applications",
    "update:applications",
    "read:reports"
  ]
}
```

#### 4. User ID Generation
- System automatically generates unique user IDs
- Format: `USER-2025-XXX` (where XXX is a 3-digit number)
- Sequential numbering based on existing user count

#### 5. Password Security
- Passwords are automatically hashed using bcrypt
- Salt rounds: 12 (configurable via BCRYPT_ROUNDS environment variable)
- Original password is never stored

---

## Role Management

### Available Roles

#### System Roles (20/20 Insight)
| Role | Code | Description | Key Permissions |
|------|------|-------------|-----------------|
| System Administrator | SYSTEM_ADMINISTRATOR | Full system access | All permissions |
| Manager | MANAGER | Application and user management | Manage applications, create users |
| Loan Officer | LOAN_OFFICER | Application processing | Create/update applications |
| Reviewer | REVIEWER | Application review | Read/update applications |
| Read-Only User | READ_ONLY_USER | View-only access | Read applications and reports |

#### Corporate Roles
| Role | Code | Description |
|------|------|-------------|
| Corporate Reviewer | CORPORATE_REVIEWER | Review corporate applications |
| Corporate Approver | CORPORATE_APPROVER | Approve corporate funding |

#### Programme Roles
| Role | Code | Description |
|------|------|-------------|
| Programme Reviewer | PROGRAMME_REVIEWER | Review programme applications |
| Programme Approver | PROGRAMME_APPROVER | Approve programme funding |

#### Service Provider Roles
| Role | Code | Description |
|------|------|-------------|
| Service Provider Loan Officer | SERVICE_PROVIDER_LOAN_OFFICER | Process loans for service provider |
| Service Provider Reviewer | SERVICE_PROVIDER_REVIEWER | Review service provider applications |
| Service Provider Read-Only | SERVICE_PROVIDER_READ_ONLY | View-only access for service provider |

### Role Assignment Process

#### 1. Single Role Assignment (Legacy)
```json
{
  "role": "LOAN_OFFICER"
}
```

#### 2. Multiple Role Assignment (New System)
```json
{
  "roles": ["loan-officer", "reviewer", "user"]
}
```

#### 3. Role Validation
- System validates role existence before assignment
- Invalid roles are rejected with error message
- Role changes are logged for audit purposes

---

## User Management Operations

### Viewing Users

#### Get All Users
```
GET /api/admin/users
```

**Response includes:**
- User ID and basic information
- Assigned roles and permissions
- Organization associations
- Account status and last login
- Creation and update timestamps

#### Get Specific User
```
GET /api/admin/users/{userId}
```

### Updating Users

#### Update User Information
```
PUT /api/admin/users/{userId}
```

**Updatable Fields:**
- Personal information (name, email)
- Roles and permissions
- Organization associations
- Account status
- Programme assignments

#### Example Update Request
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "roles": ["manager", "user"],
  "isActive": true,
  "organizationType": "CorporateSponsor",
  "corporateSponsorId": "507f1f77bcf86cd799439011"
}
```

### User Status Management

#### Account Status Options
- **active**: User can log in and access system
- **inactive**: User cannot log in
- **suspended**: Temporary access restriction

#### Deactivating Users
```json
{
  "isActive": false,
  "status": "inactive"
}
```

### Deleting Users

#### Delete User Account
```
DELETE /api/admin/users/{userId}
```

**Important Restrictions:**
- Cannot delete the last admin user
- System prevents accidental admin lockout
- Consider deactivation instead of deletion for audit purposes

---

## Entity Associations

### Corporate Sponsor Users

#### Setup Process
1. Create or identify Corporate Sponsor entity
2. Create user with `organizationType: "CorporateSponsor"`
3. Link user to sponsor: `corporateSponsorId: "sponsor_id"`
4. Assign appropriate corporate roles

#### Example Corporate User
```json
{
  "username": "sponsor.user",
  "email": "<EMAIL>",
  "organizationType": "CorporateSponsor",
  "corporateSponsorId": "507f1f77bcf86cd799439011",
  "roles": ["corporate-sponsor-user", "user"],
  "permissions": [
    "read:entity_applications",
    "update:entity_applications",
    "read:entity_reports"
  ]
}
```

#### Access Restrictions
- Can only view applications from their corporate sponsor
- Reports filtered to their organization's data
- Cannot access other sponsors' information

### Programme Users

#### Setup Process
1. Create or identify Funding Programme
2. Create user with programme association
3. Configure programme assignments
4. Set programme-specific roles

#### Programme Assignment Structure
```json
{
  "programmeAssignments": [
    {
      "programmeId": "PROG-2025-001",
      "role": "reviewer"
    },
    {
      "programmeId": "PROG-2025-002", 
      "role": "approver"
    }
  ]
}
```

#### Multiple Programme Support
- Users can be assigned to multiple programmes
- Different roles per programme
- Access automatically filtered by programme assignments

### Service Provider Users

#### Configuration
```json
{
  "organizationType": "ServiceProvider",
  "organizationId": "service_provider_id",
  "roles": ["service-provider-loan-officer", "user"]
}
```

### SME/Applicant Users

#### Applicant Setup
```json
{
  "organizationType": "SME",
  "roles": ["applicant", "user"],
  "permissions": [
    "create:applications",
    "read:own_applications",
    "update:own_applications"
  ]
}
```

---

## Permission System

### Permission Structure

#### Resource-Based Permissions
- **user**: User management operations
- **application**: Application processing
- **report**: Report generation and viewing
- **role**: Role management
- **system**: System administration

#### Action-Based Permissions
- **create**: Create new resources
- **read**: View existing resources
- **update**: Modify existing resources
- **delete**: Remove resources
- **manage**: Full control over resources

### Permission Examples

#### Standard Permissions
```json
[
  "read:own_profile",
  "update:own_profile",
  "create:applications",
  "read:applications",
  "update:applications",
  "read:reports"
]
```

#### Entity-Specific Permissions
```json
[
  "read:entity_applications",
  "update:entity_applications", 
  "read:entity_reports"
]
```

#### Administrative Permissions
```json
[
  "manage:system",
  "manage:users",
  "manage:roles",
  "manage:applications",
  "manage:reports"
]
```

### Permission Validation

#### Middleware Implementation
- `checkPermission`: Validates specific permissions
- `authorizeRole`: Validates role-based access
- `addEntityFilter`: Applies entity-specific filtering
- `checkEntityAccess`: Ensures proper entity associations

---

## Security & Best Practices

### Password Policies

#### Requirements
- Minimum 8 characters
- Mix of uppercase, lowercase, numbers, and symbols recommended
- Regular password updates encouraged
- No password reuse for admin accounts

#### Default Password Pattern
- Format: `[RoleName]123!`
- **Must be changed on first login**
- System enforces password complexity

### Account Security

#### Best Practices
1. **Regular Audit**: Review user accounts monthly
2. **Principle of Least Privilege**: Assign minimum required permissions
3. **Entity Isolation**: Ensure proper entity associations
4. **Session Management**: Monitor active sessions
5. **Failed Login Monitoring**: Track authentication failures

#### Admin Account Protection
- Cannot delete last admin user
- Admin role changes require confirmation
- System administrator actions are logged

### Data Access Control

#### Entity Filtering
- Automatic data filtering based on user associations
- Corporate users see only their organization's data
- Programme users see only assigned programme data
- SME users see only their own applications

#### Audit Trail
- All user creation/modification logged
- Timestamp tracking for all changes
- User action monitoring available

---

## API Reference

### Authentication

#### Login
```
POST /api/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "Admin123!"
}
```

#### Response
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "USER-2025-001",
    "username": "<EMAIL>",
    "roles": ["admin", "user"],
    "permissions": ["manage:system", "manage:users"]
  }
}
```

### User Management Endpoints

#### Create User
```
POST /api/admin/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "new.user",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "New",
  "lastName": "User",
  "roles": ["loan-officer", "user"]
}
```

#### Get All Users
```
GET /api/admin/users
Authorization: Bearer <token>
```

#### Get User by ID
```
GET /api/admin/users/{userId}
Authorization: Bearer <token>
```

#### Update User
```
PUT /api/admin/users/{userId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "Updated",
  "roles": ["manager", "user"],
  "isActive": true
}
```

#### Delete User
```
DELETE /api/admin/users/{userId}
Authorization: Bearer <token>
```

### Role Management Endpoints

#### Get All Roles
```
GET /api/admin/roles
Authorization: Bearer <token>
```

#### Create Role
```
POST /api/admin/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "custom-role",
  "description": "Custom role description",
  "permissions": ["read:applications", "update:applications"]
}
```

### Permission Management

#### Get All Permissions
```
GET /api/admin/permissions
Authorization: Bearer <token>
```

### Error Responses

#### Common Error Codes
- `400`: Bad Request - Validation errors
- `401`: Unauthorized - Invalid or missing token
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - User/role not found
- `409`: Conflict - Username/email already exists
- `500`: Server Error - Internal server error

#### Example Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation error",
    "details": [
      {
        "field": "email",
        "message": "Please include a valid email"
      }
    ]
  }
}
```

---

## Troubleshooting

### Common Issues

#### 1. User Creation Fails
**Problem**: User creation returns validation error
**Solutions**:
- Verify all required fields are provided
- Check email format validity
- Ensure username is unique
- Verify password meets minimum requirements

#### 2. Role Assignment Issues
**Problem**: Role assignment fails or user has incorrect permissions
**Solutions**:
- Verify role exists in system
- Check role spelling and case sensitivity
- Ensure admin has permission to assign roles
- Review role-permission mappings

#### 3. Entity Association Problems
**Problem**: User cannot see expected data
**Solutions**:
- Verify entity associations are correctly set
- Check `corporateSponsorId` or `fundingProgrammeId` values
- Ensure entity exists in database
- Review entity filtering middleware

#### 4. Authentication Issues
**Problem**: User cannot log in or access resources
**Solutions**:
- Verify user account is active (`isActive: true`)
- Check password correctness
- Ensure JWT token is valid and not expired
- Review user roles and permissions

#### 5. Permission Denied Errors
**Problem**: User receives 403 Forbidden errors
**Solutions**:
- Review user's assigned permissions
- Check route-level authorization requirements
- Verify entity-specific access controls
- Ensure user has required role for operation

#### 6. Frontend Build Errors
**Problem**: Application fails to build due to missing components
**Solutions**:
- Check route configurations in `app.routes.ts`
- Ensure all referenced components exist
- Use redirects for deprecated or moved routes
- Verify import paths are correct

### Diagnostic Steps

#### 1. User Account Verification
```bash
# Check user exists and is active
GET /api/admin/users/{userId}

# Verify user roles and permissions
# Check organizationType and entity associations
```

#### 2. Role and Permission Audit
```bash
# List all available roles
GET /api/admin/roles

# List all permissions
GET /api/admin/permissions

# Cross-reference user assignments
```

#### 3. Entity Association Check
```bash
# Verify corporate sponsor exists
GET /api/corporate-sponsors/{sponsorId}

# Check funding programme
GET /api/funding-programmes/{programmeId}

# Review programme assignments
```

### Support Contacts

For technical issues beyond this manual:
1. Check system logs for detailed error messages
2. Review database connectivity and data integrity
3. Contact system development team
4. Escalate to system administrator if needed

---

## Appendix

### Sample User Configurations

#### System Administrator
```json
{
  "username": "sysadmin",
  "email": "<EMAIL>",
  "password": "AdminSecure123!",
  "firstName": "System",
  "lastName": "Administrator",
  "role": "SYSTEM_ADMINISTRATOR",
  "roles": ["admin", "user"],
  "organizationType": "20/20Insight",
  "permissions": ["manage:system", "manage:users", "manage:roles"]
}
```

#### Corporate Sponsor Manager
```json
{
  "username": "corp.manager",
  "email": "<EMAIL>",
  "password": "CorpManager123!",
  "firstName": "Corporate",
  "lastName": "Manager",
  "role": "CORPORATE_APPROVER",
  "roles": ["corporate-sponsor-user", "user"],
  "organizationType": "CorporateSponsor",
  "corporateSponsorId": "507f1f77bcf86cd799439011",
  "permissions": ["read:entity_applications", "update:entity_applications"]
}
```

#### Programme Officer
```json
{
  "username": "prog.officer",
  "email": "<EMAIL>", 
  "password": "ProgOfficer123!",
  "firstName": "Programme",
  "lastName": "Officer",
  "role": "PROGRAMME_REVIEWER",
  "roles": ["programme-user", "user"],
  "organizationType": "ServiceProvider",
  "programmeAssignments": [
    {
      "programmeId": "PROG-2025-001",
      "role": "reviewer"
    }
  ],
  "permissions": ["read:entity_applications", "update:entity_applications"]
}
```

### Default System Users

The system comes with pre-configured users for testing and initial setup:

| Username | Role | Password | Purpose |
|----------|------|----------|---------|
| <EMAIL> | admin | Admin123! | System administration |
| <EMAIL> | manager | Manager123! | Application management |
| <EMAIL> | loan-officer | LoanOfficer123! | Loan processing |
| <EMAIL> | reviewer | Reviewer123! | Application review |
| <EMAIL> | read-only-user | ReadOnly123! | View-only access |
| <EMAIL> | user | User123! | Basic user access |

**⚠️ SECURITY WARNING: Change all default passwords immediately in production environments**

### Quick Reference

#### User Management URLs
- **Primary**: `/user-management`
- **Alternative**: `/users` (redirects to user-management)
- **User Roles**: `/user-roles`
- **User Role Table**: `/user-role-table`

#### Key UI Components
- **Create User Button**: Top-right header in User Management page
- **Search Filters**: Name, email, role, status, organization type
- **Action Buttons**: View, Edit, Toggle Status, Delete, View Programmes

#### Backend Endpoints Summary
- **Users**: `/api/admin/users` (GET, POST, PUT, DELETE)
- **Roles**: `/api/admin/roles` (GET, POST, PUT, DELETE)
- **Permissions**: `/api/admin/permissions` (GET)
- **Authentication**: `/api/auth/login` (POST)

---

*This manual is maintained by the System Administration team. Last updated: January 2025*
*For updates and additional documentation, refer to the project repository.*
