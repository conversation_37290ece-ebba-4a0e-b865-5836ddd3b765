const express = require('express');
const router = express.Router();
const ServiceProvider = require('../models/service-provider');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Get all service providers
router.get('/', authenticateToken, async (req, res) => {
  try {
    const query = {};
    
    // Handle search
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }
    
    // Handle status filter
    if (req.query.status && ['active', 'inactive'].includes(req.query.status)) {
      query.status = req.query.status;
    }
    
    // Handle type filter
    if (req.query.type && ['individual', 'company'].includes(req.query.type)) {
      query.type = req.query.type;
    }
    
    // Handle specialization filter
    if (req.query.specialization) {
      query.specializations = req.query.specialization;
    }
    
    const providers = await ServiceProvider.find(query)
      .sort({ name: 1 })
      .limit(parseInt(req.query.limit) || 100)
      .skip(parseInt(req.query.skip) || 0);
    
    const total = await ServiceProvider.countDocuments(query);
    
    res.json({
      providers,
      total,
      limit: parseInt(req.query.limit) || 100,
      skip: parseInt(req.query.skip) || 0
    });
  } catch (err) {
    console.error('Error fetching service providers:', err);
    res.status(500).json({ message: 'Error fetching service providers', error: err.message });
  }
});

// Get a specific service provider by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const provider = await ServiceProvider.findById(req.params.id);
    
    if (!provider) {
      return res.status(404).json({ message: 'Service provider not found' });
    }
    
    res.json(provider);
  } catch (err) {
    console.error('Error fetching service provider:', err);
    res.status(500).json({ message: 'Error fetching service provider', error: err.message });
  }
});

// Create a new service provider
router.post('/', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const newProvider = new ServiceProvider(req.body);
    const savedProvider = await newProvider.save();
    
    res.status(201).json(savedProvider);
  } catch (err) {
    console.error('Error creating service provider:', err);
    res.status(400).json({ message: 'Error creating service provider', error: err.message });
  }
});

// Update a service provider
router.put('/:id', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const updatedProvider = await ServiceProvider.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!updatedProvider) {
      return res.status(404).json({ message: 'Service provider not found' });
    }
    
    res.json(updatedProvider);
  } catch (err) {
    console.error('Error updating service provider:', err);
    res.status(400).json({ message: 'Error updating service provider', error: err.message });
  }
});

// Delete a service provider
router.delete('/:id', authenticateToken, authorizeRoles(['admin']), async (req, res) => {
  try {
    const deletedProvider = await ServiceProvider.findByIdAndDelete(req.params.id);
    
    if (!deletedProvider) {
      return res.status(404).json({ message: 'Service provider not found' });
    }
    
    res.json({ message: 'Service provider deleted successfully' });
  } catch (err) {
    console.error('Error deleting service provider:', err);
    res.status(500).json({ message: 'Error deleting service provider', error: err.message });
  }
});

// Get all specializations (for filtering)
router.get('/meta/specializations', authenticateToken, async (req, res) => {
  try {
    const specializations = await ServiceProvider.distinct('specializations');
    res.json(specializations.filter(spec => spec)); // Filter out null/undefined values
  } catch (err) {
    console.error('Error fetching specializations:', err);
    res.status(500).json({ message: 'Error fetching specializations', error: err.message });
  }
});

// Get service provider assignments
router.get('/:id/assignments', authenticateToken, async (req, res) => {
  try {
    // This would typically query a separate assignments collection
    // For now, we'll return a placeholder response
    res.json({
      assignments: [],
      total: 0
    });
  } catch (err) {
    console.error('Error fetching service provider assignments:', err);
    res.status(500).json({ message: 'Error fetching service provider assignments', error: err.message });
  }
});

module.exports = router;
