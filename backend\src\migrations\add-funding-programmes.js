const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Models
const Application = require('../models/application');
const CorporateSponsor = require('../models/corporate-sponsor');
const FundingProgramme = require('../models/funding-programme');

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-app';

async function runMigration() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');

    // Create default corporate sponsor if it doesn't exist
    console.log('Creating default corporate sponsor...');
    let defaultSponsor = await CorporateSponsor.findOne({ name: '20/20 Insight Default Sponsor' });
    
    if (!defaultSponsor) {
      defaultSponsor = new CorporateSponsor({
        id: 'CS-2025-DEFAULT',
        name: '20/20 Insight Default Sponsor',
        description: 'Default corporate sponsor for existing applications',
        contactPerson: {
          name: 'System Administrator',
          email: '<EMAIL>',
          phone: '************',
          position: 'System Administrator'
        },
        status: 'active'
      });
      
      await defaultSponsor.save();
      console.log(`Created default corporate sponsor with ID: ${defaultSponsor.id}`);
    } else {
      console.log(`Using existing default corporate sponsor with ID: ${defaultSponsor.id}`);
    }

    // Create default funding programme if it doesn't exist
    console.log('Creating default funding programme...');
    let defaultProgramme = await FundingProgramme.findOne({ 
      name: '20/20 Insight Default Programme',
      corporateSponsorId: defaultSponsor.id
    });
    
    if (!defaultProgramme) {
      defaultProgramme = new FundingProgramme({
        id: 'FP-2025-DEFAULT',
        name: '20/20 Insight Default Programme',
        description: 'Default funding programme for existing applications',
        corporateSponsorId: defaultSponsor.id,
        objectives: ['Support existing applications'],
        fundingCriteria: ['Default criteria'],
        fundingTypes: ['Grant', 'Loan'],
        eligibilityCriteria: ['Default eligibility'],
        geographicalCoverage: {
          provinces: ['All provinces'],
          municipalities: ['All municipalities']
        },
        sectors: {
          included: ['All sectors'],
          excluded: []
        },
        fundingPurposes: ['Assets Acquisition', 'Working Capital', 'Business Expansion'],
        status: 'active'
      });
      
      await defaultProgramme.save();
      console.log(`Created default funding programme with ID: ${defaultProgramme.id}`);
    } else {
      console.log(`Using existing default funding programme with ID: ${defaultProgramme.id}`);
    }

    // Update existing applications that don't have programme and sponsor references
    console.log('Updating existing applications...');
    const applications = await Application.find({
      $or: [
        { programmeId: { $exists: false } },
        { corporateSponsorId: { $exists: false } }
      ]
    });
    
    console.log(`Found ${applications.length} applications to update`);
    
    let updatedCount = 0;
    for (const application of applications) {
      application.programmeId = defaultProgramme.id;
      application.corporateSponsorId = defaultSponsor.id;
      
      // Initialize approval workflow if it doesn't exist
      if (!application.approvalWorkflow) {
        application.approvalWorkflow = {
          currentStep: 'ANALYST_REPORT',
          steps: [],
          committeeMeetings: []
        };
      }
      
      // Ensure required businessInfo fields are set
      if (!application.businessInfo) {
        application.businessInfo = {};
      }
      
      if (!application.businessInfo.startTradingDate) {
        application.businessInfo.startTradingDate = new Date();
      }
      
      if (!application.businessInfo.entityType) {
        application.businessInfo.entityType = 'Pty Ltd';
      }
      
      if (!application.businessInfo.cipcRegistrationNumber) {
        application.businessInfo.cipcRegistrationNumber = 'DEFAULT-' + application.id;
      }
      
      if (!application.businessInfo.legalName) {
        application.businessInfo.legalName = 'Default Business Name';
      }
      
      if (!application.businessInfo.tradingName) {
        application.businessInfo.tradingName = 'Default Trading Name';
      }
      
      // Use findOneAndUpdate to bypass validation
      await Application.findOneAndUpdate(
        { _id: application._id },
        {
          programmeId: application.programmeId,
          corporateSponsorId: application.corporateSponsorId,
          approvalWorkflow: application.approvalWorkflow,
          businessInfo: application.businessInfo
        },
        { new: true }
      );
      updatedCount++;
      
      if (updatedCount % 10 === 0) {
        console.log(`Updated ${updatedCount} applications...`);
      }
    }
    
    console.log(`Migration complete. Updated ${updatedCount} applications.`);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
