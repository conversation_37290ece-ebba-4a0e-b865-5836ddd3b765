/**
 * Models Index
 * 
 * This file ensures all models are properly registered with Mongoose
 * before they are referenced by other models or routes.
 * 
 * Import this file at the beginning of server initialization to prevent
 * "Schema hasn't been registered for model" errors.
 */

// Import all models in the correct order
// Models with no dependencies should be imported first
const CorporateSponsor = require('./corporate-sponsor');
const FundingProgramme = require('./funding-programme');
const User = require('./user');
const CommitteeMeeting = require('./committee-meeting');

// Models that depend on other models should be imported after their dependencies
const Application = require('./application');
const ApprovalWorkflow = require('./approval-workflow');
const SiteVisit = require('./site-visit');
const Report = require('./report');
const ProgrammeAssignment = require('./programme-assignment');
const Scorecard = require('./scorecard');
const ScorecardVersion = require('./scorecard-version');

// Export all models
module.exports = {
  CorporateSponsor,
  FundingProgramme,
  User,
  CommitteeMeeting,
  Application,
  ApprovalWorkflow,
  SiteVisit,
  Report,
  ProgrammeAssignment,
  Scorecard,
  ScorecardVersion
};