# Application Stage Management & Workflow Management User Manual

## Table of Contents

1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Application Stage Management](#application-stage-management)
4. [Workflow Management](#workflow-management)
5. [Integration Guide](#integration-guide)
6. [API Reference](#api-reference)
7. [Troubleshooting](#troubleshooting)
8. [Appendices](#appendices)

---

## Introduction

This manual provides comprehensive guidance for using the Application Stage Management and Workflow Management systems within the Screening Portal. These systems work together to manage the complete lifecycle of funding applications from initial submission through final approval.

### Target Audience
- Application Reviewers
- Workflow Administrators
- System Administrators
- Business Analysts
- IT Support Staff

### Prerequisites
- Access to the Screening Portal
- Appropriate user permissions
- Basic understanding of funding application processes

---

## System Overview

The Screening Portal uses a sophisticated two-tier management system:

1. **Application Stage Manager**: Handles the detailed progression of applications through predefined stages
2. **Workflow Manager**: Orchestrates the overall workflow process and automation rules

### Key Benefits
- **Standardized Process**: Consistent application processing across all programmes
- **Audit Trail**: Complete tracking of all changes and decisions
- **Flexibility**: Configurable workflows for different programme types
- **Automation**: Reduced manual effort through automated transitions and notifications
- **Visibility**: Real-time status tracking and reporting

---

## Application Stage Management

### Architecture Overview

The Application Stage Manager implements a **three-level hierarchy**:

```
Main Stage (5 levels)
├── Sub-Stage (17 total)
    └── Status (5 types)
```

### Main Stages

| Stage | Description | Purpose |
|-------|-------------|---------|
| **ONBOARDING** | Initial application processing | Validate applicant eligibility and basic requirements |
| **BUSINESS_CASE_REVIEW** | Document analysis and evaluation | Assess business viability and documentation completeness |
| **DUE_DILIGENCE** | Detailed investigation | Conduct thorough evaluation including interviews and site visits |
| **ASSESSMENT_REPORT** | Report compilation and review | Create and validate assessment documentation |
| **APPLICATION_APPROVAL** | Final approval process | Corporate-level approval through committee processes |

### Sub-Stages Detail

#### ONBOARDING
- **BENEFICIARY_REGISTRATION**: Verify applicant identity and basic eligibility
- **PRE_SCREENING**: Initial assessment of application completeness

#### BUSINESS_CASE_REVIEW
- **DOCUMENT_COLLECTION**: Gather all required documentation
- **DESKTOP_ANALYSIS**: Analyze financial and business information

#### DUE_DILIGENCE
- **DATA_VALIDATION**: Verify accuracy of submitted information
- **SME_INTERVIEW**: Conduct subject matter expert interviews
- **SITE_VISIT**: Physical inspection of business premises

#### ASSESSMENT_REPORT
- **REPORT_COMPLETION**: Compile comprehensive assessment report
- **REPORT_QUALITY_CHECK**: Internal quality assurance review
- **REPORT_REVIEW**: Management review of assessment findings

#### APPLICATION_APPROVAL
- **CORPORATE_APPROVAL_1**: First level corporate approval
- **CORPORATE_APPROVAL_2**: Second level corporate approval
- **CORPORATE_APPROVAL_3**: Third level corporate approval
- **CORPORATE_APPROVAL_4**: Final corporate approval

### Stage Statuses

| Status | Description | When to Use |
|--------|-------------|-------------|
| **NOT_STARTED** | Stage has not begun | Default state for future stages |
| **ACTIVE** | Stage is currently in progress | Work is actively being performed |
| **COMPLETED** | Stage has been finished | All requirements have been met |
| **SKIPPED** | Stage was bypassed | Not applicable for this application |
| **NOT_APPLICABLE** | Stage doesn't apply | Business rules exclude this stage |

### Stage Operations

#### Viewing Current Stage Status

1. Navigate to the Applications list
2. Select the application to view
3. The current stage information is displayed in the application header:
   - Main Stage
   - Sub-Stage
   - Current Status
   - Assigned User
   - Time in Current Stage

#### Updating Stage Status

**Standard Progression:**
1. Open the application details
2. Click on "Stage Manager" tab
3. Select the target sub-stage
4. Choose the new status
5. Add notes explaining the change
6. Click "Update Stage"

**Administrative Override:**
1. Access the Stage Manager with admin privileges
2. Select "Override Stage" option
3. Choose any target stage/sub-stage combination
4. Provide detailed justification
5. Confirm the override action

#### Stage Transition Rules

**Valid Transitions:**
- NOT_STARTED → ACTIVE, SKIPPED, NOT_APPLICABLE
- ACTIVE → COMPLETED, SKIPPED, NOT_APPLICABLE
- COMPLETED → ACTIVE (reopening)
- SKIPPED → ACTIVE, NOT_STARTED
- NOT_APPLICABLE → NOT_STARTED, ACTIVE

**Business Rules:**
- Sequential progression is recommended but not enforced
- Administrative overrides are logged and audited
- Stage completion may trigger automated actions
- Some stages may have prerequisites that must be met

### Audit Trail

Every stage change is automatically logged with:
- **Timestamp**: When the change occurred
- **User**: Who made the change
- **From/To States**: Previous and new stage information
- **Reason**: Explanation for the change
- **Duration**: Time spent in previous stage
- **Override Flag**: Whether this was an administrative override

#### Viewing Audit Trail

1. Open application details
2. Navigate to "Audit Trail" tab
3. Review chronological list of all changes
4. Filter by date range, user, or stage type
5. Export audit data for reporting

### Best Practices

#### For Reviewers
- **Always add meaningful notes** when changing stages
- **Complete stages in logical order** when possible
- **Use SKIPPED status** only when stage is truly not needed
- **Communicate with team** before making significant stage changes

#### For Administrators
- **Document override reasons** thoroughly
- **Monitor stage progression** for bottlenecks
- **Review audit trails** regularly for compliance
- **Train users** on proper stage management procedures

---

## Workflow Management

### Workflow Types

The system supports four types of workflows:

#### Sequential Workflows
- **Description**: Stages must be completed in order
- **Use Case**: Standard application processing
- **Validation**: Prevents skipping ahead without completion
- **Example**: Onboarding → Business Review → Due Diligence → Assessment → Approval

#### Parallel Workflows
- **Description**: Multiple stages can be processed simultaneously
- **Use Case**: Independent review processes
- **Validation**: Allows concurrent stage progression
- **Example**: Document Collection + Data Validation running in parallel

#### Conditional Workflows
- **Description**: Stage progression based on application data
- **Use Case**: Different paths for different application types
- **Validation**: Evaluates conditions before allowing transitions
- **Example**: High-value applications require additional approval stages

#### Priority Workflows
- **Description**: Urgent applications get expedited processing
- **Use Case**: Time-sensitive or strategic applications
- **Validation**: Allows stage skipping with proper authorization
- **Example**: Emergency funding applications

### Workflow Configuration

#### Creating a New Workflow

1. **Access Workflow Manager**
   - Navigate to Admin → Workflow Management
   - Click "Create New Workflow"

2. **Basic Information**
   ```
   Name: [Descriptive workflow name]
   Description: [Purpose and scope]
   Type: [Sequential/Parallel/Conditional/Priority]
   Status: [Draft/Active/Inactive]
   ```

3. **Stage Configuration**
   - Add main stages in logical order
   - Configure sub-stages for each main stage
   - Set requirements and conditions
   - Define parallel groups (if applicable)

4. **Automation Rules**
   - Configure triggers (Stage Complete, Document Upload, etc.)
   - Define actions (Send Notification, Assign Reviewer, etc.)
   - Set conditions for rule execution

5. **Programme Assignment**
   - Select applicable funding programmes
   - Define sector-specific rules
   - Set priority levels

#### Workflow Assignment

**Automatic Assignment:**
- New applications are automatically assigned workflows based on:
  - Programme type
  - Application sector
  - Funding amount
  - Corporate sponsor

**Manual Assignment:**
1. Open application details
2. Navigate to "Workflow" tab
3. Click "Assign Workflow"
4. Select appropriate workflow from list
5. Confirm assignment

#### Workflow Monitoring

**Dashboard Metrics:**
- Applications by workflow stage
- Average processing times
- Bottleneck identification
- Completion rates
- User performance metrics

**Accessing Workflow Analytics:**
1. Navigate to Reports → Workflow Analytics
2. Select date range and filters
3. Choose metric type:
   - Processing Times by Stage
   - Bottleneck Analysis
   - Completion Rates
   - User Performance
4. Generate and export reports

### Automation Rules

#### Trigger Types

| Trigger | Description | Example Use |
|---------|-------------|-------------|
| **StageComplete** | Fires when a stage is completed | Auto-assign next reviewer |
| **DocumentUploaded** | Fires when documents are added | Notify quality checker |
| **TimeElapsed** | Fires after specified time period | Escalate overdue applications |
| **ConditionMet** | Fires when custom conditions are satisfied | Route high-value applications |

#### Action Types

| Action | Description | Configuration |
|--------|-------------|---------------|
| **SendNotification** | Send email/system notification | Recipient, template, timing |
| **AssignReviewer** | Assign user to application | User selection rules, workload balancing |
| **UpdateStatus** | Change application status | Target status, conditions |
| **CreateTask** | Generate task in task management | Task type, assignee, due date |

#### Configuring Automation Rules

1. **Access Rule Configuration**
   - Open workflow editor
   - Navigate to "Automation Rules" section

2. **Create New Rule**
   ```
   Name: [Rule identifier]
   Description: [Rule purpose]
   Trigger: [Select trigger type]
   Trigger Details: [Configure trigger parameters]
   ```

3. **Define Actions**
   - Add one or more actions
   - Configure action parameters
   - Set execution order
   - Test rule logic

4. **Enable and Monitor**
   - Activate the rule
   - Monitor execution logs
   - Adjust parameters as needed

### Workflow Override Procedures

#### When to Use Overrides
- **Emergency Processing**: Urgent applications requiring expedited handling
- **Exception Cases**: Applications that don't fit standard workflow patterns
- **System Issues**: Technical problems requiring manual intervention
- **Business Changes**: New requirements not yet reflected in workflow configuration

#### Override Process

1. **Request Override**
   - Document business justification
   - Identify target stage/status
   - Obtain necessary approvals

2. **Execute Override**
   - Access application with admin privileges
   - Select "Override Workflow" option
   - Choose target stage and status
   - Enter detailed justification
   - Confirm override action

3. **Post-Override Actions**
   - Notify relevant stakeholders
   - Update documentation
   - Monitor for any issues
   - Review for process improvements

---

## Integration Guide

### How Systems Work Together

The Stage Manager and Workflow Manager are tightly integrated:

1. **Workflow Assignment**: Applications are assigned workflows that define their stage progression
2. **Stage Validation**: Workflow rules validate stage transitions
3. **Automation Execution**: Stage changes trigger workflow automation rules
4. **Status Synchronization**: Both systems maintain consistent application status
5. **Audit Coordination**: All changes are logged in unified audit trail

### Data Flow

```
Application Created
    ↓
Workflow Assigned (based on programme/sector)
    ↓
Initial Stage Set (usually ONBOARDING/BENEFICIARY_REGISTRATION)
    ↓
Stage Progression (validated by workflow rules)
    ↓
Automation Rules Triggered (notifications, assignments, etc.)
    ↓
Status Updates (both stage and application level)
    ↓
Audit Trail Updated (complete change history)
```

### Common Integration Scenarios

#### Scenario 1: Standard Application Processing
1. Application submitted
2. Auto-assigned to "Standard SME Workflow"
3. Progresses through stages sequentially
4. Automation rules handle notifications and assignments
5. Completion triggers final approval workflow

#### Scenario 2: High-Priority Application
1. Application flagged as high-priority
2. Assigned to "Priority Processing Workflow"
3. Certain stages run in parallel
4. Expedited approval process
5. Enhanced monitoring and reporting

#### Scenario 3: Exception Handling
1. Application encounters issue in standard workflow
2. Administrator reviews situation
3. Workflow override applied if necessary
4. Alternative processing path followed
5. Exception documented for future improvements

---

## API Reference

### Stage Management Endpoints

#### Get Application Stage Status
```http
GET /api/v1/applications/{id}/stage-status
```

**Response:**
```json
{
  "applicationId": "APP-2025-001",
  "currentMainStage": "DUE_DILIGENCE",
  "currentSubStage": "SME_INTERVIEW",
  "currentStageStatus": "ACTIVE",
  "assignedTo": "<EMAIL>",
  "timeInCurrentStage": "2d 4h 30m",
  "lastUpdated": "2025-01-15T10:30:00Z"
}
```

#### Update Application Stage
```http
PUT /api/v1/applications/{id}/stage-hierarchy
```

**Request Body:**
```json
{
  "mainStage": "DUE_DILIGENCE",
  "subStage": "SME_INTERVIEW",
  "status": "COMPLETED",
  "notes": "Interview completed successfully. Recommend proceeding to site visit."
}
```

#### Get Stage Audit Trail
```http
GET /api/v1/applications/{id}/audit-trail
```

**Response:**
```json
{
  "applicationId": "APP-2025-001",
  "auditEntries": [
    {
      "timestamp": "2025-01-15T10:30:00Z",
      "fromStage": "DUE_DILIGENCE/DATA_VALIDATION",
      "toStage": "DUE_DILIGENCE/SME_INTERVIEW",
      "changedBy": "<EMAIL>",
      "reason": "Data validation completed",
      "duration": "1d 6h 15m",
      "isOverride": false
    }
  ]
}
```

### Workflow Management Endpoints

#### Get All Workflows
```http
GET /api/v1/workflows
```

**Query Parameters:**
- `status`: Filter by workflow status (Active, Inactive, Draft)
- `type`: Filter by workflow type (Sequential, Parallel, etc.)

#### Assign Workflow to Application
```http
POST /api/v1/workflows/assign
```

**Request Body:**
```json
{
  "applicationId": "APP-2025-001",
  "workflowId": "60f7b3b4e1b2c3d4e5f6g7h8"
}
```

#### Get Application Workflow
```http
GET /api/v1/workflows/application/{applicationId}
```

#### Update Application Stage (Workflow Context)
```http
PUT /api/v1/workflows/application/{applicationId}/stage
```

### Error Handling

#### Common Error Codes

| Code | Description | Resolution |
|------|-------------|------------|
| 400 | Invalid stage transition | Check workflow rules and valid transitions |
| 403 | Insufficient permissions | Verify user has appropriate role |
| 404 | Application/Workflow not found | Confirm ID is correct |
| 409 | Workflow already assigned | Use update endpoint instead |
| 500 | Internal server error | Check logs and contact support |

#### Error Response Format
```json
{
  "error": {
    "code": 400,
    "message": "Invalid stage transition",
    "details": "Cannot transition from COMPLETED to NOT_STARTED without override",
    "timestamp": "2025-01-15T10:30:00Z"
  }
}
```

---

## Troubleshooting

### Common Issues and Solutions

#### Issue: Stage Won't Update
**Symptoms:**
- Stage update button is disabled
- Error message about invalid transition

**Possible Causes:**
1. Invalid stage transition according to workflow rules
2. Missing required permissions
3. Application is locked by another user

**Solutions:**
1. Check workflow configuration for valid transitions
2. Verify user has appropriate role permissions
3. Contact administrator for override if necessary
4. Wait for application lock to expire

#### Issue: Workflow Assignment Failed
**Symptoms:**
- Cannot assign workflow to application
- Error about workflow compatibility

**Possible Causes:**
1. Workflow is inactive or in draft status
2. Application already has workflow assigned
3. Workflow not compatible with application programme

**Solutions:**
1. Activate workflow in workflow manager
2. Remove existing workflow assignment first
3. Check programme compatibility settings

#### Issue: Automation Rules Not Firing
**Symptoms:**
- Expected notifications not sent
- Automatic assignments not happening

**Possible Causes:**
1. Automation rules are disabled
2. Trigger conditions not met
3. System configuration issues

**Solutions:**
1. Check rule status in workflow configuration
2. Verify trigger conditions are properly configured
3. Review system logs for errors
4. Test rules in development environment

#### Issue: Audit Trail Missing Entries
**Symptoms:**
- Stage changes not appearing in audit trail
- Incomplete change history

**Possible Causes:**
1. System error during stage update
2. Database connectivity issues
3. Audit service configuration problems

**Solutions:**
1. Retry the stage update operation
2. Check database connection status
3. Contact system administrator
4. Review audit service logs

### Performance Optimization

#### For Large Applications Lists
- Use filtering to reduce data load
- Implement pagination for better performance
- Cache frequently accessed data
- Optimize database queries

#### For Complex Workflows
- Simplify workflow rules where possible
- Use parallel processing for independent stages
- Monitor automation rule performance
- Regular cleanup of old audit data

### Maintenance Procedures

#### Regular Maintenance Tasks
1. **Weekly**: Review audit trails for anomalies
2. **Monthly**: Analyze workflow performance metrics
3. **Quarterly**: Update workflow configurations
4. **Annually**: Archive old application data

#### System Health Checks
- Monitor database performance
- Check automation rule execution
- Verify audit trail completeness
- Test backup and recovery procedures

---

## Appendices

### Appendix A: Stage Definitions Reference

#### Complete Stage Hierarchy

```
ONBOARDING
├── BENEFICIARY_REGISTRATION
│   ├── Identity verification
│   ├── Eligibility confirmation
│   └── Basic documentation check
└── PRE_SCREENING
    ├── Application completeness
    ├── Initial risk assessment
    └── Programme compatibility

BUSINESS_CASE_REVIEW
├── DOCUMENT_COLLECTION
│   ├── Financial statements
│   ├── Business registration
│   ├── Tax compliance
│   └── Supporting documents
└── DESKTOP_ANALYSIS
    ├── Financial analysis
    ├── Business model review
    ├── Market assessment
    └── Risk evaluation

DUE_DILIGENCE
├── DATA_VALIDATION
│   ├── Information verification
│   ├── Reference checks
│   └── Compliance validation
├── SME_INTERVIEW
│   ├── Management assessment
│   ├── Business understanding
│   ├── Technical capability
│   └── Strategic alignment
└── SITE_VISIT
    ├── Physical inspection
    ├── Operational assessment
    ├── Infrastructure review
    └── Compliance verification

ASSESSMENT_REPORT
├── REPORT_COMPLETION
│   ├── Findings compilation
│   ├── Risk assessment
│   ├── Recommendation formulation
│   └── Supporting evidence
├── REPORT_QUALITY_CHECK
│   ├── Technical review
│   ├── Accuracy verification
│   ├── Completeness check
│   └── Format compliance
└── REPORT_REVIEW
    ├── Management review
    ├── Strategic alignment
    ├── Policy compliance
    └── Final recommendations

APPLICATION_APPROVAL
├── CORPORATE_APPROVAL_1
│   ├── Initial review
│   ├── Committee preparation
│   └── Preliminary decision
├── CORPORATE_APPROVAL_2
│   ├── Senior management review
│   ├── Risk committee assessment
│   └── Conditional approval
├── CORPORATE_APPROVAL_3
│   ├── Executive committee review
│   ├── Final risk assessment
│   └── Approval recommendation
└── CORPORATE_APPROVAL_4
    ├── Board presentation
    ├── Final decision
    └── Approval documentation
```

### Appendix B: Status Transition Matrix

| From Status | To Status | Validation Required | Admin Override |
|-------------|-----------|-------------------|----------------|
| NOT_STARTED | ACTIVE | None | No |
| NOT_STARTED | SKIPPED | Justification | No |
| NOT_STARTED | NOT_APPLICABLE | Business rule | No |
| ACTIVE | COMPLETED | Work completion | No |
| ACTIVE | SKIPPED | Justification | Yes |
| ACTIVE | NOT_APPLICABLE | Business rule | Yes |
| COMPLETED | ACTIVE | Reopening reason | Yes |
| SKIPPED | ACTIVE | Reactivation reason | No |
| SKIPPED | NOT_STARTED | Reset reason | No |
| NOT_APPLICABLE | ACTIVE | Business rule change | Yes |
| NOT_APPLICABLE | NOT_STARTED | Business rule change | Yes |

### Appendix C: Workflow Configuration Examples

#### Example 1: Standard SME Workflow
```json
{
  "name": "Standard SME Processing Workflow",
  "type": "Sequential",
  "stages": [
    {
      "mainStage": "ONBOARDING",
      "subStages": [
        {"name": "BENEFICIARY_REGISTRATION", "required": true, "order": 1},
        {"name": "PRE_SCREENING", "required": true, "order": 2}
      ]
    },
    {
      "mainStage": "BUSINESS_CASE_REVIEW",
      "subStages": [
        {"name": "DOCUMENT_COLLECTION", "required": true, "order": 1},
        {"name": "DESKTOP_ANALYSIS", "required": true, "order": 2}
      ]
    }
  ],
  "automationRules": [
    {
      "trigger": "StageComplete",
      "triggerDetails": {"stage": "ONBOARDING-PRE_SCREENING"},
      "actions": [
        {
          "type": "AssignReviewer",
          "details": {"role": "business_analyst", "workloadBalance": true}
        }
      ]
    }
  ]
}
```

#### Example 2: High-Priority Workflow
```json
{
  "name": "High Priority Processing Workflow",
  "type": "Priority",
  "stages": [
    {
      "mainStage": "ONBOARDING",
      "subStages": [
        {"name": "BENEFICIARY_REGISTRATION", "required": true, "order": 1},
        {"name": "PRE_SCREENING", "required": false, "order": 2}
      ]
    }
  ],
  "automationRules": [
    {
      "trigger": "StageComplete",
      "actions": [
        {
          "type": "SendNotification",
          "details": {"template": "urgent_processing", "escalate": true}
        }
      ]
    }
  ]
}
```

### Appendix D: User Roles and Permissions

#### Role Definitions

| Role | Stage Management | Workflow Management | Override Capability |
|------|------------------|-------------------|-------------------|
| **Reviewer** | Update assigned stages | View workflows | No |
| **Senior Reviewer** | Update any stage | View workflows | Limited |
| **Manager** | Full stage management | Create/edit workflows | Yes |
| **Administrator** | Full access | Full access | Yes |
| **Read-Only** | View only | View only | No |

#### Permission Matrix

| Action | Reviewer | Senior Reviewer | Manager | Administrator |
|--------|----------|----------------|---------|---------------|
| View application stages | ✓ | ✓ | ✓ | ✓ |
| Update assigned stages | ✓ | ✓ | ✓ | ✓ |
| Update any stage | ✗ | ✓ | ✓ | ✓ |
| Override stage transitions | ✗ | Limited | ✓ | ✓ |
| View audit trail | ✓ | ✓ | ✓ | ✓ |
| Create workflows | ✗ | ✗ | ✓ | ✓ |
| Modify workflows | ✗ | ✗ | ✓ | ✓ |
| Assign workflows | ✗ | ✓ | ✓ | ✓ |
| Configure automation | ✗ | ✗ | ✓ | ✓ |
| System administration | ✗ | ✗ | ✗ | ✓ |

### Appendix E: Frequently Asked Questions

#### General Questions

**Q: How do I know which workflow is assigned to an application?**
A: Open the application details and check the "Workflow" tab. The assigned workflow name and type will be displayed at the top.

**Q: Can I change the workflow after it's been assigned?**
A: Yes, but this requires manager-level permissions and should be done carefully as it may affect stage progression rules.

**Q: What happens if I make a mistake in stage progression?**
A: Most stage changes can be reversed using administrative override. Contact your manager or administrator for assistance.

#### Technical Questions

**Q: Why are some stage transitions disabled?**
A: Stage transitions are controlled by workflow rules. Sequential workflows require stages to be completed in order, while other restrictions may apply based on business rules.

**Q: How long are audit trails kept?**
A: Audit trails are retained according to company policy, typically 7 years for compliance purposes.

**Q: Can I bulk update multiple applications?**
A: Currently, bulk updates are not supported through the UI. Contact your administrator for bulk operations.

#### Troubleshooting Questions

**Q: The system is running slowly when loading applications. What should I do?**
A: Try using filters to reduce the data load, clear your browser cache, or contact IT support if the issue persists.

**Q: I'm not receiving automated notifications. How do I fix this?**
A: Check your email settings and spam folder. If the issue continues, verify that automation rules are properly configured for your workflow.

**Q: An application seems stuck in a stage. What should I do?**
A: Check if the application is locked by another user, verify you have the necessary permissions, and ensure all required information is complete before attempting to progress.

---

## Document Information

**Version**: 1.0  
**Last Updated**: January 2025  
**Document Owner**: IT Department  
**Review Cycle**: Quarterly  

**Change History**:
- v1.0 (Jan 2025): Initial version created

**Related Documents**:
- System Architecture Guide
- User Access Management Manual
- API Documentation
- Business Process Guidelines

---

*For additional support or questions not covered in this manual, please contact the IT Help Desk or your system administrator.*
