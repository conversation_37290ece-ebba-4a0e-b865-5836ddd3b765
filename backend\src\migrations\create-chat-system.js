const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import models
const ChatRoom = require('../models/chat-room');
const ChatParticipant = require('../models/chat-participant');
const ChatMessage = require('../models/chat-message');
const User = require('../models/user');

async function createChatSystem() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing chat data
    await ChatRoom.deleteMany({});
    await ChatParticipant.deleteMany({});
    await ChatMessage.deleteMany({});
    console.log('Cleared existing chat data');

    // Get users
    const admin = await User.findOne({ username: 'admin' });
    const manager = await User.findOne({ username: 'manager' });
    const loanOfficer = await User.findOne({ username: 'loan.officer' });
    const reviewer = await User.findOne({ username: 'reviewer' });

    if (!admin || !manager || !loanOfficer || !reviewer) {
      throw new Error('Required users not found. Please run seed-auth-data.js first.');
    }

    // Create chat rooms
    const rooms = [];

    // 1. Direct message between admin and manager
    const dmRoom = await ChatRoom.create({
      name: 'Direct Message',
      type: 'DIRECT',
      description: 'Private conversation',
      directMessageUsers: [admin._id, manager._id],
      createdBy: admin._id,
      settings: {
        allowFileSharing: true,
        maxFileSize: 10485760,
        allowedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png']
      }
    });
    rooms.push(dmRoom);

    // 2. General discussion group
    const generalRoom = await ChatRoom.create({
      name: 'General Discussion',
      type: 'GROUP',
      description: 'General team discussions and announcements',
      createdBy: admin._id,
      settings: {
        allowFileSharing: true,
        maxFileSize: 10485760,
        allowedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png']
      }
    });
    rooms.push(generalRoom);

    // 3. Funding committee room
    const committeeRoom = await ChatRoom.create({
      name: 'Funding Committee',
      type: 'COMMITTEE',
      description: 'Funding decision discussions',
      createdBy: admin._id,
      relatedEntity: {
        entityType: 'COMMITTEE',
        entityName: 'Funding Approval Committee'
      },
      settings: {
        allowFileSharing: true,
        maxFileSize: 10485760,
        allowedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx']
      }
    });
    rooms.push(committeeRoom);

    console.log(`Created ${rooms.length} chat rooms`);

    // Create participants
    const participants = [];

    // Direct message participants
    participants.push(await ChatParticipant.create({
      roomId: dmRoom._id,
      userId: admin._id,
      role: 'ADMIN',
      joinedAt: new Date(),
      lastReadAt: new Date(),
      notificationPreference: 'ALL'
    }));

    participants.push(await ChatParticipant.create({
      roomId: dmRoom._id,
      userId: manager._id,
      role: 'MEMBER',
      joinedAt: new Date(),
      lastReadAt: new Date(Date.now() - 3600000), // 1 hour ago
      notificationPreference: 'ALL'
    }));

    // General room participants
    const generalParticipants = [
      { userId: admin._id, role: 'ADMIN' },
      { userId: manager._id, role: 'MODERATOR' },
      { userId: loanOfficer._id, role: 'MEMBER' },
      { userId: reviewer._id, role: 'MEMBER' }
    ];

    for (const participant of generalParticipants) {
      participants.push(await ChatParticipant.create({
        roomId: generalRoom._id,
        userId: participant.userId,
        role: participant.role,
        joinedAt: new Date(),
        lastReadAt: new Date(Date.now() - 7200000), // 2 hours ago
        notificationPreference: 'ALL'
      }));
    }

    // Committee room participants
    const committeeParticipants = [
      { userId: admin._id, role: 'ADMIN' },
      { userId: manager._id, role: 'MODERATOR' },
      { userId: reviewer._id, role: 'MEMBER' }
    ];

    for (const participant of committeeParticipants) {
      participants.push(await ChatParticipant.create({
        roomId: committeeRoom._id,
        userId: participant.userId,
        role: participant.role,
        joinedAt: new Date(),
        lastReadAt: new Date(Date.now() - 86400000), // 1 day ago
        notificationPreference: 'MENTIONS_ONLY'
      }));
    }

    console.log(`Created ${participants.length} participant records`);

    // Create sample messages
    const messages = [];

    // Direct message conversation
    messages.push(await ChatMessage.create({
      roomId: dmRoom._id,
      senderId: admin._id,
      content: {
        text: 'Hi Sarah, I wanted to discuss the upcoming funding applications.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 3600000) // 1 hour ago
    }));

    messages.push(await ChatMessage.create({
      roomId: dmRoom._id,
      senderId: manager._id,
      content: {
        text: 'Sure! I\'ve reviewed the latest batch. There are some promising candidates.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 3300000) // 55 minutes ago
    }));

    // General room messages
    messages.push(await ChatMessage.create({
      roomId: generalRoom._id,
      senderId: admin._id,
      content: {
        text: 'Welcome to the general discussion room!',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 86400000) // 1 day ago
    }));

    messages.push(await ChatMessage.create({
      roomId: generalRoom._id,
      senderId: manager._id,
      content: {
        text: 'Thanks for setting this up. Looking forward to collaborating here.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 82800000) // 23 hours ago
    }));

    messages.push(await ChatMessage.create({
      roomId: generalRoom._id,
      senderId: admin._id,
      content: {
        text: '@manager Great to have you here!',
        type: 'TEXT'
      },
      mentions: [{
        userId: manager._id,
        username: 'manager'
      }],
      createdAt: new Date(Date.now() - 79200000) // 22 hours ago
    }));

    // Committee room messages
    messages.push(await ChatMessage.create({
      roomId: committeeRoom._id,
      senderId: admin._id,
      content: {
        text: 'This room is for funding committee discussions. Our next meeting is scheduled for Thursday.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 172800000) // 2 days ago
    }));

    messages.push(await ChatMessage.create({
      roomId: committeeRoom._id,
      senderId: manager._id,
      content: {
        text: 'I\'ll prepare the application summary for review.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 169200000) // 47 hours ago
    }));

    messages.push(await ChatMessage.create({
      roomId: committeeRoom._id,
      senderId: reviewer._id,
      content: {
        text: 'Looking forward to it. I\'ve completed my initial assessments.',
        type: 'TEXT'
      },
      createdAt: new Date(Date.now() - 165600000) // 46 hours ago
    }));

    console.log(`Created ${messages.length} sample messages`);

    // Update room last activity
    for (const room of rooms) {
      const lastMessage = await ChatMessage.findOne({ roomId: room._id })
        .sort({ createdAt: -1 });
      
      if (lastMessage) {
        room.lastActivity = lastMessage.createdAt;
        await room.save();
      }
    }

    console.log('Chat system created successfully!');
    console.log('\nSummary:');
    console.log(`- ${rooms.length} chat rooms`);
    console.log(`- ${participants.length} participant records`);
    console.log(`- ${messages.length} sample messages`);
    console.log('\nChat rooms created:');
    rooms.forEach(room => {
      console.log(`  - ${room.name} (${room.type})`);
    });

  } catch (error) {
    console.error('Error creating chat system:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the migration
createChatSystem();