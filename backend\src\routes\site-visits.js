const express = require('express');
const router = express.Router();
const SiteVisit = require('../models/site-visit');
const { authenticateToken } = require('../middleware/auth');

// Get all site visits with pagination
router.get('/', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    const siteVisits = await SiteVisit.find()
      .sort({ scheduledDate: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await SiteVisit.countDocuments();
    
    res.json({
      success: true,
      data: siteVisits,
      total: total,
      page: page,
      limit: limit
    });
  } catch (error) {
    console.error('Error fetching site visits:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch site visits',
      error: error.message
    });
  }
});

// Handle scheduler route specifically to avoid treating it as an ID
router.get('/scheduler', async (req, res) => {
  try {
    // Get all site visits for the scheduler
    const siteVisits = await SiteVisit.find().sort({ scheduledDate: -1 });
    
    res.json({
      success: true,
      data: siteVisits,
      message: 'Site visits retrieved for scheduler'
    });
  } catch (error) {
    console.error('Error accessing site visits scheduler:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to access site visits scheduler',
      error: error.message
    });
  }
});

// Get site visits for a specific application
router.get('/application/:applicationId', async (req, res) => {
  try {
    const applicationId = req.params.applicationId;
    const siteVisits = await SiteVisit.find({ applicationId });
    
    res.json({
      success: true,
      data: siteVisits
    });
  } catch (error) {
    console.error(`Error fetching site visits for application ${req.params.applicationId}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch site visits for application',
      error: error.message
    });
  }
});

// Get a specific site visit by ID
router.get('/:id', async (req, res) => {
  try {
    const siteVisit = await SiteVisit.findById(req.params.id);
    
    if (!siteVisit) {
      return res.status(404).json({
        success: false,
        message: 'Site visit not found'
      });
    }
    
    res.json({
      success: true,
      data: siteVisit
    });
  } catch (error) {
    console.error(`Error fetching site visit ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch site visit',
      error: error.message
    });
  }
});

// Create a new site visit
router.post('/', async (req, res) => {
  try {
    const siteVisit = new SiteVisit(req.body);
    const savedSiteVisit = await siteVisit.save();
    
    res.status(201).json({
      success: true,
      data: savedSiteVisit
    });
  } catch (error) {
    console.error('Error creating site visit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create site visit',
      error: error.message
    });
  }
});

// Update a site visit
router.put('/:id', async (req, res) => {
  try {
    const siteVisit = await SiteVisit.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!siteVisit) {
      return res.status(404).json({
        success: false,
        message: 'Site visit not found'
      });
    }
    
    res.json({
      success: true,
      data: siteVisit
    });
  } catch (error) {
    console.error(`Error updating site visit ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to update site visit',
      error: error.message
    });
  }
});

// Delete a site visit
router.delete('/:id', async (req, res) => {
  try {
    const siteVisit = await SiteVisit.findByIdAndDelete(req.params.id);
    
    if (!siteVisit) {
      return res.status(404).json({
        success: false,
        message: 'Site visit not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Site visit deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting site visit ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete site visit',
      error: error.message
    });
  }
});


// Complete a site visit
router.put('/:id/complete', async (req, res) => {
  try {
    const updates = {
      ...req.body,
      status: 'COMPLETED',
      conductedDate: new Date()
    };
    
    const siteVisit = await SiteVisit.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    );
    
    if (!siteVisit) {
      return res.status(404).json({
        success: false,
        message: 'Site visit not found'
      });
    }
    
    res.json({
      success: true,
      data: siteVisit
    });
  } catch (error) {
    console.error(`Error completing site visit ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete site visit',
      error: error.message
    });
  }
});

module.exports = router;
