# Environment Configuration Guide

This guide explains how to set up and use environment variables for the Funding Screening App.

## Environment Files

The application uses different environment files for different deployment scenarios:

- **`.env`** - Default environment file (currently set to development)
- **`.env.development`** - Development environment settings
- **`.env.production`** - Production environment settings  
- **`.env.example`** - Template showing all available variables

## Quick Setup

### For Development
1. The default `.env` file is already configured for development
2. Update database and email settings as needed
3. Start the application: `npm start`

### For Production
1. Copy production settings: `cp .env.production .env`
2. Update all production values (see Security section below)
3. Deploy your application

## Environment Variables Reference

### Core Application Settings
- `NODE_ENV` - Environment mode (development/production)
- `PORT` - Server port (default: 3002)
- `HOST` - Server host (localhost for dev, 0.0.0.0 for prod)
- `API_PREFIX` - API route prefix (/api/v1)

### Database Configuration
- `MONGODB_URI` - Complete MongoDB connection string
- `DB_NAME` - Database name
- `DB_HOST` - Database host
- `DB_PORT` - Database port

### Authentication & Security
- `JWT_SECRET` - Secret key for JWT tokens ⚠️ **CHANGE IN PRODUCTION**
- `JWT_EXPIRATION` - JWT token expiration time
- `REFRESH_TOKEN_SECRET` - Secret for refresh tokens ⚠️ **CHANGE IN PRODUCTION**
- `REFRESH_TOKEN_EXPIRATION` - Refresh token expiration time

### Email Configuration
- `EMAIL_HOST` - SMTP server host
- `EMAIL_PORT` - SMTP server port
- `EMAIL_SECURE` - Use SSL/TLS (true/false)
- `EMAIL_USER` - SMTP username
- `EMAIL_PASSWORD` - SMTP password ⚠️ **SECURE THIS**
- `EMAIL_FROM` - Default sender email address

### File Upload Settings
- `UPLOAD_DIR` - Directory for uploaded files
- `MAX_FILE_SIZE` - Maximum file size in bytes
- `ALLOWED_FILE_TYPES` - Comma-separated list of allowed file extensions

### WebSocket & Notifications
- `WEBSOCKET_PORT` - WebSocket server port
- `WEBSOCKET_URL` - WebSocket connection URL
- `VAPID_PUBLIC_KEY` - VAPID public key for push notifications
- `VAPID_PRIVATE_KEY` - VAPID private key ⚠️ **SECURE THIS**

### Feature Flags
- `ENABLE_MONGODB` - Use MongoDB (true/false)
- `ENABLE_REAL_TIME_NOTIFICATIONS` - Enable WebSocket notifications
- `ENABLE_PUSH_NOTIFICATIONS` - Enable browser push notifications
- `ENABLE_STANDALONE_INTERVIEW` - Enable standalone interview feature

## Security Considerations

### ⚠️ CRITICAL: Change These in Production
1. `JWT_SECRET` - Generate a strong, unique secret
2. `REFRESH_TOKEN_SECRET` - Generate a different strong secret
3. `SESSION_SECRET` - Generate another unique secret
4. `EMAIL_PASSWORD` - Use your actual email service password
5. `VAPID_PRIVATE_KEY` - Generate new VAPID keys for production

### Generate Secure Secrets
```bash
# Generate random secrets (Linux/Mac)
openssl rand -base64 32

# Or use Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Generate VAPID Keys
```bash
# Install web-push globally
npm install -g web-push

# Generate VAPID keys
web-push generate-vapid-keys
```

## Environment-Specific Settings

### Development
- Detailed logging enabled
- Less restrictive rate limiting
- HTTP connections allowed
- Debug features enabled

### Production
- Error-level logging only
- Strict rate limiting
- HTTPS enforced
- Debug features disabled
- Secure cookies enabled

## Switching Environments

### Method 1: Copy Files
```bash
# Switch to production
cp .env.production .env

# Switch to development  
cp .env.development .env
```

### Method 2: Environment Variable
```bash
# Set NODE_ENV and the app will load the appropriate file
export NODE_ENV=production
npm start
```

## Database Setup

### Development (Local MongoDB)
```bash
# Install MongoDB locally or use Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

### Production (MongoDB Atlas or Remote)
Update `MONGODB_URI` with your production database connection string:
```
MONGODB_URI=mongodb+srv://username:<EMAIL>/funding-screening-app
```

## Email Service Setup

### Development (Mailtrap)
1. Sign up at [Mailtrap.io](https://mailtrap.io)
2. Get SMTP credentials from your inbox
3. Update EMAIL_* variables in `.env`

### Production (SendGrid, AWS SES, etc.)
1. Choose your email service provider
2. Get SMTP credentials
3. Update EMAIL_* variables in `.env.production`

## Troubleshooting

### Common Issues
1. **Port already in use**: Change `PORT` in .env
2. **Database connection failed**: Check `MONGODB_URI`
3. **Email not sending**: Verify EMAIL_* settings
4. **JWT errors**: Ensure JWT_SECRET is set

### Debugging
Enable debug logging:
```bash
LOG_LEVEL=debug
ENABLE_DEBUG_LOGGING=true
```

## Best Practices

1. **Never commit .env files** - They're in .gitignore
2. **Use different secrets** for each environment
3. **Rotate secrets regularly** in production
4. **Use environment-specific databases**
5. **Monitor your logs** for security issues
6. **Use HTTPS in production**
7. **Keep dependencies updated**

## Support

If you need help with environment configuration:
1. Check this documentation
2. Review the `.env.example` file
3. Check application logs
4. Contact the development team
