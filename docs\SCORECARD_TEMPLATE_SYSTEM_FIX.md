# Scorecard Template System Fix

## Problem Analysis

From the screenshot, I can see that:

1. **Scorecard Management tool exists** but stages are mislabeled as "criteria"
2. **No sub-stage options** available for creating specific scorecard templates
3. **Template system is not properly configured** for the stage hierarchy
4. **Missing scorecard templates** for each stage/sub-stage combination

## Root Cause

The scorecard template system needs:
1. **Proper stage/sub-stage mapping** in the dropdown options
2. **Scorecard templates created** for each sub-stage
3. **Database seeding** with default scorecard templates
4. **Frontend configuration** to show correct stage/sub-stage options

## Fixes Required

### Fix 1: Update Scorecard Template Categories

The dropdown should show sub-stages, not main stages. Update the frontend to show:

```javascript
// Frontend scorecard template categories should be:
const scorecardCategories = [
  // Onboarding sub-stages
  { value: 'BENEFICIARY_REGISTRATION', label: 'Beneficiary Registration', mainStage: 'ONBOARDING' },
  { value: 'PRE_SCREENING', label: 'Pre-Screening', mainStage: 'ONBOARDING' },
  
  // Business Case Review sub-stages
  { value: 'DOCUMENT_COLLECTION', label: 'Document Collection', mainStage: 'BUSINESS_CASE_REVIEW' },
  { value: 'DESKTOP_ANALYSIS', label: 'Desktop Analysis', mainStage: 'BUSINESS_CASE_REVIEW' },
  
  // Due Diligence sub-stages
  { value: 'DATA_VALIDATION', label: 'Data Validation', mainStage: 'DUE_DILIGENCE' },
  { value: 'SME_INTERVIEW', label: 'SME Interview', mainStage: 'DUE_DILIGENCE' },
  { value: 'SITE_VISIT', label: 'Site Visit', mainStage: 'DUE_DILIGENCE' },
  
  // Assessment Report sub-stages
  { value: 'REPORT_COMPLETION', label: 'Report Completion', mainStage: 'ASSESSMENT_REPORT' },
  { value: 'REPORT_QUALITY_CHECK', label: 'Report Quality Check', mainStage: 'ASSESSMENT_REPORT' },
  { value: 'REPORT_REVIEW', label: 'Report Review', mainStage: 'ASSESSMENT_REPORT' },
  
  // Application Approval sub-stages
  { value: 'CORPORATE_APPROVAL_1', label: 'Corporate Approval Level 1', mainStage: 'APPLICATION_APPROVAL' },
  { value: 'CORPORATE_APPROVAL_2', label: 'Corporate Approval Level 2', mainStage: 'APPLICATION_APPROVAL' },
  { value: 'CORPORATE_APPROVAL_3', label: 'Corporate Approval Level 3', mainStage: 'APPLICATION_APPROVAL' },
  { value: 'CORPORATE_APPROVAL_4', label: 'Corporate Approval Level 4', mainStage: 'APPLICATION_APPROVAL' }
];
```

### Fix 2: Create Default Scorecard Templates

Run this MongoDB script to create scorecard templates for each sub-stage:

```javascript
// Create default scorecard templates for all sub-stages
print("Creating default scorecard templates...");

var templates = [
  {
    name: "Beneficiary Registration Scorecard",
    category: "BENEFICIARY_REGISTRATION",
    mainStage: "ONBOARDING",
    subStage: "BENEFICIARY_REGISTRATION",
    scope: "Programme Specific",
    description: "Evaluation criteria for beneficiary registration process",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "BR001",
        text: "Complete application form submitted",
        description: "All required fields in application form are completed",
        weight: 5,
        required: true,
        category: "Documentation"
      },
      {
        id: "BR002", 
        text: "Valid identification documents provided",
        description: "Current and valid ID documents submitted",
        weight: 5,
        required: true,
        category: "Identity Verification"
      },
      {
        id: "BR003",
        text: "Eligibility criteria met",
        description: "Applicant meets all programme eligibility requirements",
        weight: 4,
        required: true,
        category: "Eligibility"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Pre-Screening Scorecard",
    category: "PRE_SCREENING", 
    mainStage: "ONBOARDING",
    subStage: "PRE_SCREENING",
    scope: "Programme Specific",
    description: "Initial screening evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 65,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "PS001",
        text: "Business concept viability",
        description: "Initial assessment of business concept feasibility",
        weight: 4,
        required: true,
        category: "Business Viability"
      },
      {
        id: "PS002",
        text: "Market opportunity assessment",
        description: "Preliminary market analysis and opportunity evaluation",
        weight: 3,
        required: true,
        category: "Market Analysis"
      },
      {
        id: "PS003",
        text: "Applicant experience and skills",
        description: "Evaluation of applicant's relevant experience and capabilities",
        weight: 4,
        required: true,
        category: "Applicant Assessment"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Data Validation Scorecard",
    category: "DATA_VALIDATION",
    mainStage: "DUE_DILIGENCE", 
    subStage: "DATA_VALIDATION",
    scope: "Programme Specific",
    description: "Comprehensive data validation evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 75,
    allowCustomCriteria: true,
    requireAllCriteria: true,
    criteria: [
      {
        id: "DV001",
        text: "Identity verification completed",
        description: "All identity documents verified and authenticated",
        weight: 5,
        required: true,
        category: "Identity Verification"
      },
      {
        id: "DV002",
        text: "Business registration verified",
        description: "Business registration documents verified with authorities",
        weight: 4,
        required: true,
        category: "Business Registration"
      },
      {
        id: "DV003",
        text: "Financial information accuracy",
        description: "Financial statements and supporting documents verified",
        weight: 4,
        required: true,
        category: "Financial Verification"
      },
      {
        id: "DV004",
        text: "Tax compliance status",
        description: "Tax registration and compliance status verified",
        weight: 3,
        required: true,
        category: "Tax Compliance"
      },
      {
        id: "DV005",
        text: "Reference verification",
        description: "Business and personal references contacted and verified",
        weight: 3,
        required: false,
        category: "References"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "SME Interview Scorecard",
    category: "SME_INTERVIEW",
    mainStage: "DUE_DILIGENCE",
    subStage: "SME_INTERVIEW", 
    scope: "Programme Specific",
    description: "Subject matter expert interview evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "SME001",
        text: "Business knowledge and understanding",
        description: "Applicant demonstrates clear understanding of their business",
        weight: 4,
        required: true,
        category: "Business Knowledge"
      },
      {
        id: "SME002",
        text: "Market awareness",
        description: "Understanding of target market and competition",
        weight: 3,
        required: true,
        category: "Market Knowledge"
      },
      {
        id: "SME003",
        text: "Financial planning capability",
        description: "Ability to explain financial projections and planning",
        weight: 4,
        required: true,
        category: "Financial Planning"
      },
      {
        id: "SME004",
        text: "Management and operational skills",
        description: "Demonstrated management and operational capabilities",
        weight: 4,
        required: true,
        category: "Management Skills"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "Site Visit Scorecard",
    category: "SITE_VISIT",
    mainStage: "DUE_DILIGENCE",
    subStage: "SITE_VISIT",
    scope: "Programme Specific", 
    description: "On-site evaluation criteria",
    status: "active",
    version: "1.0",
    passingScore: 70,
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: [
      {
        id: "SV001",
        text: "Business premises suitability",
        description: "Physical location and premises are suitable for business operations",
        weight: 4,
        required: true,
        category: "Infrastructure"
      },
      {
        id: "SV002",
        text: "Equipment and assets verification",
        description: "Verification of declared equipment and business assets",
        weight: 3,
        required: true,
        category: "Assets"
      },
      {
        id: "SV003",
        text: "Operational readiness",
        description: "Business is operationally ready or has clear implementation plan",
        weight: 4,
        required: true,
        category: "Operations"
      },
      {
        id: "SV004",
        text: "Compliance with regulations",
        description: "Business complies with relevant local regulations and permits",
        weight: 3,
        required: true,
        category: "Compliance"
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Insert all templates
var insertResult = db.scorecardtemplates.insertMany(templates);

if (insertResult.acknowledged) {
  print("SUCCESS: Created " + insertResult.insertedIds.length + " scorecard templates");
  print("Templates created for:");
  templates.forEach(function(template) {
    print("  - " + template.name + " (" + template.subStage + ")");
  });
} else {
  print("ERROR: Failed to create scorecard templates");
}

// Verify templates were created
var count = db.scorecardtemplates.countDocuments();
print("Total scorecard templates in database: " + count);
```

### Fix 3: Update Frontend Category Dropdown

The frontend component needs to be updated to show sub-stages instead of main stages:

```javascript
// Update the scorecard management frontend component
const ScorecardManagement = () => {
  const [categories, setCategories] = useState([]);
  
  useEffect(() => {
    // Fetch sub-stage categories instead of main stages
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/v1/scorecard-templates/categories');
        const data = await response.json();
        setCategories(data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    fetchCategories();
  }, []);
  
  return (
    <div className="scorecard-management">
      <select name="category">
        <option value="">Select Sub-Stage</option>
        {categories.map(category => (
          <option key={category.value} value={category.value}>
            {category.label} ({category.mainStage})
          </option>
        ))}
      </select>
    </div>
  );
};
```

### Fix 4: Add API Endpoint for Categories

Add this endpoint to return proper sub-stage categories:

```javascript
// Add to backend/src/routes/scorecard-templates.js
router.get('/categories', async (req, res) => {
  try {
    const categories = [
      // Onboarding sub-stages
      { value: 'BENEFICIARY_REGISTRATION', label: 'Beneficiary Registration', mainStage: 'ONBOARDING' },
      { value: 'PRE_SCREENING', label: 'Pre-Screening', mainStage: 'ONBOARDING' },
      
      // Business Case Review sub-stages
      { value: 'DOCUMENT_COLLECTION', label: 'Document Collection', mainStage: 'BUSINESS_CASE_REVIEW' },
      { value: 'DESKTOP_ANALYSIS', label: 'Desktop Analysis', mainStage: 'BUSINESS_CASE_REVIEW' },
      
      // Due Diligence sub-stages
      { value: 'DATA_VALIDATION', label: 'Data Validation', mainStage: 'DUE_DILIGENCE' },
      { value: 'SME_INTERVIEW', label: 'SME Interview', mainStage: 'DUE_DILIGENCE' },
      { value: 'SITE_VISIT', label: 'Site Visit', mainStage: 'DUE_DILIGENCE' },
      
      // Assessment Report sub-stages
      { value: 'REPORT_COMPLETION', label: 'Report Completion', mainStage: 'ASSESSMENT_REPORT' },
      { value: 'REPORT_QUALITY_CHECK', label: 'Report Quality Check', mainStage: 'ASSESSMENT_REPORT' },
      { value: 'REPORT_REVIEW', label: 'Report Review', mainStage: 'ASSESSMENT_REPORT' },
      
      // Application Approval sub-stages
      { value: 'CORPORATE_APPROVAL_1', label: 'Corporate Approval Level 1', mainStage: 'APPLICATION_APPROVAL' },
      { value: 'CORPORATE_APPROVAL_2', label: 'Corporate Approval Level 2', mainStage: 'APPLICATION_APPROVAL' },
      { value: 'CORPORATE_APPROVAL_3', label: 'Corporate Approval Level 3', mainStage: 'APPLICATION_APPROVAL' },
      { value: 'CORPORATE_APPROVAL_4', label: 'Corporate Approval Level 4', mainStage: 'APPLICATION_APPROVAL' }
    ];
    
    res.json(categories);
  } catch (error) {
    console.error('Error fetching scorecard categories:', error);
    res.status(500).json({ message: 'Failed to fetch categories' });
  }
});
```

## Implementation Steps

### Step 1: Create Scorecard Templates
```bash
# Run the MongoDB script to create default templates
mongo your-database-name create-scorecard-templates.js
```

### Step 2: Update Frontend Categories
- Update the scorecard management component to use sub-stages
- Add API call to fetch proper categories
- Update dropdown options

### Step 3: Test Template Creation
1. Navigate to Scorecard Management
2. Click "Create New Template"
3. Verify sub-stages appear in Category dropdown
4. Create a test template for "Data Validation"

### Step 4: Verify Application Integration
1. Go to APP-2025-001 → Application Stages → Scorecard
2. Verify it now loads the appropriate scorecard template
3. Test scoring functionality

## Expected Results

After implementing these fixes:

1. **Scorecard Management** will show sub-stages in the Category dropdown
2. **Default templates** will be available for each sub-stage
3. **Application scorecards** will automatically load the correct template
4. **Evaluation criteria** will be properly structured and functional

## Verification Commands

```bash
# Check if templates were created
mongo your-database-name
db.scorecardtemplates.find().count()
db.scorecardtemplates.find({}, {name: 1, subStage: 1}).pretty()

# Check specific template
db.scorecardtemplates.findOne({subStage: "DATA_VALIDATION"})
```

This comprehensive fix addresses the scorecard template system issues and provides a proper foundation for scorecard management across all application stages.
