const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const widgetDataPointSchema = new Schema({
  label: { 
    type: String, 
    required: true,
    trim: true
  },
  value: { 
    type: Number, 
    required: true
  },
  color: { 
    type: String,
    trim: true
  },
  additionalData: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
});

const widgetDataSeriesSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  data: [widgetDataPointSchema],
  color: { 
    type: String,
    trim: true
  }
});

const widgetTableDataSchema = new Schema({
  columns: [{ 
    field: { type: String, required: true },
    header: { type: String, required: true }
  }],
  rows: [{ type: Schema.Types.Mixed }]
});

const widgetDataSchema = new Schema({
  series: [widgetDataSeriesSchema],
  singleValue: { 
    type: Schema.Types.Mixed
  },
  trend: { 
    type: Number
  },
  trendDirection: { 
    type: String, 
    enum: ['up', 'down', 'neutral']
  },
  tableData: { 
    type: widgetTableDataSchema
  },
  mapData: { 
    type: Schema.Types.Mixed
  }
});

const widgetConfigSchema = new Schema({
  chartType: { 
    type: String, 
    enum: ['bar', 'line', 'pie', 'doughnut', 'radar', 'scatter', 'bubble', 'area', 'stacked-bar']
  },
  showLegend: { 
    type: Boolean, 
    default: true
  },
  showLabels: { 
    type: Boolean, 
    default: true
  },
  showValues: { 
    type: Boolean, 
    default: true
  },
  showAxis: { 
    type: Boolean, 
    default: true
  },
  stacked: { 
    type: Boolean, 
    default: false
  },
  colorScheme: [{ 
    type: String,
    trim: true
  }],
  customOptions: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
});

const widgetPositionSchema = new Schema({
  row: { 
    type: Number, 
    required: true,
    min: 0
  },
  col: { 
    type: Number, 
    required: true,
    min: 0
  },
  rowSpan: { 
    type: Number, 
    required: true,
    min: 1,
    default: 1
  },
  colSpan: { 
    type: Number, 
    required: true,
    min: 1,
    default: 1
  }
});

const dashboardWidgetSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  type: { 
    type: String, 
    enum: ['chart', 'metric', 'table', 'map', 'custom'],
    required: true
  },
  size: { 
    type: String, 
    enum: ['small', 'medium', 'large', 'full-width'],
    default: 'medium'
  },
  position: { 
    type: widgetPositionSchema,
    required: true
  },
  filters: { 
    type: Schema.Types.Mixed
  },
  config: { 
    type: widgetConfigSchema,
    required: true
  },
  data: { 
    type: widgetDataSchema
  },
  reportId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Report'
  },
  refreshInterval: { 
    type: Number, 
    default: 0,
    min: 0
  },
  lastRefreshed: { 
    type: Date
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

const dashboardLayoutSchema = new Schema({
  columns: { 
    type: Number, 
    default: 12,
    min: 1
  },
  rowHeight: { 
    type: Number, 
    default: 100,
    min: 50
  }
});

const dashboardSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  widgets: [dashboardWidgetSchema],
  layout: { 
    type: dashboardLayoutSchema,
    default: { columns: 12, rowHeight: 100 }
  },
  isDefault: { 
    type: Boolean, 
    default: false
  },
  isPublic: { 
    type: Boolean, 
    default: false
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  }
}, { timestamps: true });

// Add text index for search functionality
dashboardSchema.index({ 
  name: 'text', 
  description: 'text'
});

// Virtual for URL
dashboardSchema.virtual('url').get(function() {
  return `/dashboards/${this._id}`;
});

// Pre-save hook to ensure only one default dashboard per user
dashboardSchema.pre('save', async function(next) {
  if (this.isDefault) {
    // Find other default dashboards for this user and unset them
    await this.constructor.updateMany(
      { createdBy: this.createdBy, _id: { $ne: this._id }, isDefault: true },
      { $set: { isDefault: false } }
    );
  }
  next();
});

module.exports = mongoose.model('Dashboard', dashboardSchema);
