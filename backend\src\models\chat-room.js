const mongoose = require('mongoose');

const chatRoomSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  type: {
    type: String,
    required: true,
    enum: ['DIRECT', 'GROUP', 'COMMITTEE', 'APPLICATION', 'PROGRAMME'],
    default: 'DIRECT'
  },
  // For direct messages, store both user IDs
  directMessageUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  // Related entities
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['APPLICATION', 'FUNDING_PROGRAMME', 'COMMITTEE', 'CORPORATE_SPONSOR']
    },
    entityId: String,
    entityName: String
  },
  // Room settings
  settings: {
    allowFileSharing: {
      type: Boolean,
      default: true
    },
    allowVoiceNotes: {
      type: Boolean,
      default: true
    },
    messageRetentionDays: {
      type: Number,
      default: 365
    },
    notifyOnMessage: {
      type: Boolean,
      default: true
    },
    allowedFileTypes: [{
      type: String,
      default: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'png', 'jpg', 'jpeg']
    }],
    maxFileSize: {
      type: Number,
      default: 10485760 // 10MB
    }
  },
  // Room status
  isActive: {
    type: Boolean,
    default: true
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatMessage'
  },
  messageCount: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
chatRoomSchema.index({ type: 1, isActive: 1 });
chatRoomSchema.index({ directMessageUsers: 1 });
chatRoomSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });
chatRoomSchema.index({ lastActivity: -1 });
chatRoomSchema.index({ createdBy: 1 });

// Pre-save middleware to generate room ID
chatRoomSchema.pre('save', async function(next) {
  try {
    // Generate room ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(6, '0');
      this.id = `ROOM-2025-${nextNumber}`;
    }
    
    // For direct messages, ensure only 2 users
    if (this.type === 'DIRECT' && this.directMessageUsers.length !== 2) {
      throw new Error('Direct message rooms must have exactly 2 users');
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Virtual for room display name (useful for direct messages)
chatRoomSchema.virtual('displayName').get(function() {
  if (this.type === 'DIRECT') {
    // Display name will be determined by the other user in the conversation
    return null; // Will be populated in the service layer
  }
  return this.name;
});

// Method to check if user is participant
chatRoomSchema.methods.isUserParticipant = async function(userId) {
  const ChatParticipant = mongoose.model('ChatParticipant');
  const participant = await ChatParticipant.findOne({
    roomId: this._id,
    userId: userId,
    isActive: true
  });
  return !!participant;
};

// Method to get participant count
chatRoomSchema.methods.getParticipantCount = async function() {
  const ChatParticipant = mongoose.model('ChatParticipant');
  return await ChatParticipant.countDocuments({
    roomId: this._id,
    isActive: true
  });
};

// Method to get active participants
chatRoomSchema.methods.getActiveParticipants = async function() {
  const ChatParticipant = mongoose.model('ChatParticipant');
  return await ChatParticipant.find({
    roomId: this._id,
    isActive: true
  }).populate('userId', 'username email firstName lastName profilePicture');
};

// Static method to find or create direct message room
chatRoomSchema.statics.findOrCreateDirectRoom = async function(user1Id, user2Id, createdBy) {
  // Sort user IDs to ensure consistent ordering
  const sortedUserIds = [user1Id.toString(), user2Id.toString()].sort();
  
  // Check if room already exists
  let room = await this.findOne({
    type: 'DIRECT',
    directMessageUsers: { $all: sortedUserIds }
  });
  
  if (!room) {
    // Create new room
    room = new this({
      name: 'Direct Message',
      type: 'DIRECT',
      directMessageUsers: sortedUserIds,
      createdBy: createdBy
    });
    await room.save();
    
    // Create participants
    const ChatParticipant = mongoose.model('ChatParticipant');
    for (const userId of sortedUserIds) {
      await ChatParticipant.create({
        roomId: room._id,
        userId: userId,
        role: 'MEMBER',
        joinedAt: new Date()
      });
    }
  }
  
  return room;
};

const ChatRoom = mongoose.model('ChatRoom', chatRoomSchema);

module.exports = ChatRoom;