# Stage Manager Troubleshooting Guide

## Issue: "No stage information available for this application"

### Problem Description
The Stage Manager tab displays "No stage information available for this application" instead of showing the stage hierarchy interface. This typically occurs when:

1. **Stage hierarchy is not initialized** for the application
2. **Database inconsistency** between application record and stage data
3. **Frontend-backend communication issue**
4. **Permission/access control problem**

---

## Immediate Diagnostic Steps

### Step 1: Check Application Data
```bash
# Connect to MongoDB and check the application record
db.applications.findOne({id: "APP-2025-001"})
```

**Look for:**
- `stageHierarchy` field exists and is not empty
- `currentMainStage`, `currentSubStage`, `currentStageStatus` fields are populated
- Application status is valid

### Step 2: Verify API Response
```bash
# Test the stage status endpoint
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Expected Response Should Include:**
```json
{
  "id": "APP-2025-001",
  "currentMainStage": "ONBOARDING",
  "currentSubStage": "BENEFICIARY_REGISTRATION", 
  "currentStageStatus": "NOT_STARTED",
  "stageHierarchy": [...]
}
```

### Step 3: Check Browser Console
1. Open browser Developer Tools (F12)
2. Navigate to Console tab
3. Look for JavaScript errors related to stage loading
4. Check Network tab for failed API calls

---

## Common Fixes

### Fix 1: Initialize Stage Hierarchy
If `stageHierarchy` is missing or empty:

```javascript
// Run this script to initialize stage hierarchy for APP-2025-001
const Application = require('./backend/src/models/application');
const StageStatusService = require('./backend/src/services/stage-status.service');

async function initializeStageHierarchy(applicationId) {
  const application = await Application.findOne({ id: applicationId });
  
  if (!application) {
    console.error('Application not found:', applicationId);
    return;
  }
  
  if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
    console.log('Initializing stage hierarchy for:', applicationId);
    
    // Initialize with default hierarchy
    application.stageHierarchy = StageStatusService._initializeStageHierarchy();
    
    // Set default current stage if not set
    if (!application.currentMainStage) {
      application.currentMainStage = 'ONBOARDING';
      application.currentSubStage = 'BENEFICIARY_REGISTRATION';
      application.currentStageStatus = 'NOT_STARTED';
    }
    
    await application.save();
    console.log('Stage hierarchy initialized successfully');
  } else {
    console.log('Stage hierarchy already exists');
  }
}

// Run the fix
initializeStageHierarchy('APP-2025-001');
```

### Fix 2: Database Update Script
```javascript
// backend/scripts/fix-stage-hierarchy.js
const mongoose = require('mongoose');
const Application = require('../src/models/application');
const { ApplicationMainStage, ApplicationSubStage, StageStatus, MainStageToSubStagesMap } = require('../src/models/stage-status-enums');

async function fixStageHierarchy() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    
    // Find applications without stage hierarchy
    const applications = await Application.find({
      $or: [
        { stageHierarchy: { $exists: false } },
        { stageHierarchy: { $size: 0 } },
        { stageHierarchy: null }
      ]
    });
    
    console.log(`Found ${applications.length} applications needing stage hierarchy initialization`);
    
    for (const app of applications) {
      console.log(`Fixing application: ${app.id}`);
      
      // Initialize stage hierarchy
      app.stageHierarchy = initializeStageHierarchy();
      
      // Set default current stage if missing
      if (!app.currentMainStage) {
        app.currentMainStage = ApplicationMainStage.ONBOARDING;
        app.currentSubStage = ApplicationSubStage.BENEFICIARY_REGISTRATION;
        app.currentStageStatus = StageStatus.NOT_STARTED;
      }
      
      await app.save();
      console.log(`✓ Fixed application: ${app.id}`);
    }
    
    console.log('Stage hierarchy fix completed');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing stage hierarchy:', error);
    process.exit(1);
  }
}

function initializeStageHierarchy() {
  const stageHierarchy = [];
  
  Object.values(ApplicationMainStage).forEach(mainStage => {
    const mainStageObj = {
      mainStage,
      status: StageStatus.NOT_STARTED,
      subStages: []
    };
    
    MainStageToSubStagesMap[mainStage].forEach(subStage => {
      mainStageObj.subStages.push({
        subStage,
        status: {
          status: StageStatus.NOT_STARTED,
          startedAt: null,
          completedAt: null,
          assignedTo: null,
          notes: null
        },
        history: []
      });
    });
    
    stageHierarchy.push(mainStageObj);
  });
  
  return stageHierarchy;
}

// Run the script
fixStageHierarchy();
```

### Fix 3: API Endpoint Check
Verify the stage-related endpoints are working:

```bash
# Test stage status endpoint
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Test stage hierarchy update endpoint
curl -X PUT "http://localhost:3000/api/v1/applications/APP-2025-001/stage-hierarchy" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "mainStage": "ONBOARDING",
    "subStage": "BENEFICIARY_REGISTRATION", 
    "status": "ACTIVE",
    "notes": "Test stage update"
  }'
```

### Fix 4: Frontend Component Check
Check if the frontend component is properly loading stage data:

```javascript
// Check in browser console
// This should show the stage data being loaded
console.log('Stage data:', window.stageData);

// Check if the API call is being made
fetch('/api/v1/applications/APP-2025-001')
  .then(response => response.json())
  .then(data => {
    console.log('Application data:', data);
    console.log('Stage hierarchy:', data.stageHierarchy);
  });
```

---

## Prevention Steps

### 1. Ensure Proper Application Creation
When creating new applications, always initialize stage hierarchy:

```javascript
// In application creation endpoint
router.post('/', async (req, res) => {
  try {
    const applicationData = {
      ...req.body,
      currentMainStage: ApplicationMainStage.ONBOARDING,
      currentSubStage: ApplicationSubStage.BENEFICIARY_REGISTRATION,
      currentStageStatus: StageStatus.NOT_STARTED,
      stageHierarchy: StageStatusService._initializeStageHierarchy()
    };
    
    const application = await Application.create(applicationData);
    res.status(201).json(application);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

### 2. Add Validation Middleware
```javascript
// Middleware to ensure stage hierarchy exists
const ensureStageHierarchy = async (req, res, next) => {
  try {
    const application = await Application.findOne({ id: req.params.id });
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
      console.log(`Initializing missing stage hierarchy for ${application.id}`);
      application.stageHierarchy = StageStatusService._initializeStageHierarchy();
      await application.save();
    }
    
    next();
  } catch (error) {
    res.status(500).json({ message: 'Stage hierarchy validation failed' });
  }
};

// Use in routes
router.get('/:id', ensureStageHierarchy, async (req, res) => {
  // Application retrieval logic
});
```

### 3. Database Migration
Create a migration to fix existing data:

```javascript
// migrations/002-ensure-stage-hierarchy.js
const Application = require('../src/models/application');

async function up() {
  console.log('Ensuring all applications have stage hierarchy...');
  
  const result = await Application.updateMany(
    {
      $or: [
        { stageHierarchy: { $exists: false } },
        { stageHierarchy: { $size: 0 } }
      ]
    },
    {
      $set: {
        stageHierarchy: initializeStageHierarchy(),
        currentMainStage: 'ONBOARDING',
        currentSubStage: 'BENEFICIARY_REGISTRATION',
        currentStageStatus: 'NOT_STARTED'
      }
    }
  );
  
  console.log(`Updated ${result.modifiedCount} applications`);
}

module.exports = { up };
```

---

## Monitoring and Alerts

### 1. Health Check Enhancement
Add stage hierarchy validation to health checks:

```javascript
router.get('/health/stage-manager', async (req, res) => {
  try {
    // Check for applications without stage hierarchy
    const missingHierarchy = await Application.countDocuments({
      $or: [
        { stageHierarchy: { $exists: false } },
        { stageHierarchy: { $size: 0 } }
      ]
    });
    
    const healthStatus = {
      status: missingHierarchy === 0 ? 'healthy' : 'warning',
      timestamp: new Date(),
      checks: {
        database: 'connected',
        stageHierarchyIntegrity: missingHierarchy === 0 ? 'ok' : `${missingHierarchy} applications missing hierarchy`
      }
    };
    
    res.json(healthStatus);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

### 2. Automated Monitoring Script
```javascript
// scripts/monitor-stage-integrity.js
const cron = require('node-cron');

// Run every hour
cron.schedule('0 * * * *', async () => {
  try {
    const missingHierarchy = await Application.countDocuments({
      $or: [
        { stageHierarchy: { $exists: false } },
        { stageHierarchy: { $size: 0 } }
      ]
    });
    
    if (missingHierarchy > 0) {
      console.warn(`⚠️  ${missingHierarchy} applications missing stage hierarchy`);
      // Send alert to monitoring system
      await sendAlert({
        type: 'stage_hierarchy_missing',
        count: missingHierarchy,
        timestamp: new Date()
      });
    }
  } catch (error) {
    console.error('Stage integrity check failed:', error);
  }
});
```

---

## Quick Fix Commands

### For APP-2025-001 Specifically:
```bash
# 1. Connect to MongoDB
mongo your-database-name

# 2. Check current state
db.applications.findOne({id: "APP-2025-001"}, {stageHierarchy: 1, currentMainStage: 1, currentSubStage: 1})

# 3. Initialize stage hierarchy if missing
db.applications.updateOne(
  {id: "APP-2025-001"},
  {
    $set: {
      currentMainStage: "ONBOARDING",
      currentSubStage: "BENEFICIARY_REGISTRATION", 
      currentStageStatus: "NOT_STARTED",
      stageHierarchy: [
        {
          mainStage: "ONBOARDING",
          status: "NOT_STARTED",
          subStages: [
            {
              subStage: "BENEFICIARY_REGISTRATION",
              status: {
                status: "NOT_STARTED",
                startedAt: null,
                completedAt: null,
                assignedTo: null,
                notes: null
              },
              history: []
            },
            {
              subStage: "PRE_SCREENING", 
              status: {
                status: "NOT_STARTED",
                startedAt: null,
                completedAt: null,
                assignedTo: null,
                notes: null
              },
              history: []
            }
          ]
        }
        // Add other main stages...
      ]
    }
  }
)

# 4. Verify the fix
db.applications.findOne({id: "APP-2025-001"}, {stageHierarchy: 1})
```

### Restart Application Services:
```bash
# Restart the backend service
pm2 restart screening-portal-backend

# Or if using Docker
docker-compose restart backend

# Clear any cached data
redis-cli FLUSHDB
```

---

## Contact Information

If the issue persists after trying these fixes:

1. **Check application logs** for detailed error messages
2. **Contact system administrator** with the specific application ID
3. **Provide screenshots** of the error and browser console logs
4. **Include database query results** from the diagnostic steps

**Emergency Contact**: [System Administrator Email/Phone]
**Support Ticket System**: [URL/Process]
