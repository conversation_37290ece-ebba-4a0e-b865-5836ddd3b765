/**
 * Restart Backend Server Script
 *
 * This script restarts the backend server by:
 * 1. Finding and killing any processes using port 3002
 * 2. Ensuring all models are properly registered
 * 3. Starting a new backend server instance with the fixed configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const PORT = 3002;
const LOG_FILE = path.join(__dirname, 'logs', 'server-restart.log');

// Create logs directory if it doesn't exist
const logsDir = path.dirname(LOG_FILE);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Logger
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Error logger
function logError(message, error) {
  const timestamp = new Date().toISOString();
  const errorMessage = `[${timestamp}] ERROR: ${message} - ${error.message || error}`;
  console.error(errorMessage);
  fs.appendFileSync(LOG_FILE, errorMessage + '\n');
}

// Find and kill processes using port 3002
function killProcessesUsingPort() {
  log(`Finding processes using port ${PORT}...`);
  
  try {
    let pids = [];
    
    if (process.platform === 'win32') {
      // Windows
      try {
        const output = execSync(`netstat -ano | findstr :${PORT}`).toString();
        const lines = output.split('\n');
        
        for (const line of lines) {
          const match = line.match(/LISTENING\s+(\d+)/);
          if (match && match[1]) {
            pids.push(match[1]);
          }
        }
      } catch (error) {
        // If the command fails, it might mean no processes are using the port
        log('No processes found using the port.');
        return true;
      }
    } else {
      // Linux/Mac
      try {
        const output = execSync(`lsof -i :${PORT} | grep LISTEN`).toString();
        const lines = output.split('\n');
        
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length > 1) {
            pids.push(parts[1]);
          }
        }
      } catch (error) {
        // If the command fails, it might mean no processes are using the port
        log('No processes found using the port.');
        return true;
      }
    }
    
    if (pids.length === 0) {
      log('No processes found using the port.');
      return true;
    }
    
    log(`Found ${pids.length} processes using port ${PORT}: ${pids.join(', ')}`);
    
    // Kill the processes
    for (const pid of pids) {
      try {
        if (process.platform === 'win32') {
          execSync(`taskkill /F /PID ${pid}`);
        } else {
          execSync(`kill -9 ${pid}`);
        }
        log(`Killed process with PID ${pid}`);
      } catch (error) {
        logError(`Failed to kill process with PID ${pid}`, error);
        return false;
      }
    }
    
    // Verify that the port is now free
    try {
      if (process.platform === 'win32') {
        execSync(`netstat -ano | findstr :${PORT} | findstr LISTENING`);
        logError('Port is still in use after killing processes', new Error('Port still in use'));
        return false;
      } else {
        execSync(`lsof -i :${PORT} | grep LISTEN`);
        logError('Port is still in use after killing processes', new Error('Port still in use'));
        return false;
      }
    } catch (error) {
      // If the command fails, it means no processes are using the port
      log('Port is now free.');
      return true;
    }
  } catch (error) {
    logError('Error killing processes', error);
    return false;
  }
}

// Start the backend server
function startBackendServer() {
  log('Starting backend server...');
  
  try {
    // Check if the models/index.js file exists
    const modelsIndexPath = path.join(__dirname, 'src', 'models', 'index.js');
    if (!fs.existsSync(modelsIndexPath)) {
      log('WARNING: Models index file not found at ' + modelsIndexPath);
      log('Applications may not display correctly without this fix.');
      log('Run: node fix-applications-display-issue.js to fix the issue.');
    } else {
      log('Models index file found. Schema registration should work correctly.');
    }
    
    // Use npm start in a new process
    const child = require('child_process').spawn('npm', ['start'], {
      detached: true,
      stdio: 'ignore',
      cwd: __dirname
    });
    
    // Unref the child process so it can run independently
    child.unref();
    
    log('Backend server started successfully!');
    return true;
  } catch (error) {
    logError('Error starting backend server', error);
    return false;
  }
}

// Main function
function main() {
  log('=== STARTING BACKEND SERVER RESTART ===');
  
  // Step 1: Kill processes using port 3002
  const isPortFree = killProcessesUsingPort();
  
  if (!isPortFree) {
    log('Failed to free port 3002. Please check if any processes are still using it.');
    return;
  }
  
  // Step 2: Start backend server
  const isStarted = startBackendServer();
  
  if (!isStarted) {
    log('Failed to start backend server. Please start it manually.');
    return;
  }
  
  log('=== BACKEND SERVER RESTART COMPLETED ===');
  log('The backend server has been restarted with the model registration fix.');
  log('Applications should now display correctly in the applications table.');
  log('If you still experience issues, run: node fix-applications-display-issue.js');
}

// Run the main function
main();