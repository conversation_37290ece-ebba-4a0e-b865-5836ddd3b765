const mongoose = require('mongoose');

const workflowAssignmentSchema = new mongoose.Schema({
  applicationId: {
    type: String,
    required: true,
    unique: true
  },
  workflowId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Workflow',
    required: true
  },
  currentStage: {
    mainStage: String,
    subStage: String
  },
  stageHistory: [{
    mainStage: String,
    subStage: String,
    status: String,
    startedAt: Date,
    completedAt: Date,
    completedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String,
    skippedReason: String
  }],
  overrides: [{
    fromStage: {
      mainStage: String,
      subStage: String
    },
    toStage: {
      mainStage: String,
      subStage: String
    },
    reason: String,
    overriddenBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    overriddenAt: Date
  }],
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes
workflowAssignmentSchema.index({ applicationId: 1 });
workflowAssignmentSchema.index({ workflowId: 1 });
workflowAssignmentSchema.index({ 'currentStage.mainStage': 1, 'currentStage.subStage': 1 });

module.exports = mongoose.model('WorkflowAssignment', workflowAssignmentSchema);