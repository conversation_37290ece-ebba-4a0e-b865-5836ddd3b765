const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Reuse schemas from corporate sponsor model
const addressSchema = new Schema({
  street: { type: String },
  city: { type: String },
  province: { type: String },
  postalCode: { type: String },
  country: { type: String, default: 'South Africa' }
});

const contactPersonSchema = new Schema({
  name: { type: String },
  email: { type: String },
  phone: { type: String },
  position: { type: String }
});

const companyDetailsSchema = new Schema({
  registrationNumber: { type: String },
  vatNumber: { type: String },
  bbbeeLevel: { type: String },
  industry: { type: String }
});

const serviceProviderSchema = new Schema({
  type: { 
    type: String, 
    enum: ['individual', 'company'],
    required: true
  },
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  companyDetails: companyDetailsSchema,
  contactPerson: contactPersonSchema,
  address: addressSchema,
  specializations: [{ 
    type: String,
    trim: true
  }],
  status: { 
    type: String, 
    enum: ['active', 'inactive'],
    default: 'active'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { timestamps: true });

// Add text index for search functionality
serviceProviderSchema.index({ 
  name: 'text', 
  specializations: 'text'
});

// Virtual for URL
serviceProviderSchema.virtual('url').get(function() {
  return `/service-providers/${this._id}`;
});

module.exports = mongoose.model('ServiceProvider', serviceProviderSchema);
