const LoanProduct = require('../models/loan-product');
const PricingRule = require('../models/pricing-rule');
const LoanOffer = require('../models/loan-offer');
const Loan = require('../models/loan');
const Application = require('../models/application');
const NotificationService = require('./notification-service');

class LoanManagementService {
  // Loan Product Management
  async createLoanProduct(productData, userId) {
    try {
      const product = new LoanProduct({
        ...productData,
        createdBy: userId,
        updatedBy: userId
      });
      
      await product.save();
      return { success: true, product };
    } catch (error) {
      console.error('Error creating loan product:', error);
      return { success: false, error: error.message };
    }
  }

  async updateLoanProduct(productId, updateData, userId) {
    try {
      const product = await LoanProduct.findOne({ productId });
      if (!product) {
        return { success: false, error: 'Loan product not found' };
      }
      
      Object.assign(product, updateData, { updatedBy: userId });
      await product.save();
      
      return { success: true, product };
    } catch (error) {
      console.error('Error updating loan product:', error);
      return { success: false, error: error.message };
    }
  }

  async getLoanProducts(filters = {}) {
    try {
      const query = { ...filters };
      const products = await LoanProduct.find(query)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');
      
      return { success: true, products };
    } catch (error) {
      console.error('Error fetching loan products:', error);
      return { success: false, error: error.message };
    }
  }

  // Pricing Rule Management
  async createPricingRule(ruleData, userId) {
    try {
      const rule = new PricingRule({
        ...ruleData,
        createdBy: userId,
        updatedBy: userId
      });
      
      await rule.save();
      return { success: true, rule };
    } catch (error) {
      console.error('Error creating pricing rule:', error);
      return { success: false, error: error.message };
    }
  }

  async calculateLoanPricing(applicationId, productId, requestedAmount, requestedTerm) {
    try {
      // Get application details
      const application = await Application.findOne({ id: applicationId });
      if (!application) {
        return { success: false, error: 'Application not found' };
      }

      // Get product details
      const product = await LoanProduct.findOne({ productId, active: true });
      if (!product) {
        return { success: false, error: 'Loan product not found' };
      }

      // Validate amount and term
      if (requestedAmount < product.minAmount || requestedAmount > product.maxAmount) {
        return { success: false, error: 'Requested amount is outside product limits' };
      }
      if (requestedTerm < product.minTerm || requestedTerm > product.maxTerm) {
        return { success: false, error: 'Requested term is outside product limits' };
      }

      // Get active pricing rules for the product
      const pricingRules = await PricingRule.find({ 
        productId, 
        active: true,
        effectiveDate: { $lte: new Date() },
        $or: [
          { expiryDate: null },
          { expiryDate: { $gte: new Date() } }
        ]
      });

      if (pricingRules.length === 0) {
        return { success: false, error: 'No active pricing rules found for this product' };
      }

      // Use the first matching rule (in production, you might have more complex logic)
      const rule = pricingRules[0];
      let interestRate = rule.baseRate;

      // Apply risk factor adjustments
      const riskFactors = await this.calculateRiskFactors(application);
      
      rule.riskFactors.forEach(factor => {
        const riskValue = riskFactors[factor.factor];
        if (riskValue !== undefined) {
          // Find the matching range
          const range = factor.ranges.find(r => 
            riskValue >= r.min && riskValue <= r.max
          );
          if (range) {
            interestRate += range.adjustment * factor.weight;
          }
        }
      });

      // Apply term adjustments
      const termAdjustment = rule.termAdjustments.find(adj =>
        requestedTerm >= adj.minTerm && requestedTerm <= adj.maxTerm
      );
      if (termAdjustment) {
        interestRate += termAdjustment.adjustment;
      }

      // Apply amount adjustments
      const amountAdjustment = rule.amountAdjustments.find(adj =>
        requestedAmount >= adj.minAmount && requestedAmount <= adj.maxAmount
      );
      if (amountAdjustment) {
        interestRate += amountAdjustment.adjustment;
      }

      // Ensure interest rate is within margin range
      interestRate = Math.max(rule.baseRate + rule.marginRange.min, interestRate);
      interestRate = Math.min(rule.baseRate + rule.marginRange.max, interestRate);

      // Calculate fees
      const fees = {
        originationFee: requestedAmount * (rule.fees.originationFeePercent / 100),
        processingFee: rule.fees.processingFeeFixed,
        totalFees: 0
      };
      fees.totalFees = fees.originationFee + fees.processingFee;

      return {
        success: true,
        pricing: {
          productId,
          pricingRuleId: rule.ruleId,
          requestedAmount,
          requestedTerm,
          interestRate: Math.round(interestRate * 100) / 100,
          fees,
          riskFactors,
          monthlyPayment: this.calculateMonthlyPayment(requestedAmount, interestRate, requestedTerm)
        }
      };
    } catch (error) {
      console.error('Error calculating loan pricing:', error);
      return { success: false, error: error.message };
    }
  }

  // Loan Offer Management
  async generateLoanOffer(offerData, userId) {
    try {
      // Calculate pricing if not provided
      if (!offerData.interestRate) {
        const pricingResult = await this.calculateLoanPricing(
          offerData.applicationId,
          offerData.productId,
          offerData.offeredAmount,
          offerData.term
        );

        if (!pricingResult.success) {
          return pricingResult;
        }

        offerData.interestRate = pricingResult.pricing.interestRate;
        offerData.fees = pricingResult.pricing.fees;
        offerData.pricingRuleId = pricingResult.pricing.pricingRuleId;
      }

      // Create the offer
      const offer = new LoanOffer({
        ...offerData,
        validUntil: offerData.validUntil || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        createdBy: userId,
        updatedBy: userId
      });

      await offer.save();

      // Send notification
      await this.notifyOfferGenerated(offer);

      return { success: true, offer };
    } catch (error) {
      console.error('Error generating loan offer:', error);
      return { success: false, error: error.message };
    }
  }

  async updateLoanOfferStatus(offerId, status, userId, reason = null) {
    try {
      const offer = await LoanOffer.findOne({ offerId });
      if (!offer) {
        return { success: false, error: 'Loan offer not found' };
      }

      offer.status = status;
      offer.updatedBy = userId;

      switch (status) {
        case 'SENT':
          offer.sentDate = new Date();
          break;
        case 'ACCEPTED':
          offer.acceptedDate = new Date();
          // Create loan record
          await this.createLoanFromOffer(offer);
          break;
        case 'REJECTED':
          offer.rejectedDate = new Date();
          offer.rejectionReason = reason;
          break;
      }

      await offer.save();

      // Send notification
      await this.notifyOfferStatusChange(offer);

      return { success: true, offer };
    } catch (error) {
      console.error('Error updating loan offer status:', error);
      return { success: false, error: error.message };
    }
  }

  // Loan Management
  async createLoanFromOffer(offer) {
    try {
      const loan = new Loan({
        offerId: offer.offerId,
        applicationId: offer.applicationId,
        productId: offer.productId,
        principalAmount: offer.offeredAmount,
        disbursedAmount: offer.offeredAmount - (offer.fees?.totalFees || 0),
        interestRate: offer.interestRate,
        term: offer.term,
        termUnit: offer.termUnit,
        payments: offer.repaymentSchedule.map((payment, index) => ({
          paymentId: `PAY-${offer.offerId}-${index + 1}`,
          ...payment,
          status: 'SCHEDULED'
        })),
        createdBy: offer.createdBy
      });

      await loan.save();

      // Update application status
      await Application.findOneAndUpdate(
        { id: offer.applicationId },
        { 
          status: 'approved',
          loanNumber: loan.loanNumber,
          lastUpdated: new Date()
        }
      );

      return loan;
    } catch (error) {
      console.error('Error creating loan from offer:', error);
      throw error;
    }
  }

  async disburseLoan(loanNumber, disbursementData, userId) {
    try {
      const loan = await Loan.findOne({ loanNumber });
      if (!loan) {
        return { success: false, error: 'Loan not found' };
      }

      if (loan.status !== 'PENDING_DISBURSEMENT') {
        return { success: false, error: 'Loan is not pending disbursement' };
      }

      loan.disbursementDate = disbursementData.disbursementDate || new Date();
      loan.status = 'ACTIVE';
      loan.updatedBy = userId;

      // Update payment schedule dates based on disbursement date
      const firstPaymentDate = new Date(loan.disbursementDate);
      firstPaymentDate.setMonth(firstPaymentDate.getMonth() + 1);
      loan.firstPaymentDate = firstPaymentDate;

      loan.payments.forEach((payment, index) => {
        const paymentDate = new Date(firstPaymentDate);
        paymentDate.setMonth(paymentDate.getMonth() + index);
        payment.dueDate = paymentDate;
        if (index === 0) {
          payment.status = 'PENDING';
        }
      });

      loan.updateOutstandingAmounts();
      await loan.save();

      // Send disbursement notification
      await this.notifyLoanDisbursed(loan);

      return { success: true, loan };
    } catch (error) {
      console.error('Error disbursing loan:', error);
      return { success: false, error: error.message };
    }
  }

  async recordPayment(loanNumber, paymentData, userId) {
    try {
      const loan = await Loan.findOne({ loanNumber });
      if (!loan) {
        return { success: false, error: 'Loan not found' };
      }

      const payment = loan.payments.find(p => p.paymentId === paymentData.paymentId);
      if (!payment) {
        return { success: false, error: 'Payment not found' };
      }

      payment.paymentDate = paymentData.paymentDate || new Date();
      payment.paidAmount = paymentData.amount;
      payment.paymentMethod = paymentData.paymentMethod;
      payment.transactionReference = paymentData.transactionReference;
      payment.notes = paymentData.notes;

      if (payment.paidAmount >= payment.totalAmount) {
        payment.status = 'PAID';
      } else {
        payment.status = 'PARTIAL';
      }

      // Update next payment to pending
      const nextPaymentIndex = loan.payments.findIndex(p => p.paymentId === payment.paymentId) + 1;
      if (nextPaymentIndex < loan.payments.length && loan.payments[nextPaymentIndex].status === 'SCHEDULED') {
        loan.payments[nextPaymentIndex].status = 'PENDING';
      }

      loan.updateOutstandingAmounts();
      loan.updateStatus();
      loan.updatedBy = userId;

      await loan.save();

      // Send payment confirmation
      await this.notifyPaymentReceived(loan, payment);

      return { success: true, loan, payment };
    } catch (error) {
      console.error('Error recording payment:', error);
      return { success: false, error: error.message };
    }
  }

  // Helper Methods
  calculateMonthlyPayment(principal, annualRate, termMonths) {
    const monthlyRate = annualRate / 100 / 12;
    if (monthlyRate === 0) {
      return principal / termMonths;
    }
    
    const payment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, termMonths)) / 
      (Math.pow(1 + monthlyRate, termMonths) - 1);
    
    return Math.round(payment * 100) / 100;
  }

  async calculateRiskFactors(application) {
    // This is a simplified risk calculation
    // In production, this would integrate with credit bureaus and other data sources
    const factors = {
      CREDIT_SCORE: 700, // Mock credit score
      DEBT_TO_INCOME: 0.3,
      TIME_IN_BUSINESS: application.businessInfo?.yearEstablished ? 
        new Date().getFullYear() - application.businessInfo.yearEstablished : 2,
      INDUSTRY_RISK: 0.5, // Medium risk
      COLLATERAL_COVERAGE: 1.2,
      REVENUE_STABILITY: 0.8,
      CASH_FLOW_RATIO: 1.5
    };

    return factors;
  }

  // Notification Methods
  async notifyOfferGenerated(offer) {
    try {
      const application = await Application.findOne({ id: offer.applicationId });
      if (application && application.personalInfo?.email) {
        await NotificationService.createNotification({
          userId: application.owner,
          type: 'LOAN_OFFER_GENERATED',
          title: 'Loan Offer Available',
          message: `A loan offer of R${offer.offeredAmount} has been generated for your application.`,
          priority: 'high',
          relatedEntity: {
            type: 'loan_offer',
            id: offer.offerId
          }
        });
      }
    } catch (error) {
      console.error('Error sending offer notification:', error);
    }
  }

  async notifyOfferStatusChange(offer) {
    try {
      const application = await Application.findOne({ id: offer.applicationId });
      if (application && application.owner) {
        let title, message;
        
        switch (offer.status) {
          case 'ACCEPTED':
            title = 'Loan Offer Accepted';
            message = 'Your loan offer has been accepted and is being processed.';
            break;
          case 'REJECTED':
            title = 'Loan Offer Declined';
            message = 'The loan offer has been declined.';
            break;
          case 'EXPIRED':
            title = 'Loan Offer Expired';
            message = 'Your loan offer has expired.';
            break;
          default:
            return;
        }

        await NotificationService.createNotification({
          userId: application.owner,
          type: 'LOAN_OFFER_STATUS_CHANGE',
          title,
          message,
          priority: 'high',
          relatedEntity: {
            type: 'loan_offer',
            id: offer.offerId
          }
        });
      }
    } catch (error) {
      console.error('Error sending offer status notification:', error);
    }
  }

  async notifyLoanDisbursed(loan) {
    try {
      const application = await Application.findOne({ id: loan.applicationId });
      if (application && application.owner) {
        await NotificationService.createNotification({
          userId: application.owner,
          type: 'LOAN_DISBURSED',
          title: 'Loan Disbursed',
          message: `Your loan of R${loan.disbursedAmount} has been disbursed.`,
          priority: 'high',
          relatedEntity: {
            type: 'loan',
            id: loan.loanNumber
          }
        });
      }
    } catch (error) {
      console.error('Error sending disbursement notification:', error);
    }
  }

  async notifyPaymentReceived(loan, payment) {
    try {
      const application = await Application.findOne({ id: loan.applicationId });
      if (application && application.owner) {
        await NotificationService.createNotification({
          userId: application.owner,
          type: 'PAYMENT_RECEIVED',
          title: 'Payment Received',
          message: `Payment of R${payment.paidAmount} has been received for your loan.`,
          priority: 'medium',
          relatedEntity: {
            type: 'loan',
            id: loan.loanNumber
          }
        });
      }
    } catch (error) {
      console.error('Error sending payment notification:', error);
    }
  }
}

module.exports = new LoanManagementService();