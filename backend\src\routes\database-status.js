/**
 * Database Status API Routes
 * 
 * This module provides API endpoints for checking database status
 * and retrieving database information.
 */

const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const dbConfig = require('../config/database-config');

/**
 * @route GET /api/database-status
 * @desc Get database status and connection information
 * @access Public
 */
router.get('/', (req, res) => {
  const config = dbConfig.readConfig();
  const connectionState = mongoose.connection.readyState;
  
  // Map connection state to a readable status
  let status;
  switch (connectionState) {
    case 0:
      status = 'disconnected';
      break;
    case 1:
      status = 'connected';
      break;
    case 2:
      status = 'connecting';
      break;
    case 3:
      status = 'disconnecting';
      break;
    default:
      status = 'unknown';
  }
  
  res.json({
    status,
    connectionState,
    uri: config.persistentUri,
    databaseName: mongoose.connection.name || 'Not connected',
    host: mongoose.connection.host || 'Not connected',
    port: mongoose.connection.port || 'Not connected',
    models: Object.keys(mongoose.models)
  });
});

/**
 * @route GET /api/database-status/health
 * @desc Check database health with detailed metrics
 * @access Public
 */
router.get('/health', async (req, res) => {
  try {
    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({
        status: 'error',
        message: 'Database is not connected',
        connectionState: mongoose.connection.readyState
      });
    }
    
    // Get database stats
    const dbStats = await mongoose.connection.db.stats();
    
    // Get collection stats for each collection
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionStats = {};
    
    for (const collection of collections) {
      const stats = await mongoose.connection.db.collection(collection.name).stats();
      collectionStats[collection.name] = {
        count: stats.count,
        size: stats.size,
        avgObjSize: stats.avgObjSize
      };
    }
    
    res.json({
      status: 'connected',
      message: 'Database is healthy',
      stats: {
        dbName: mongoose.connection.name,
        collections: collections.length,
        dataSize: dbStats.dataSize,
        storageSize: dbStats.storageSize,
        indexes: dbStats.indexes,
        indexSize: dbStats.indexSize
      },
      collectionStats
    });
  } catch (error) {
    console.error('Error checking database health:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error checking database health',
      error: error.message
    });
  }
});

/**
 * @route GET /api/database-status/collections
 * @desc Get list of collections and their document counts
 * @access Public
 */
router.get('/collections', async (req, res) => {
  try {
    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({
        status: 'error',
        message: 'Database is not connected',
        connectionState: mongoose.connection.readyState
      });
    }
    
    // Get collections and their counts
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionData = [];
    
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      collectionData.push({
        name: collection.name,
        count,
        type: collection.type
      });
    }
    
    res.json({
      status: 'success',
      collections: collectionData
    });
  } catch (error) {
    console.error('Error getting collections:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error getting collections',
      error: error.message
    });
  }
});

module.exports = router;