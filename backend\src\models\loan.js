const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    required: true
  },
  paymentNumber: Number,
  dueDate: Date,
  paymentDate: Date,
  principalAmount: Number,
  interestAmount: Number,
  feesAmount: Number,
  totalAmount: Number,
  paidAmount: Number,
  balance: Number,
  status: {
    type: String,
    enum: ['SCHEDULED', 'PENDING', 'PAID', 'PARTIAL', 'LATE', 'DEFAULTED'],
    default: 'SCHEDULED'
  },
  paymentMethod: String,
  transactionReference: String,
  notes: String
});

const loanSchema = new mongoose.Schema({
  loanNumber: {
    type: String,
    required: true,
    unique: true
  },
  offerId: {
    type: String,
    required: true,
    ref: 'LoanOffer'
  },
  applicationId: {
    type: String,
    required: true,
    ref: 'Application'
  },
  borrowerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  productId: {
    type: String,
    required: true,
    ref: 'LoanProduct'
  },
  principalAmount: {
    type: Number,
    required: true
  },
  disbursedAmount: {
    type: Number,
    required: true
  },
  interestRate: {
    type: Number,
    required: true
  },
  term: {
    type: Number,
    required: true
  },
  termUnit: {
    type: String,
    enum: ['DAYS', 'MONTHS', 'YEARS'],
    default: 'MONTHS'
  },
  status: {
    type: String,
    enum: ['PENDING_DISBURSEMENT', 'ACTIVE', 'CLOSED', 'DEFAULTED', 'WRITTEN_OFF', 'RESTRUCTURED'],
    default: 'PENDING_DISBURSEMENT'
  },
  disbursementDate: Date,
  firstPaymentDate: Date,
  maturityDate: Date,
  closureDate: Date,
  outstandingPrincipal: Number,
  outstandingInterest: Number,
  outstandingFees: Number,
  totalOutstanding: Number,
  totalPaidPrincipal: {
    type: Number,
    default: 0
  },
  totalPaidInterest: {
    type: Number,
    default: 0
  },
  totalPaidFees: {
    type: Number,
    default: 0
  },
  nextPaymentDate: Date,
  nextPaymentAmount: Number,
  daysOverdue: {
    type: Number,
    default: 0
  },
  payments: [paymentSchema],
  restructureHistory: [{
    date: Date,
    reason: String,
    oldTerms: {
      interestRate: Number,
      term: Number,
      monthlyPayment: Number
    },
    newTerms: {
      interestRate: Number,
      term: Number,
      monthlyPayment: Number
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  collateral: {
    hasCollateral: Boolean,
    type: String,
    description: String,
    value: Number,
    status: String
  },
  guarantors: [{
    name: String,
    idNumber: String,
    relationship: String,
    contactNumber: String,
    email: String,
    address: String
  }],
  documents: [{
    type: String,
    name: String,
    url: String,
    uploadDate: Date
  }],
  notes: [{
    date: Date,
    note: String,
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  performanceMetrics: {
    paymentHistory: {
      onTimePayments: Number,
      latePayments: Number,
      missedPayments: Number
    },
    averageDaysLate: Number,
    riskRating: {
      type: String,
      enum: ['LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']
    },
    lastRiskAssessmentDate: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Generate loan number if not provided
loanSchema.pre('save', async function(next) {
  if (!this.loanNumber) {
    const count = await this.constructor.countDocuments();
    const date = new Date();
    const year = date.getFullYear();
    this.loanNumber = `LN-${year}-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

// Update outstanding amounts
loanSchema.methods.updateOutstandingAmounts = function() {
  const paidPayments = this.payments.filter(p => p.status === 'PAID');
  
  this.totalPaidPrincipal = paidPayments.reduce((sum, p) => sum + (p.principalAmount || 0), 0);
  this.totalPaidInterest = paidPayments.reduce((sum, p) => sum + (p.interestAmount || 0), 0);
  this.totalPaidFees = paidPayments.reduce((sum, p) => sum + (p.feesAmount || 0), 0);
  
  this.outstandingPrincipal = this.principalAmount - this.totalPaidPrincipal;
  this.outstandingInterest = this.payments.reduce((sum, p) => {
    if (p.status !== 'PAID') {
      return sum + (p.interestAmount || 0);
    }
    return sum;
  }, 0);
  this.outstandingFees = this.payments.reduce((sum, p) => {
    if (p.status !== 'PAID') {
      return sum + (p.feesAmount || 0);
    }
    return sum;
  }, 0);
  
  this.totalOutstanding = this.outstandingPrincipal + this.outstandingInterest + this.outstandingFees;
  
  // Update next payment info
  const nextPayment = this.payments.find(p => p.status === 'SCHEDULED' || p.status === 'PENDING');
  if (nextPayment) {
    this.nextPaymentDate = nextPayment.dueDate;
    this.nextPaymentAmount = nextPayment.totalAmount;
  }
  
  // Calculate days overdue
  const overduePayments = this.payments.filter(p => 
    (p.status === 'PENDING' || p.status === 'LATE') && 
    p.dueDate < new Date()
  );
  
  if (overduePayments.length > 0) {
    const oldestOverdue = overduePayments.reduce((oldest, p) => 
      p.dueDate < oldest.dueDate ? p : oldest
    );
    this.daysOverdue = Math.floor((new Date() - oldestOverdue.dueDate) / (1000 * 60 * 60 * 24));
  } else {
    this.daysOverdue = 0;
  }
};

// Update loan status based on payments
loanSchema.methods.updateStatus = function() {
  if (this.outstandingPrincipal <= 0 && this.totalOutstanding <= 0) {
    this.status = 'CLOSED';
    this.closureDate = new Date();
  } else if (this.daysOverdue > 90) {
    this.status = 'DEFAULTED';
  } else if (this.disbursementDate) {
    this.status = 'ACTIVE';
  }
};

const Loan = mongoose.model('Loan', loanSchema);

module.exports = Loan;