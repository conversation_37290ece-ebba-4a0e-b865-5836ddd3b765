const mongoose = require('mongoose');

// Define the schema for interview notes
const interviewNoteSchema = new mongoose.Schema({
  id: { type: String, required: true },
  text: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  createdBy: { type: String, required: true }
});

// Define the schema for interview questions
const interviewQuestionSchema = new mongoose.Schema({
  id: { type: String, required: true },
  text: { type: String, required: true },
  type: { type: String, enum: ['OPEN_ENDED', 'MULTIPLE_CHOICE', 'YES_NO', 'RATING'], default: 'OPEN_ENDED' },
  required: { type: Boolean, default: false },
  answer: { type: String },
  createdAt: { type: Date, default: Date.now }
});

// Define the schema for interview participants
const interviewParticipantSchema = new mongoose.Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  role: { type: String, required: true },
  email: { type: String }
});

// Define the schema for interview recordings
const interviewRecordingSchema = new mongoose.Schema({
  id: { type: String, required: true },
  fileName: { type: String, required: true },
  fileSize: { type: Number, required: true },
  duration: { type: Number },
  fileUrl: { type: String, required: true },
  transcriptStatus: { type: String, enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED'], default: 'PENDING' },
  transcript: { type: String },
  createdAt: { type: Date, default: Date.now }
});

// Define the schema for interview sections
const interviewSectionSchema = new mongoose.Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  questions: [interviewQuestionSchema]
});

// Define the main interview schema
const interviewSchema = new mongoose.Schema({
  id: { type: String, required: true },
  applicationId: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  agenda: { type: String },
  templateId: { type: String, required: false }, // Reference to questionnaire template
  templateVersion: { type: String, required: false }, // Version of the template used
  status: { 
    type: String, 
    enum: ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], 
    default: 'SCHEDULED' 
  },
  type: { 
    type: String, 
    enum: ['ONLINE', 'IN_PERSON', 'PHONE'], 
    default: 'ONLINE' 
  },
  scheduledDate: { type: Date, required: true },
  duration: { type: Number, required: true }, // in minutes
  location: { type: String },
  meetingLink: { type: String },
  primaryInterviewer: { type: String, required: true },
  interviewee: { type: String, required: true },
  questions: [interviewQuestionSchema],
  notes: [interviewNoteSchema],
  participants: [interviewParticipantSchema],
  recordings: [interviewRecordingSchema],
  sections: [interviewSectionSchema],
  summary: { type: String },
  outcome: { type: String },
  followUpActions: { type: String },
  completedAt: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Create a model from the schema
const Interview = mongoose.model('Interview', interviewSchema);

module.exports = Interview;
