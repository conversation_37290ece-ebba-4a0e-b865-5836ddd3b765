const express = require('express');
const { check, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const NotificationService = require('../services/notification-service');
const NotificationDeliveryService = require('../services/notification-delivery-service');
const Notification = require('../models/notification');
const NotificationTemplate = require('../models/notification-template');
const UserNotification = require('../models/user-notification');

const {
  authenticateToken,
  canCreateNotifications,
  canManageTemplates,
  canManageSettings,
  canSendToTarget,
  validateNotificationData,
  canAccessNotification
} = require('../middleware/notification-auth');

const router = express.Router();

// Rate limiting for admin notification endpoints
const adminNotificationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 200 requests per windowMs
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many admin notification requests, please try again later.'
    }
  }
});

router.use(adminNotificationLimiter);

// POST /api/v1/admin/notifications - Create a new notification
router.post('/', 
  authenticateToken,
  canCreateNotifications,
  validateNotificationData,
  canSendToTarget,
  async (req, res) => {
    try {
      const notification = await NotificationService.createNotification(req.body, req.user.sub);
      
      res.status(201).json({
        message: 'Notification created successfully',
        notification: {
          id: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          status: notification.status,
          scheduledFor: notification.scheduledFor,
          targetAudience: notification.targetAudience,
          createdAt: notification.createdAt
        }
      });
    } catch (error) {
      console.error('Error creating notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to create notification'
        }
      });
    }
  }
);

// POST /api/v1/admin/notifications/from-template - Create notification from template
router.post('/from-template',
  authenticateToken,
  canCreateNotifications,
  canSendToTarget,
  [
    check('templateId', 'Template ID is required').notEmpty(),
    check('variables', 'Variables must be an object').optional().isObject()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation error',
            details: errors.array()
          }
        });
      }

      const { templateId, variables = {}, overrides = {} } = req.body;
      
      const notification = await NotificationService.createFromTemplate(
        templateId,
        variables,
        overrides,
        req.user.sub
      );
      
      res.status(201).json({
        message: 'Notification created from template successfully',
        notification: {
          id: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          status: notification.status,
          scheduledFor: notification.scheduledFor,
          targetAudience: notification.targetAudience,
          templateId: notification.templateId,
          createdAt: notification.createdAt
        }
      });
    } catch (error) {
      console.error('Error creating notification from template:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to create notification from template'
        }
      });
    }
  }
);

// GET /api/v1/admin/notifications - Get all notifications with filtering
router.get('/',
  authenticateToken,
  canCreateNotifications,
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 50,
        status,
        type,
        category,
        priority,
        createdBy,
        dateFrom,
        dateTo,
        search
      } = req.query;

      const query = {};
      
      // Apply filters
      if (status) query.status = status;
      if (type) query.type = type;
      if (category) query.category = category;
      if (priority) query.priority = priority;
      if (createdBy) query.createdBy = createdBy;
      
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }
      
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { message: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const [notifications, total] = await Promise.all([
        Notification.find(query)
          .populate('createdBy', 'username firstName lastName')
          .populate('templateId', 'name')
          .sort({ createdAt: -1 })
          .limit(parseInt(limit))
          .skip(skip),
        Notification.countDocuments(query)
      ]);

      res.json({
        notifications: notifications.map(notification => ({
          id: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          status: notification.status,
          scheduledFor: notification.scheduledFor,
          targetAudience: notification.targetAudience,
          deliveryMethods: notification.deliveryMethods,
          analytics: notification.analytics,
          createdBy: notification.createdBy,
          template: notification.templateId,
          createdAt: notification.createdAt,
          sentAt: notification.sentAt
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Error getting notifications:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notifications'
        }
      });
    }
  }
);

// GET /api/v1/admin/notifications/:id - Get specific notification
router.get('/:id',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const notification = await Notification.findById(req.params.id)
        .populate('createdBy', 'username firstName lastName')
        .populate('templateId', 'name description');

      if (!notification) {
        return res.status(404).json({
          error: {
            code: 'NOTIFICATION_NOT_FOUND',
            message: 'Notification not found'
          }
        });
      }

      res.json({
        notification: {
          id: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          status: notification.status,
          scheduledFor: notification.scheduledFor,
          targetAudience: notification.targetAudience,
          targetUsers: notification.targetUsers,
          targetRoles: notification.targetRoles,
          targetEntities: notification.targetEntities,
          deliveryMethods: notification.deliveryMethods,
          actions: notification.actions,
          analytics: notification.analytics,
          createdBy: notification.createdBy,
          template: notification.templateId,
          createdAt: notification.createdAt,
          updatedAt: notification.updatedAt,
          sentAt: notification.sentAt
        }
      });
    } catch (error) {
      console.error('Error getting notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notification'
        }
      });
    }
  }
);

// PUT /api/v1/admin/notifications/:id - Update notification
router.put('/:id',
  authenticateToken,
  canAccessNotification,
  validateNotificationData,
  async (req, res) => {
    try {
      const notification = await Notification.findById(req.params.id);
      
      if (!notification) {
        return res.status(404).json({
          error: {
            code: 'NOTIFICATION_NOT_FOUND',
            message: 'Notification not found'
          }
        });
      }

      if (notification.status === 'SENT') {
        return res.status(400).json({
          error: {
            code: 'NOTIFICATION_ALREADY_SENT',
            message: 'Cannot update notification that has already been sent'
          }
        });
      }

      // Update notification fields
      Object.assign(notification, req.body);
      notification.updatedBy = req.user.sub;
      
      await notification.save();

      res.json({
        message: 'Notification updated successfully',
        notification: {
          id: notification._id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          category: notification.category,
          priority: notification.priority,
          status: notification.status,
          scheduledFor: notification.scheduledFor,
          targetAudience: notification.targetAudience,
          updatedAt: notification.updatedAt
        }
      });
    } catch (error) {
      console.error('Error updating notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to update notification'
        }
      });
    }
  }
);

// DELETE /api/v1/admin/notifications/:id - Delete notification
router.delete('/:id',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const notification = await Notification.findById(req.params.id);
      
      if (!notification) {
        return res.status(404).json({
          error: {
            code: 'NOTIFICATION_NOT_FOUND',
            message: 'Notification not found'
          }
        });
      }

      if (notification.status === 'SENT') {
        return res.status(400).json({
          error: {
            code: 'NOTIFICATION_ALREADY_SENT',
            message: 'Cannot delete notification that has already been sent'
          }
        });
      }

      await Notification.findByIdAndDelete(req.params.id);
      
      // Delete associated user notifications
      await UserNotification.deleteMany({ notificationId: req.params.id });

      res.json({
        message: 'Notification deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to delete notification'
        }
      });
    }
  }
);

// POST /api/v1/admin/notifications/:id/send - Send notification immediately
router.post('/:id/send',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const result = await NotificationService.processNotification(req.params.id);
      
      res.json({
        message: 'Notification sent successfully',
        result
      });
    } catch (error) {
      console.error('Error sending notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to send notification'
        }
      });
    }
  }
);

// POST /api/v1/admin/notifications/:id/cancel - Cancel scheduled notification
router.post('/:id/cancel',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const notification = await NotificationService.cancelNotification(req.params.id, req.user.sub);
      
      res.json({
        message: 'Notification cancelled successfully',
        notification: {
          id: notification._id,
          status: notification.status,
          updatedAt: notification.updatedAt
        }
      });
    } catch (error) {
      console.error('Error cancelling notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to cancel notification'
        }
      });
    }
  }
);

// GET /api/v1/admin/notifications/:id/stats - Get notification statistics
router.get('/:id/stats',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const stats = await NotificationService.getNotificationStats(req.params.id);
      
      res.json({
        stats
      });
    } catch (error) {
      console.error('Error getting notification stats:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to get notification statistics'
        }
      });
    }
  }
);

// GET /api/v1/admin/notifications/:id/recipients - Get notification recipients
router.get('/:id/recipients',
  authenticateToken,
  canAccessNotification,
  async (req, res) => {
    try {
      const { page = 1, limit = 50, status } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const query = { notificationId: req.params.id };
      if (status) query.status = status;
      
      const [recipients, total] = await Promise.all([
        UserNotification.find(query)
          .populate('userId', 'username firstName lastName email')
          .sort({ createdAt: -1 })
          .limit(parseInt(limit))
          .skip(skip),
        UserNotification.countDocuments(query)
      ]);

      res.json({
        recipients: recipients.map(recipient => ({
          id: recipient._id,
          user: recipient.userId,
          status: recipient.status,
          deliveryStatus: recipient.deliveryStatus,
          isRead: recipient.isRead,
          readAt: recipient.readAt,
          isClicked: recipient.isClicked,
          clickedAt: recipient.clickedAt,
          createdAt: recipient.createdAt
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Error getting notification recipients:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notification recipients'
        }
      });
    }
  }
);

// POST /api/v1/admin/notifications/retry-failed - Retry failed deliveries
router.post('/retry-failed',
  authenticateToken,
  canCreateNotifications,
  async (req, res) => {
    try {
      const { notificationId } = req.body;
      
      const results = await NotificationDeliveryService.retryFailedDeliveries(notificationId);
      
      res.json({
        message: 'Retry process completed',
        results: {
          total: results.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          details: results
        }
      });
    } catch (error) {
      console.error('Error retrying failed deliveries:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to retry failed deliveries'
        }
      });
    }
  }
);

// GET /api/v1/admin/notifications/analytics/overview - Get notifications overview analytics
router.get('/analytics/overview',
  authenticateToken,
  canCreateNotifications,
  async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const dateFilter = {};
      if (dateFrom || dateTo) {
        dateFilter.createdAt = {};
        if (dateFrom) dateFilter.createdAt.$gte = new Date(dateFrom);
        if (dateTo) dateFilter.createdAt.$lte = new Date(dateTo);
      }

      const [
        totalNotifications,
        sentNotifications,
        scheduledNotifications,
        failedNotifications,
        typeBreakdown,
        priorityBreakdown
      ] = await Promise.all([
        Notification.countDocuments(dateFilter),
        Notification.countDocuments({ ...dateFilter, status: 'SENT' }),
        Notification.countDocuments({ ...dateFilter, status: 'SCHEDULED' }),
        Notification.countDocuments({ ...dateFilter, status: 'FAILED' }),
        Notification.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$type', count: { $sum: 1 } } }
        ]),
        Notification.aggregate([
          { $match: dateFilter },
          { $group: { _id: '$priority', count: { $sum: 1 } } }
        ])
      ]);

      res.json({
        overview: {
          total: totalNotifications,
          sent: sentNotifications,
          scheduled: scheduledNotifications,
          failed: failedNotifications,
          pending: totalNotifications - sentNotifications - failedNotifications
        },
        breakdown: {
          byType: typeBreakdown,
          byPriority: priorityBreakdown
        }
      });
    } catch (error) {
      console.error('Error getting notifications analytics:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notifications analytics'
        }
      });
    }
  }
);

module.exports = router;