const Notification = require('../models/notification');
const UserNotification = require('../models/user-notification');
const NotificationTemplate = require('../models/notification-template');
const User = require('../models/user');

/**
 * Notification Service
 * Core business logic for managing notifications
 */
class NotificationService {
  /**
   * Create a new notification
   * @param {Object} notificationData - Notification data
   * @param {String} createdBy - User ID who created the notification
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(notificationData, createdBy) {
    try {
      // Validate required fields
      if (!notificationData.title || !notificationData.message) {
        throw new Error('Title and message are required');
      }

      // Create notification
      const notification = new Notification({
        ...notificationData,
        createdBy,
        status: notificationData.scheduledFor && new Date(notificationData.scheduledFor) > new Date() 
          ? 'SCHEDULED' 
          : 'DRAFT'
      });

      await notification.save();

      // If notification is set to send immediately, process it
      if (notification.status === 'DRAFT' && notification.scheduledFor <= new Date()) {
        await this.processNotification(notification._id);
      }

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Create notification from template
   * @param {String} templateId - Template ID
   * @param {Object} variables - Template variables
   * @param {Object} overrides - Override settings
   * @param {String} createdBy - User ID who created the notification
   * @returns {Promise<Object>} Created notification
   */
  async createFromTemplate(templateId, variables = {}, overrides = {}, createdBy) {
    try {
      const template = await NotificationTemplate.findById(templateId);
      if (!template || !template.isActive) {
        throw new Error('Template not found or inactive');
      }

      // Validate template variables
      const validationErrors = template.validateVariables(variables);
      if (validationErrors.length > 0) {
        throw new Error(`Template validation failed: ${validationErrors.join(', ')}`);
      }

      // Render template content
      const renderedContent = template.render(variables);

      // Create notification data from template
      const notificationData = {
        title: renderedContent.inApp?.title || template.name,
        message: renderedContent.inApp?.message || 'No message content',
        type: template.type,
        category: template.category,
        priority: overrides.priority || template.defaultSettings.priority,
        deliveryMethods: overrides.deliveryMethods || template.defaultSettings.deliveryMethods,
        targetAudience: overrides.targetAudience || template.defaultSettings.targetAudience,
        targetUsers: overrides.targetUsers || [],
        targetRoles: overrides.targetRoles || [],
        targetEntities: overrides.targetEntities || {},
        actions: renderedContent.inApp?.actions || [],
        templateId: template._id,
        scheduledFor: overrides.scheduledFor || new Date(),
        // Add rendered content for different delivery methods
        renderedContent: {
          email: renderedContent.email,
          sms: renderedContent.sms,
          push: renderedContent.push
        }
      };

      // Merge any additional overrides
      Object.assign(notificationData, overrides);

      // Increment template usage
      await template.incrementUsage();

      return await this.createNotification(notificationData, createdBy);
    } catch (error) {
      console.error('Error creating notification from template:', error);
      throw error;
    }
  }

  /**
   * Process a notification for delivery
   * @param {String} notificationId - Notification ID
   * @returns {Promise<Object>} Processing result
   */
  async processNotification(notificationId) {
    try {
      const notification = await Notification.findById(notificationId);
      if (!notification) {
        throw new Error('Notification not found');
      }

      if (!notification.shouldSendNow()) {
        return { success: false, message: 'Notification not ready to send' };
      }

      // Update notification status
      notification.status = 'SENDING';
      await notification.save();

      // Get target users
      const targetUsers = await notification.getTargetUsers();
      
      if (targetUsers.length === 0) {
        notification.status = 'FAILED';
        await notification.save();
        throw new Error('No target users found');
      }

      // Create user notifications
      const userNotifications = [];
      for (const user of targetUsers) {
        try {
          const userNotification = new UserNotification({
            userId: user._id,
            notificationId: notification._id,
            scheduledFor: notification.scheduledFor,
            status: 'PENDING'
          });

          await userNotification.save();
          userNotifications.push(userNotification);
        } catch (error) {
          console.error(`Error creating user notification for user ${user._id}:`, error);
        }
      }

      // Update notification analytics
      notification.analytics.totalRecipients = userNotifications.length;
      notification.status = 'SENT';
      notification.sentAt = new Date();
      await notification.save();

      // Trigger delivery service
      const NotificationDeliveryService = require('./notification-delivery-service');
      await NotificationDeliveryService.deliverNotification(notification._id);

      return {
        success: true,
        message: 'Notification processed successfully',
        recipientCount: userNotifications.length
      };
    } catch (error) {
      console.error('Error processing notification:', error);
      
      // Update notification status to failed
      try {
        await Notification.findByIdAndUpdate(notificationId, { status: 'FAILED' });
      } catch (updateError) {
        console.error('Error updating notification status to failed:', updateError);
      }
      
      throw error;
    }
  }

  /**
   * Schedule a recurring notification
   * @param {Object} notificationData - Notification data with recurring pattern
   * @param {String} createdBy - User ID who created the notification
   * @returns {Promise<Object>} Created notification
   */
  async scheduleRecurringNotification(notificationData, createdBy) {
    try {
      if (!notificationData.isRecurring || !notificationData.recurringPattern) {
        throw new Error('Recurring pattern is required for recurring notifications');
      }

      // Validate recurring pattern
      const pattern = notificationData.recurringPattern;
      if (!pattern.frequency || !['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'].includes(pattern.frequency)) {
        throw new Error('Invalid recurring frequency');
      }

      // Calculate next scheduled time
      const nextScheduled = this.calculateNextRecurrence(new Date(), pattern);
      notificationData.scheduledFor = nextScheduled;

      return await this.createNotification(notificationData, createdBy);
    } catch (error) {
      console.error('Error scheduling recurring notification:', error);
      throw error;
    }
  }

  /**
   * Calculate next recurrence date
   * @param {Date} baseDate - Base date to calculate from
   * @param {Object} pattern - Recurring pattern
   * @returns {Date} Next recurrence date
   */
  calculateNextRecurrence(baseDate, pattern) {
    const nextDate = new Date(baseDate);
    const interval = pattern.interval || 1;

    switch (pattern.frequency) {
      case 'DAILY':
        nextDate.setDate(nextDate.getDate() + interval);
        break;
      
      case 'WEEKLY':
        if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
          // Find next occurrence on specified days of week
          const currentDay = nextDate.getDay();
          const sortedDays = pattern.daysOfWeek.sort((a, b) => a - b);
          
          let nextDay = sortedDays.find(day => day > currentDay);
          if (!nextDay) {
            nextDay = sortedDays[0];
            nextDate.setDate(nextDate.getDate() + (7 - currentDay + nextDay));
          } else {
            nextDate.setDate(nextDate.getDate() + (nextDay - currentDay));
          }
        } else {
          nextDate.setDate(nextDate.getDate() + (7 * interval));
        }
        break;
      
      case 'MONTHLY':
        if (pattern.dayOfMonth) {
          nextDate.setMonth(nextDate.getMonth() + interval);
          nextDate.setDate(pattern.dayOfMonth);
        } else {
          nextDate.setMonth(nextDate.getMonth() + interval);
        }
        break;
      
      case 'YEARLY':
        nextDate.setFullYear(nextDate.getFullYear() + interval);
        break;
    }

    return nextDate;
  }

  /**
   * Get notifications for a user
   * @param {String} userId - User ID
   * @param {Object} filters - Filter options
   * @param {Number} limit - Limit results
   * @param {Number} skip - Skip results
   * @returns {Promise<Array>} User notifications
   */
  async getUserNotifications(userId, filters = {}, limit = 50, skip = 0) {
    try {
      return await UserNotification.getHistoryForUser(userId, filters, limit, skip);
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notifications for a user
   * @param {String} userId - User ID
   * @param {Number} limit - Limit results
   * @returns {Promise<Array>} Unread notifications
   */
  async getUnreadNotifications(userId, limit = 50) {
    try {
      return await UserNotification.getUnreadForUser(userId, limit);
    } catch (error) {
      console.error('Error getting unread notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   * @param {String} userId - User ID
   * @param {String} userNotificationId - User notification ID
   * @returns {Promise<Object>} Updated user notification
   */
  async markAsRead(userId, userNotificationId) {
    try {
      const userNotification = await UserNotification.findOne({
        _id: userNotificationId,
        userId: userId
      });

      if (!userNotification) {
        throw new Error('User notification not found');
      }

      await userNotification.markAsRead();

      // Update notification analytics
      await this.updateNotificationAnalytics(userNotification.notificationId, 'read');

      return userNotification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark multiple notifications as read
   * @param {String} userId - User ID
   * @param {Array} notificationIds - Array of notification IDs
   * @returns {Promise<Object>} Update result
   */
  async markMultipleAsRead(userId, notificationIds) {
    try {
      const result = await UserNotification.markMultipleAsRead(userId, notificationIds);
      
      // Update analytics for each notification
      for (const notificationId of notificationIds) {
        await this.updateNotificationAnalytics(notificationId, 'read');
      }

      return result;
    } catch (error) {
      console.error('Error marking multiple notifications as read:', error);
      throw error;
    }
  }

  /**
   * Handle notification action click
   * @param {String} userId - User ID
   * @param {String} userNotificationId - User notification ID
   * @param {String} actionLabel - Action label that was clicked
   * @returns {Promise<Object>} Updated user notification
   */
  async handleActionClick(userId, userNotificationId, actionLabel) {
    try {
      const userNotification = await UserNotification.findOne({
        _id: userNotificationId,
        userId: userId
      }).populate('notificationId');

      if (!userNotification) {
        throw new Error('User notification not found');
      }

      await userNotification.markAsClicked(actionLabel);

      // Update notification analytics
      await this.updateNotificationAnalytics(userNotification.notificationId._id, 'click');

      return userNotification;
    } catch (error) {
      console.error('Error handling action click:', error);
      throw error;
    }
  }

  /**
   * Update notification analytics
   * @param {String} notificationId - Notification ID
   * @param {String} action - Action type ('read', 'click')
   * @returns {Promise<void>}
   */
  async updateNotificationAnalytics(notificationId, action) {
    try {
      const updateField = action === 'read' ? 'readCount' : 'clickCount';
      
      await Notification.findByIdAndUpdate(
        notificationId,
        {
          $inc: { [`analytics.${updateField}`]: 1 },
          $set: { 'analytics.lastAnalyticsUpdate': new Date() }
        }
      );
    } catch (error) {
      console.error('Error updating notification analytics:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Get notification statistics
   * @param {String} notificationId - Notification ID
   * @returns {Promise<Object>} Notification statistics
   */
  async getNotificationStats(notificationId) {
    try {
      const notification = await Notification.findById(notificationId);
      if (!notification) {
        throw new Error('Notification not found');
      }

      const userNotifications = await UserNotification.find({
        notificationId: notificationId
      });

      const stats = {
        totalRecipients: userNotifications.length,
        delivered: userNotifications.filter(un => un.status === 'DELIVERED').length,
        partiallyDelivered: userNotifications.filter(un => un.status === 'PARTIALLY_DELIVERED').length,
        failed: userNotifications.filter(un => un.status === 'FAILED').length,
        pending: userNotifications.filter(un => un.status === 'PENDING').length,
        read: userNotifications.filter(un => un.isRead).length,
        clicked: userNotifications.filter(un => un.isClicked).length,
        dismissed: userNotifications.filter(un => un.isDismissed).length
      };

      // Calculate rates
      if (stats.totalRecipients > 0) {
        stats.deliveryRate = ((stats.delivered + stats.partiallyDelivered) / stats.totalRecipients * 100).toFixed(2);
        stats.readRate = (stats.read / stats.totalRecipients * 100).toFixed(2);
        stats.clickRate = (stats.clicked / stats.totalRecipients * 100).toFixed(2);
      }

      return stats;
    } catch (error) {
      console.error('Error getting notification stats:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   * @param {String} notificationId - Notification ID
   * @param {String} userId - User ID who is cancelling
   * @returns {Promise<Object>} Updated notification
   */
  async cancelNotification(notificationId, userId) {
    try {
      const notification = await Notification.findById(notificationId);
      if (!notification) {
        throw new Error('Notification not found');
      }

      if (notification.status === 'SENT') {
        throw new Error('Cannot cancel notification that has already been sent');
      }

      notification.status = 'CANCELLED';
      notification.updatedBy = userId;
      await notification.save();

      // Cancel any pending user notifications
      await UserNotification.updateMany(
        { notificationId: notificationId, status: 'PENDING' },
        { status: 'CANCELLED' }
      );

      return notification;
    } catch (error) {
      console.error('Error cancelling notification:', error);
      throw error;
    }
  }

  /**
   * Process scheduled notifications
   * This method should be called by a scheduler/cron job
   * @returns {Promise<Array>} Processing results
   */
  async processScheduledNotifications() {
    try {
      const now = new Date();
      const scheduledNotifications = await Notification.find({
        status: 'SCHEDULED',
        scheduledFor: { $lte: now }
      });

      const results = [];
      for (const notification of scheduledNotifications) {
        try {
          const result = await this.processNotification(notification._id);
          results.push({
            notificationId: notification._id,
            success: true,
            result
          });

          // Handle recurring notifications
          if (notification.isRecurring && notification.recurringPattern) {
            const nextScheduled = this.calculateNextRecurrence(now, notification.recurringPattern);
            
            // Check if we haven't exceeded the end date
            if (!notification.recurringPattern.endDate || nextScheduled <= new Date(notification.recurringPattern.endDate)) {
              // Create next occurrence
              const nextNotification = new Notification({
                ...notification.toObject(),
                _id: undefined,
                id: undefined,
                scheduledFor: nextScheduled,
                status: 'SCHEDULED',
                sentAt: undefined,
                analytics: {
                  totalRecipients: 0,
                  deliveredCount: 0,
                  readCount: 0,
                  clickCount: 0
                }
              });
              
              await nextNotification.save();
            }
          }
        } catch (error) {
          console.error(`Error processing notification ${notification._id}:`, error);
          results.push({
            notificationId: notification._id,
            success: false,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error processing scheduled notifications:', error);
      throw error;
    }
  }
}

module.exports = new NotificationService();