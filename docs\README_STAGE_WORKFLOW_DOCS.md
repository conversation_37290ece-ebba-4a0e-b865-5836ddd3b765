# Stage & Workflow Management Documentation

This directory contains comprehensive documentation for the Application Stage Management and Workflow Management systems in the Screening Portal.

## 🚨 IMMEDIATE FIX for APP-2025-001

If you're seeing "No stage information available for this application" for APP-2025-001, choose the easiest option:

### Option 1: MongoDB One-Liner (EASIEST) ⭐
```bash
# 1. Connect to MongoDB
mongo your-database-name

# 2. Copy and paste this single command:
db.applications.updateOne({id: "APP-2025-001"}, {$set: {stageHierarchy: [{mainStage: "ONBOARDING", status: "NOT_STARTED", subStages: [{subStage: "BENEFICIARY_REGISTRATION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "PRE_SCREENING", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "BUSINESS_CASE_REVIEW", status: "NOT_STARTED", subStages: [{subStage: "DOCUMENT_COLLECTION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "DESKTOP_ANALYSIS", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "DUE_DILIGENCE", status: "NOT_STARTED", subStages: [{subStage: "DATA_VALIDATION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "SME_INTERVIEW", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "SITE_VISIT", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "ASSESSMENT_REPORT", status: "NOT_STARTED", subStages: [{subStage: "REPORT_COMPLETION", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "REPORT_QUALITY_CHECK", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "REPORT_REVIEW", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}, {mainStage: "APPLICATION_APPROVAL", status: "NOT_STARTED", subStages: [{subStage: "CORPORATE_APPROVAL_1", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_2", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_3", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}, {subStage: "CORPORATE_APPROVAL_4", status: {status: "NOT_STARTED", startedAt: null, completedAt: null, assignedTo: null, notes: null}, history: []}]}], currentMainStage: "ONBOARDING", currentSubStage: "BENEFICIARY_REGISTRATION", currentStageStatus: "NOT_STARTED"}})
```

### Option 2: Simple Script
```bash
mongo your-database-name fix-app-2025-001-simple.js
```

### Option 3: Node.js Script (if you have dependencies)
```bash
# Copy to backend directory first
cp fix-app-2025-001-stage-hierarchy.js backend/
cd backend
node fix-app-2025-001-stage-hierarchy.js
```

**After running any fix, refresh the application page** and the Stage Manager tab should work properly.

**Expected Result:** You should see `{ "acknowledged" : true, "matchedCount" : 1, "modifiedCount" : 1 }`

## 🎯 SCORECARD DATA FIX for APP-2025-001

If the Stage Manager is working but the Scorecard tab shows empty criteria:

### Quick Scorecard Fix:
```bash
# Make sure you're in the root directory (not backend)
cd C:\Users\<USER>\Documents\GitHub\screening_portal

# Run the MongoDB script (replace 'your-database-name' with actual database name)
mongo your-database-name fix-scorecard-app-2025-001.js
```

**Alternative - Copy/Paste Method:**
1. Connect to MongoDB: `mongo your-database-name`
2. Copy and paste the script content from **SCORECARD_FIX_INSTRUCTIONS.txt**

This will add 5 evaluation criteria:
- Identity Verification (Weight: 5)
- Business Registration (Weight: 4) 
- Financial Information (Weight: 4)
- Tax Compliance (Weight: 3)
- References (Weight: 3)

**Expected Result:** `{ "acknowledged" : true, "matchedCount" : 1, "modifiedCount" : 1 }`

---

## 📚 Documentation Files

### 1. **APPLICATION_STAGE_AND_WORKFLOW_MANAGEMENT_MANUAL.md**
**📖 Main User Manual (60+ pages)**
- Complete system overview and architecture
- Detailed stage management procedures
- Workflow configuration and management
- API reference with examples
- Troubleshooting guide
- User roles and permissions
- FAQ section

**Who should use this:** All users - reviewers, managers, administrators

### 2. **STAGE_WORKFLOW_QUICK_REFERENCE.md**
**⚡ Quick Reference Card (2 pages)**
- Stage hierarchy at a glance
- Common actions and procedures
- Status transition matrix
- Key API endpoints
- Quick troubleshooting fixes

**Who should use this:** Daily users who need quick reference

### 3. **STAGE_WORKFLOW_TECHNICAL_GUIDE.md**
**🔧 Technical Implementation Guide (30+ pages)**
- System architecture and design patterns
- Data models and database schemas
- Service layer implementation
- Performance optimization
- Security implementation
- Monitoring and deployment

**Who should use this:** Developers, system administrators, DevOps

### 4. **STAGE_MANAGER_TROUBLESHOOTING_GUIDE.md**
**🚨 Troubleshooting Guide**
- Specific fix for "No stage information available" error
- Diagnostic steps and common solutions
- Prevention measures
- Monitoring and alerts setup

**Who should use this:** Support staff, administrators, when issues occur

### 5. **fix-app-2025-001-stage-hierarchy.js**
**🛠️ Emergency Fix Script**
- Ready-to-run Node.js script
- Fixes missing stage hierarchy for APP-2025-001
- Can be adapted for other applications
- Includes detailed logging and validation

**Who should use this:** System administrators, developers

### 6. **FRONTEND_STAGE_MANAGER_FIX.md**
**🖥️ Frontend Issue Troubleshooting**
- Specific fix for when database has stage data but Stage Manager tab shows "No stage information available"
- Browser debugging steps
- API endpoint verification
- Frontend component fixes
- Cache clearing procedures

**Who should use this:** When database fix doesn't resolve the Stage Manager display issue

---

## 🎯 Quick Start Guide

### For End Users (Reviewers, Analysts)
1. Start with: **STAGE_WORKFLOW_QUICK_REFERENCE.md**
2. For detailed procedures: **APPLICATION_STAGE_AND_WORKFLOW_MANAGEMENT_MANUAL.md**
3. For issues: **STAGE_MANAGER_TROUBLESHOOTING_GUIDE.md**

### For Administrators
1. Read: **APPLICATION_STAGE_AND_WORKFLOW_MANAGEMENT_MANUAL.md** (Sections 3-4)
2. Keep handy: **STAGE_MANAGER_TROUBLESHOOTING_GUIDE.md**
3. For system issues: **fix-app-2025-001-stage-hierarchy.js**

### For Developers
1. Start with: **STAGE_WORKFLOW_TECHNICAL_GUIDE.md**
2. Reference: **APPLICATION_STAGE_AND_WORKFLOW_MANAGEMENT_MANUAL.md** (API section)
3. For debugging: **STAGE_MANAGER_TROUBLESHOOTING_GUIDE.md**

---

## 🏗️ System Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Stage Manager  │  │ Workflow Manager│                  │
│  │      Tab        │  │      Tab        │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    API Gateway                              │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ /applications   │  │   /workflows    │                  │
│  │   endpoints     │  │   endpoints     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Stage Manager   │  │ Workflow Manager│                  │
│  │    Service      │  │    Service      │                  │
│  └─────────────────┘  └─────────────────┘                  │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Audit Trail     │  │ Notification    │                  │
│  │   Service       │  │   Service       │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer (MongoDB)                     │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Applications   │  │   Workflows     │                  │
│  │   Collection    │  │   Collection    │                  │
│  └─────────────────┘  └─────────────────┘                  │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Stage Audit     │  │ Workflow        │                  │
│  │   Collection    │  │ Assignments     │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔍 Stage Hierarchy Overview

```
Main Stages (5) → Sub-Stages (17) → Status (5)

1. ONBOARDING
   ├── BENEFICIARY_REGISTRATION
   └── PRE_SCREENING

2. BUSINESS_CASE_REVIEW  
   ├── DOCUMENT_COLLECTION
   └── DESKTOP_ANALYSIS

3. DUE_DILIGENCE
   ├── DATA_VALIDATION
   ├── SME_INTERVIEW
   └── SITE_VISIT

4. ASSESSMENT_REPORT
   ├── REPORT_COMPLETION
   ├── REPORT_QUALITY_CHECK
   └── REPORT_REVIEW

5. APPLICATION_APPROVAL
   ├── CORPORATE_APPROVAL_1
   ├── CORPORATE_APPROVAL_2
   ├── CORPORATE_APPROVAL_3
   └── CORPORATE_APPROVAL_4

Status Types:
• NOT_STARTED → ACTIVE → COMPLETED
• SKIPPED (with justification)
• NOT_APPLICABLE (business rule exclusion)
```

---

## 🆘 Emergency Contacts

**For Immediate Issues:**
- System Administrator: [Contact Info]
- IT Help Desk: [Contact Info]
- On-call Developer: [Contact Info]

**For the APP-2025-001 Issue:**
1. Run the fix script: `node fix-app-2025-001-stage-hierarchy.js`
2. If script fails, use the MongoDB direct fix above
3. Restart backend services if needed
4. Contact administrator if issue persists

---

## 📝 Document Maintenance

**Last Updated:** January 2025  
**Version:** 1.0  
**Next Review:** April 2025  

**Update Process:**
1. Technical changes → Update STAGE_WORKFLOW_TECHNICAL_GUIDE.md
2. User procedure changes → Update APPLICATION_STAGE_AND_WORKFLOW_MANAGEMENT_MANUAL.md  
3. New issues discovered → Update STAGE_MANAGER_TROUBLESHOOTING_GUIDE.md
4. Quick reference changes → Update STAGE_WORKFLOW_QUICK_REFERENCE.md

---

*For additional support or questions not covered in these documents, please contact the IT Help Desk or your system administrator.*
