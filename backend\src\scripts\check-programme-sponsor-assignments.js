const mongoose = require('mongoose');
const Application = require('../models/application');
const FundingProgramme = require('../models/funding-programme');
const CorporateSponsor = require('../models/corporate-sponsor');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

async function checkProgrammeSponsorAssignments() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');

    // Get all applications with populated references
    const applications = await Application.find({})
      .populate('programmeId')
      .populate('corporateSponsorId')
      .lean();

    console.log(`\nFound ${applications.length} applications\n`);

    // Check for mismatches
    let mismatchCount = 0;
    const mismatches = [];

    for (const app of applications) {
      if (app.programmeId && app.corporateSponsorId) {
        // Get the programme's assigned sponsor
        const programme = app.programmeId;
        const appSponsor = app.corporateSponsorId;
        
        // Check if programme has a corporateSponsorId
        if (programme.corporateSponsorId) {
          // Compare the programme's sponsor with the application's sponsor
          const programmeSponsorId = programme.corporateSponsorId.toString();
          const appSponsorId = appSponsor._id ? appSponsor._id.toString() : app.corporateSponsorId.toString();
          
          if (programmeSponsorId !== appSponsorId) {
            mismatchCount++;
            mismatches.push({
              applicationId: app.id,
              programmeName: programme.name,
              programmeSponsorId: programmeSponsorId,
              applicationSponsorName: appSponsor.name || 'Unknown',
              applicationSponsorId: appSponsorId
            });
          }
        }
      }
    }

    console.log(`\n=== ANALYSIS RESULTS ===`);
    console.log(`Total Applications: ${applications.length}`);
    console.log(`Mismatched Assignments: ${mismatchCount}`);
    
    if (mismatchCount > 0) {
      console.log(`\n⚠️  MISMATCHES FOUND:`);
      console.log('Applications assigned to programmes from different sponsors:\n');
      
      mismatches.forEach((mismatch, index) => {
        console.log(`${index + 1}. Application ${mismatch.applicationId}:`);
        console.log(`   - Programme: ${mismatch.programmeName}`);
        console.log(`   - Programme's Sponsor ID: ${mismatch.programmeSponsorId}`);
        console.log(`   - Application's Sponsor: ${mismatch.applicationSponsorName} (ID: ${mismatch.applicationSponsorId})`);
        console.log('');
      });
    } else {
      console.log('\n✅ No mismatches found! All applications are correctly assigned.');
    }

    // Additional analysis: Show programme-sponsor relationships
    console.log('\n=== PROGRAMME-SPONSOR RELATIONSHIPS ===');
    const programmes = await FundingProgramme.find({}).populate('corporateSponsorId').lean();
    
    programmes.forEach(prog => {
      const sponsorName = prog.corporateSponsorId ? prog.corporateSponsorId.name : 'No Sponsor';
      console.log(`- ${prog.name} → ${sponsorName}`);
    });

    // Show application distribution
    console.log('\n=== APPLICATION DISTRIBUTION ===');
    const distribution = {};
    
    applications.forEach(app => {
      if (app.programmeId && app.corporateSponsorId) {
        const key = `${app.programmeId.name} (${app.corporateSponsorId.name})`;
        distribution[key] = (distribution[key] || 0) + 1;
      }
    });
    
    Object.entries(distribution).forEach(([key, count]) => {
      console.log(`- ${key}: ${count} applications`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the check
checkProgrammeSponsorAssignments();