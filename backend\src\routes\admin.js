const express = require('express');
const { check, validationResult } = require('express-validator');

const User = require('../models/user');
const Role = require('../models/role');
const Permission = require('../models/permission');
const { authenticateToken, authorizeRole } = require('../middleware/auth');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticateToken);
router.use(authorizeRole(['admin', 'SYSTEM_ADMINISTRATOR']));

// User Management Routes

// GET /api/admin/users - Get all users
router.get('/users', async (req, res) => {
  try {
    const users = await User.find().select('-password');
    
    res.json({
      users: users.map(user => ({
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: user.roles,
        permissions: user.permissions,
        role: user.role, // Legacy field
        organizationType: user.organizationType,
        organizationId: user.organizationId,
        programmeAssignments: user.programmeAssignments,
        isActive: user.isActive,
        status: user.status,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }))
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// GET /api/admin/users/:id - Get user by ID
router.get('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    res.json({
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: user.roles,
        permissions: user.permissions,
        role: user.role, // Legacy field
        organizationType: user.organizationType,
        organizationId: user.organizationId,
        programmeAssignments: user.programmeAssignments,
        isActive: user.isActive,
        status: user.status,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// POST /api/admin/users - Create user
router.post('/users', [
  check('username', 'Username is required and must be at least 3 characters').isLength({ min: 3 }),
  check('email', 'Please include a valid email').isEmail(),
  check('password', 'Password must be at least 8 characters').isLength({ min: 8 }),
  check('firstName', 'First name is required').not().isEmpty(),
  check('lastName', 'Last name is required').not().isEmpty(),
  check('roles', 'Roles must be an array').optional().isArray()
], async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation error',
        details: errors.array()
      }
    });
  }

  const { 
    username, 
    email, 
    password, 
    firstName, 
    lastName, 
    role, 
    roles, 
    permissions, 
    organizationType, 
    organizationId,
    programmeAssignments
  } = req.body;

  try {
    // Check if user already exists
    let user = await User.findOne({ $or: [{ email }, { username }] });
    
    if (user) {
      return res.status(400).json({
        error: {
          code: 'USER_EXISTS',
          message: 'User already exists'
        }
      });
    }

    // Create new user
    user = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      role: role || 'READ_ONLY_USER',
      roles: roles || ['user'],
      permissions: permissions || [],
      organizationType: organizationType || '20/20Insight',
      organizationId,
      programmeAssignments: programmeAssignments || []
    });

    await user.save();

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: user.roles,
        permissions: user.permissions,
        role: user.role,
        organizationType: user.organizationType,
        organizationId: user.organizationId,
        programmeAssignments: user.programmeAssignments
      }
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// PUT /api/admin/users/:id - Update user
router.put('/users/:id', [
  check('username', 'Username must be at least 3 characters').optional().isLength({ min: 3 }),
  check('email', 'Please include a valid email').optional().isEmail(),
  check('firstName', 'First name is required').optional().not().isEmpty(),
  check('lastName', 'Last name is required').optional().not().isEmpty(),
  check('roles', 'Roles must be an array').optional().isArray(),
  check('isActive', 'isActive must be a boolean').optional().isBoolean()
], async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation error',
        details: errors.array()
      }
    });
  }

  const { 
    username, 
    email, 
    firstName, 
    lastName, 
    role,
    roles, 
    permissions, 
    isActive,
    status,
    organizationType,
    organizationId,
    programmeAssignments
  } = req.body;

  try {
    // Find user
    let user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    // Check if username is already in use by another user
    if (username && username !== user.username) {
      const existingUser = await User.findOne({ username });
      
      if (existingUser && existingUser._id.toString() !== req.params.id) {
        return res.status(400).json({
          error: {
            code: 'USERNAME_IN_USE',
            message: 'Username is already in use'
          }
        });
      }
      
      user.username = username;
    }

    // Check if email is already in use by another user
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      
      if (existingUser && existingUser._id.toString() !== req.params.id) {
        return res.status(400).json({
          error: {
            code: 'EMAIL_IN_USE',
            message: 'Email is already in use'
          }
        });
      }
      
      user.email = email;
    }

    // Update user fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (role) user.role = role;
    if (roles) user.roles = roles;
    if (permissions) user.permissions = permissions;
    if (isActive !== undefined) user.isActive = isActive;
    if (status) user.status = status;
    if (organizationType) user.organizationType = organizationType;
    if (organizationId !== undefined) user.organizationId = organizationId;
    if (programmeAssignments) user.programmeAssignments = programmeAssignments;
    
    user.updatedAt = Date.now();
    
    await user.save();

    res.json({
      message: 'User updated successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: user.roles,
        permissions: user.permissions,
        role: user.role,
        organizationType: user.organizationType,
        organizationId: user.organizationId,
        programmeAssignments: user.programmeAssignments,
        isActive: user.isActive,
        status: user.status
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// DELETE /api/admin/users/:id - Delete user
router.delete('/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    // Don't allow deleting the last admin
    if (user.roles.includes('admin') || user.role === 'SYSTEM_ADMINISTRATOR') {
      const adminCount = await User.countDocuments({ 
        $or: [
          { roles: 'admin' },
          { role: 'SYSTEM_ADMINISTRATOR' }
        ]
      });
      
      if (adminCount <= 1) {
        return res.status(400).json({
          error: {
            code: 'LAST_ADMIN',
            message: 'Cannot delete the last admin user'
          }
        });
      }
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// Role Management Routes

// GET /api/admin/roles - Get all roles
router.get('/roles', async (req, res) => {
  try {
    const roles = await Role.find();
    
    res.json({
      roles: roles.map(role => ({
        id: role._id,
        name: role.name,
        description: role.description,
        permissions: role.permissions,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt
      }))
    });
  } catch (error) {
    console.error('Get roles error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// POST /api/admin/roles - Create role
router.post('/roles', [
  check('name', 'Role name is required').not().isEmpty(),
  check('permissions', 'Permissions must be an array').optional().isArray()
], async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation error',
        details: errors.array()
      }
    });
  }

  const { name, description, permissions } = req.body;

  try {
    // Check if role already exists
    let role = await Role.findOne({ name });
    
    if (role) {
      return res.status(400).json({
        error: {
          code: 'ROLE_EXISTS',
          message: 'Role already exists'
        }
      });
    }

    // Create new role
    role = new Role({
      name,
      description,
      permissions: permissions || []
    });

    await role.save();

    res.status(201).json({
      message: 'Role created successfully',
      role: {
        id: role._id,
        name: role.name,
        description: role.description,
        permissions: role.permissions
      }
    });
  } catch (error) {
    console.error('Create role error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// PUT /api/admin/roles/:id - Update role
router.put('/roles/:id', [
  check('name', 'Role name is required').optional().not().isEmpty(),
  check('permissions', 'Permissions must be an array').optional().isArray()
], async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Validation error',
        details: errors.array()
      }
    });
  }

  const { name, description, permissions } = req.body;

  try {
    let role = await Role.findById(req.params.id);
    
    if (!role) {
      return res.status(404).json({
        error: {
          code: 'ROLE_NOT_FOUND',
          message: 'Role not found'
        }
      });
    }

    // Check if name is already in use by another role
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({ name });
      
      if (existingRole && existingRole._id.toString() !== req.params.id) {
        return res.status(400).json({
          error: {
            code: 'ROLE_NAME_IN_USE',
            message: 'Role name is already in use'
          }
        });
      }
      
      role.name = name;
    }

    // Update role fields
    if (description !== undefined) role.description = description;
    if (permissions) role.permissions = permissions;
    
    role.updatedAt = Date.now();
    
    await role.save();

    res.json({
      message: 'Role updated successfully',
      role: {
        id: role._id,
        name: role.name,
        description: role.description,
        permissions: role.permissions
      }
    });
  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// DELETE /api/admin/roles/:id - Delete role
router.delete('/roles/:id', async (req, res) => {
  try {
    const role = await Role.findById(req.params.id);
    
    if (!role) {
      return res.status(404).json({
        error: {
          code: 'ROLE_NOT_FOUND',
          message: 'Role not found'
        }
      });
    }

    // Check if role is being used by any users
    const usersWithRole = await User.countDocuments({ roles: role.name });
    
    if (usersWithRole > 0) {
      return res.status(400).json({
        error: {
          code: 'ROLE_IN_USE',
          message: 'Cannot delete role that is assigned to users'
        }
      });
    }

    await Role.findByIdAndDelete(req.params.id);

    res.json({
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Delete role error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

// Permission Management Routes

// GET /api/admin/permissions - Get all permissions
router.get('/permissions', async (req, res) => {
  try {
    const permissions = await Permission.find();
    
    res.json({
      permissions: permissions.map(permission => ({
        id: permission._id,
        name: permission.name,
        description: permission.description,
        resource: permission.resource,
        action: permission.action,
        createdAt: permission.createdAt,
        updatedAt: permission.updatedAt
      }))
    });
  } catch (error) {
    console.error('Get permissions error:', error);
    res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Server error'
      }
    });
  }
});

module.exports = router;