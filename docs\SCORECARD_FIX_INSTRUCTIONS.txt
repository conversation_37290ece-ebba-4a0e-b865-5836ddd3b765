# SCORECARD FIX INSTRUCTIONS for APP-2025-001

## CORRECT USAGE:

The script needs to be run WITH the mongo command, not directly.

### Option 1: Run the MongoDB script file
```bash
# Make sure you're in the root directory (not backend)
cd C:\Users\<USER>\Documents\GitHub\screening_portal

# Run the script with mongo command
mongo your-database-name fix-scorecard-app-2025-001.js
```

### Option 2: Copy and paste the script content directly
```bash
# Connect to MongoDB first
mongo your-database-name

# Then copy and paste this entire script:
```

```javascript
// Quick fix to add scorecard data to APP-2025-001
print("Adding scorecard data to APP-2025-001...");

var result = db.applications.updateOne(
  {id: "APP-2025-001"},
  {
    $set: {
      scorecard: {
        templateName: "Due Diligence Data Validation Scorecard",
        stage: "DUE_DILIGENCE",
        subStage: "DATA_VALIDATION", 
        status: "in_progress",
        scores: [
          {
            criteriaId: "DV001",
            category: "Identity Verification",
            text: "Valid ID document provided and verified",
            description: "Check if the applicant has provided a valid, current ID document",
            weight: 5,
            required: true,
            score: null,
            comment: "",
            maxScore: 5
          },
          {
            criteriaId: "DV002",
            category: "Business Registration", 
            text: "Business registration documents verified",
            description: "Verify business registration with relevant authorities",
            weight: 4,
            required: true,
            score: null,
            comment: "",
            maxScore: 5
          },
          {
            criteriaId: "DV003",
            category: "Financial Information",
            text: "Financial statements accuracy verified", 
            description: "Cross-check financial statements with supporting documents",
            weight: 4,
            required: true,
            score: null,
            comment: "",
            maxScore: 5
          },
          {
            criteriaId: "DV004",
            category: "Tax Compliance",
            text: "Tax compliance status verified",
            description: "Verify tax registration and compliance status",
            weight: 3,
            required: true,
            score: null,
            comment: "",
            maxScore: 5
          },
          {
            criteriaId: "DV005",
            category: "References",
            text: "Business and personal references verified",
            description: "Contact and verify provided references",
            weight: 3,
            required: false,
            score: null,
            comment: "",
            maxScore: 5
          }
        ],
        overallScore: null,
        totalPossibleScore: 105,
        completedAt: null,
        lastUpdated: new Date(),
        createdAt: new Date()
      }
    }
  }
);

if (result.modifiedCount === 1) {
  print("SUCCESS: Scorecard data added to APP-2025-001");
  print("Scorecard contains 5 evaluation criteria:");
  print("  - Identity Verification (Weight: 5)");
  print("  - Business Registration (Weight: 4)");
  print("  - Financial Information (Weight: 4)");
  print("  - Tax Compliance (Weight: 3)");
  print("  - References (Weight: 3)");
  print("");
  print("Refresh the application page and check the Scorecard tab.");
} else {
  print("ERROR: Failed to add scorecard data");
}

// Verify the scorecard was added
var app = db.applications.findOne({id: "APP-2025-001"}, {scorecard: 1});
if (app && app.scorecard) {
  print("VERIFICATION: Scorecard found with " + app.scorecard.scores.length + " criteria");
} else {
  print("VERIFICATION: No scorecard found - something went wrong");
}
```

### Option 3: One-liner command (if you prefer)
```bash
# Connect to MongoDB
mongo your-database-name

# Run this single command:
db.applications.updateOne({id: "APP-2025-001"}, {$set: {scorecard: {templateName: "Due Diligence Data Validation Scorecard", stage: "DUE_DILIGENCE", subStage: "DATA_VALIDATION", status: "in_progress", scores: [{criteriaId: "DV001", category: "Identity Verification", text: "Valid ID document provided and verified", weight: 5, required: true, score: null, comment: "", maxScore: 5}, {criteriaId: "DV002", category: "Business Registration", text: "Business registration documents verified", weight: 4, required: true, score: null, comment: "", maxScore: 5}, {criteriaId: "DV003", category: "Financial Information", text: "Financial statements accuracy verified", weight: 4, required: true, score: null, comment: "", maxScore: 5}, {criteriaId: "DV004", category: "Tax Compliance", text: "Tax compliance status verified", weight: 3, required: true, score: null, comment: "", maxScore: 5}, {criteriaId: "DV005", category: "References", text: "Business and personal references verified", weight: 3, required: false, score: null, comment: "", maxScore: 5}], overallScore: null, totalPossibleScore: 105, completedAt: null, lastUpdated: new Date(), createdAt: new Date()}}})
```

## EXPECTED RESULT:
You should see:
```
{ "acknowledged" : true, "matchedCount" : 1, "modifiedCount" : 1 }
```

## AFTER RUNNING THE FIX:
1. Refresh the application page
2. Navigate to APP-2025-001 → Application Stages → Scorecard tab
3. You should now see 5 evaluation criteria with weights and scoring fields

## TROUBLESHOOTING:
- Make sure you're connected to the correct database
- Replace "your-database-name" with your actual database name
- If you get permission errors, make sure you have write access to the database
- If the script doesn't work, try the copy/paste option instead
