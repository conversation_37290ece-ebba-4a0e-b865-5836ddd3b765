const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Models
const ScorecardTemplate = require('../models/scorecard-template');

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-app';

async function runMigration() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');

    // Check if templates already exist
    const existingTemplates = await ScorecardTemplate.countDocuments();
    if (existingTemplates > 0) {
      console.log(`Found ${existingTemplates} existing templates. Skipping template creation.`);
      await mongoose.disconnect();
      process.exit(0);
    }

    console.log('Creating default scorecard templates...');

    // Default templates to create based on actual application stages and substages
    const defaultTemplates = [
      {
        name: 'Beneficiary Registration Evaluation',
        description: 'Template for evaluating beneficiary registration process and documentation',
        category: 'beneficiary-registration',
        version: 1,
        isActive: true,
        allowCustomCriteria: true,
        requireAllCriteria: false,
        passingScore: 60,
        applicableStages: ['Onboarding'],
        applicableSubstages: ['BENEFICIARY_REGISTRATION'],
        criteria: [
          {
            id: 'criteria_registration_completeness',
            name: 'Registration Completeness',
            description: 'All required registration information and documents are provided',
            weight: 40,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_eligibility_verification',
            name: 'Eligibility Verification',
            description: 'Beneficiary meets all eligibility criteria for the program',
            weight: 35,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_documentation_quality',
            name: 'Documentation Quality',
            description: 'Quality and authenticity of submitted documents',
            weight: 25,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'Pre-screening Evaluation',
        description: 'Initial evaluation template for pre-screening applications to assess basic eligibility and potential',
        category: 'pre-screening',
        version: 1,
        isActive: true,
        allowCustomCriteria: true,
        requireAllCriteria: false,
        passingScore: 65,
        applicableStages: ['Onboarding'],
        applicableSubstages: ['PRE_SCREENING'],
        criteria: [
          {
            id: 'criteria_basic_eligibility',
            name: 'Basic Eligibility',
            description: 'Meets fundamental eligibility requirements for the program',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_business_potential',
            name: 'Business Potential',
            description: 'Initial assessment of business viability and growth potential',
            weight: 40,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_risk_indicators',
            name: 'Risk Indicators',
            description: 'Assessment of potential risk factors and red flags',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'Document Collection Evaluation',
        description: 'Template for evaluating document collection completeness and quality',
        category: 'document-collection',
        version: 1,
        isActive: true,
        allowCustomCriteria: false,
        requireAllCriteria: true,
        passingScore: 70,
        applicableStages: ['Business Case Review'],
        applicableSubstages: ['DOCUMENT_COLLECTION'],
        criteria: [
          {
            id: 'criteria_document_completeness',
            name: 'Document Completeness',
            description: 'All required documents have been collected and submitted',
            weight: 40,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_document_authenticity',
            name: 'Document Authenticity',
            description: 'Documents are authentic and properly verified',
            weight: 35,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_document_currency',
            name: 'Document Currency',
            description: 'Documents are current and within required validity periods',
            weight: 25,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'Desktop Analysis Evaluation',
        description: 'Comprehensive desktop analysis template for assessing business case and financial viability',
        category: 'desktop-analysis',
        version: 1,
        isActive: true,
        allowCustomCriteria: false,
        requireAllCriteria: true,
        passingScore: 70,
        applicableStages: ['Business Case Review'],
        applicableSubstages: ['DESKTOP_ANALYSIS'],
        criteria: [
          {
            id: 'criteria_financial_analysis',
            name: 'Financial Analysis',
            description: 'Analysis of financial statements, projections, and viability',
            weight: 35,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_market_analysis',
            name: 'Market Analysis',
            description: 'Assessment of market opportunity and competitive landscape',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_business_model',
            name: 'Business Model Assessment',
            description: 'Evaluation of business model sustainability and scalability',
            weight: 35,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'SME Interview Evaluation',
        description: 'Template for evaluating SME interviews, assessing management team and business understanding',
        category: 'sme-interview',
        version: 1,
        isActive: true,
        allowCustomCriteria: true,
        requireAllCriteria: false,
        passingScore: 65,
        applicableStages: ['Due Diligence'],
        applicableSubstages: ['SME_INTERVIEW'],
        criteria: [
          {
            id: 'criteria_management_competency',
            name: 'Management Competency',
            description: 'Assessment of management team skills, experience, and leadership capability',
            weight: 35,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_business_knowledge',
            name: 'Business Knowledge',
            description: 'Depth of understanding of business operations, market, and industry',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_strategic_vision',
            name: 'Strategic Vision',
            description: 'Clarity of strategic direction and growth plans',
            weight: 20,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          },
          {
            id: 'criteria_commitment_level',
            name: 'Commitment Level',
            description: 'Demonstrated commitment and dedication to the business venture',
            weight: 15,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 4
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'Site Visit Evaluation',
        description: 'Template for evaluating site visits, assessing physical infrastructure, operations, and compliance',
        category: 'site-visit',
        version: 1,
        isActive: true,
        allowCustomCriteria: true,
        requireAllCriteria: false,
        passingScore: 70,
        applicableStages: ['Due Diligence'],
        applicableSubstages: ['SITE_VISIT'],
        criteria: [
          {
            id: 'criteria_physical_infrastructure',
            name: 'Physical Infrastructure',
            description: 'Quality, adequacy, and condition of business premises, equipment, and facilities',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_operational_efficiency',
            name: 'Operational Efficiency',
            description: 'Efficiency, organization, and effectiveness of business operations and processes',
            weight: 25,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_human_resources',
            name: 'Human Resources',
            description: 'Adequacy and quality of human resources, skills, and operational capacity',
            weight: 25,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          },
          {
            id: 'criteria_compliance_standards',
            name: 'Compliance Standards',
            description: 'Adherence to regulatory requirements, safety standards, and industry best practices',
            weight: 20,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 4
          }
        ],
        createdBy: 'system',
        usageCount: 0
      },
      {
        name: 'Committee Review Evaluation',
        description: 'Template for committee review process and final decision making',
        category: 'committee-review',
        version: 1,
        isActive: true,
        allowCustomCriteria: true,
        requireAllCriteria: false,
        passingScore: 75,
        applicableStages: ['Application Approval'],
        applicableSubstages: ['COMMITTEE_REVIEW'],
        criteria: [
          {
            id: 'criteria_overall_assessment',
            name: 'Overall Assessment',
            description: 'Comprehensive evaluation of all previous stages and findings',
            weight: 40,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 1
          },
          {
            id: 'criteria_risk_evaluation',
            name: 'Risk Evaluation',
            description: 'Assessment of identified risks and mitigation strategies',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 2
          },
          {
            id: 'criteria_strategic_alignment',
            name: 'Strategic Alignment',
            description: 'Alignment with program objectives and strategic priorities',
            weight: 30,
            maxScore: 10,
            scoreType: 'numeric',
            required: true,
            order: 3
          }
        ],
        createdBy: 'system',
        usageCount: 0
      }
    ];

    // Create templates
    let createdCount = 0;
    for (const templateData of defaultTemplates) {
      try {
        const template = new ScorecardTemplate(templateData);
        await template.save();
        console.log(`Created template: ${template.name}`);
        createdCount++;
      } catch (error) {
        console.error(`Error creating template "${templateData.name}":`, error.message);
      }
    }

    console.log(`Migration complete. Created ${createdCount} scorecard templates.`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');

    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();