# Frontend Scorecard Dropdown Fix

## Problem
The Scorecard Management interface has:
1. **Dropdown labeled as "Category"** but should show "Stage" and "Sub-Stage"
2. **No sub-stage dropdown** - only shows main stages
3. **Stages mislabeled as "criteria"** in the dropdown options

## Root Cause
The frontend component is not properly configured to:
1. Show separate Stage and Sub-Stage dropdowns
2. Load the correct stage/sub-stage options
3. Use proper labels for the dropdowns

## Frontend Files to Update

### 1. Update Scorecard Management Component

**File:** `frontend/src/components/ScorecardManagement.jsx` (or similar)

**Current Issue:** Single "Category" dropdown with main stages
**Fix:** Add separate Stage and Sub-Stage dropdowns

```jsx
// BEFORE (Current broken implementation)
<div className="form-group">
  <label>Category*</label>
  <select name="category">
    <option value="">Select Category</option>
    <option value="ONBOARDING">Onboarding</option>
    <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
    <option value="DUE_DILIGENCE">Due Diligence</option>
    <option value="ASSESSMENT_REPORT">Assessment Report</option>
    <option value="APPLICATION_APPROVAL">Application Approval</option>
  </select>
</div>

// AFTER (Fixed implementation)
<div className="form-row">
  <div className="form-group col-md-6">
    <label>Stage*</label>
    <select 
      name="mainStage" 
      value={formData.mainStage} 
      onChange={handleStageChange}
      required
    >
      <option value="">Select Stage</option>
      <option value="ONBOARDING">Onboarding</option>
      <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
      <option value="DUE_DILIGENCE">Due Diligence</option>
      <option value="ASSESSMENT_REPORT">Assessment Report</option>
      <option value="APPLICATION_APPROVAL">Application Approval</option>
    </select>
  </div>
  
  <div className="form-group col-md-6">
    <label>Sub-Stage*</label>
    <select 
      name="subStage" 
      value={formData.subStage} 
      onChange={handleSubStageChange}
      required
      disabled={!formData.mainStage}
    >
      <option value="">Select Sub-Stage</option>
      {getSubStagesForMainStage(formData.mainStage).map(subStage => (
        <option key={subStage.value} value={subStage.value}>
          {subStage.label}
        </option>
      ))}
    </select>
  </div>
</div>
```

### 2. Add Stage/Sub-Stage Logic

```jsx
// Add this to your ScorecardManagement component

const [formData, setFormData] = useState({
  templateName: '',
  mainStage: '',
  subStage: '',
  description: '',
  passingScore: 70,
  scope: 'Programme Specific'
});

// Stage to Sub-Stage mapping
const stageSubStageMap = {
  'ONBOARDING': [
    { value: 'BENEFICIARY_REGISTRATION', label: 'Beneficiary Registration' },
    { value: 'PRE_SCREENING', label: 'Pre-Screening' }
  ],
  'BUSINESS_CASE_REVIEW': [
    { value: 'DOCUMENT_COLLECTION', label: 'Document Collection' },
    { value: 'DESKTOP_ANALYSIS', label: 'Desktop Analysis' }
  ],
  'DUE_DILIGENCE': [
    { value: 'DATA_VALIDATION', label: 'Data Validation' },
    { value: 'SME_INTERVIEW', label: 'SME Interview' },
    { value: 'SITE_VISIT', label: 'Site Visit' }
  ],
  'ASSESSMENT_REPORT': [
    { value: 'REPORT_COMPLETION', label: 'Report Completion' },
    { value: 'REPORT_QUALITY_CHECK', label: 'Report Quality Check' },
    { value: 'REPORT_REVIEW', label: 'Report Review' }
  ],
  'APPLICATION_APPROVAL': [
    { value: 'CORPORATE_APPROVAL_1', label: 'Corporate Approval Level 1' },
    { value: 'CORPORATE_APPROVAL_2', label: 'Corporate Approval Level 2' },
    { value: 'CORPORATE_APPROVAL_3', label: 'Corporate Approval Level 3' },
    { value: 'CORPORATE_APPROVAL_4', label: 'Corporate Approval Level 4' }
  ]
};

// Helper function to get sub-stages for selected main stage
const getSubStagesForMainStage = (mainStage) => {
  return stageSubStageMap[mainStage] || [];
};

// Handle main stage change
const handleStageChange = (e) => {
  const mainStage = e.target.value;
  setFormData({
    ...formData,
    mainStage: mainStage,
    subStage: '' // Reset sub-stage when main stage changes
  });
};

// Handle sub-stage change
const handleSubStageChange = (e) => {
  setFormData({
    ...formData,
    subStage: e.target.value
  });
};

// Handle form submission
const handleSubmit = async (e) => {
  e.preventDefault();
  
  const templateData = {
    name: formData.templateName,
    mainStage: formData.mainStage,
    subStage: formData.subStage,
    category: formData.subStage, // Use subStage as category for backward compatibility
    description: formData.description,
    passingScore: formData.passingScore,
    scope: formData.scope,
    status: 'active',
    version: '1.0',
    allowCustomCriteria: true,
    requireAllCriteria: false,
    criteria: []
  };
  
  try {
    const response = await fetch('/api/v1/scorecard-templates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(templateData)
    });
    
    if (response.ok) {
      alert('Scorecard template created successfully!');
      // Reset form or redirect
    } else {
      alert('Failed to create scorecard template');
    }
  } catch (error) {
    console.error('Error creating template:', error);
    alert('Error creating scorecard template');
  }
};
```

### 3. Update Template Library Display

```jsx
// Update the template library to show Stage and Sub-Stage columns
const TemplateLibrary = ({ templates }) => {
  return (
    <div className="template-library">
      <h3>Template Library</h3>
      <table className="table">
        <thead>
          <tr>
            <th>Template Name</th>
            <th>Stage</th>
            <th>Sub-Stage</th>
            <th>Criteria Count</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {templates.map(template => (
            <tr key={template._id}>
              <td>{template.name}</td>
              <td>{template.mainStage}</td>
              <td>{template.subStage}</td>
              <td>{template.criteria?.length || 0}</td>
              <td>
                <span className={`badge ${template.status === 'active' ? 'badge-success' : 'badge-secondary'}`}>
                  {template.status}
                </span>
              </td>
              <td>
                <button className="btn btn-sm btn-primary" onClick={() => editTemplate(template)}>
                  Edit
                </button>
                <button className="btn btn-sm btn-danger" onClick={() => deleteTemplate(template._id)}>
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

### 4. Update CSS for Better Layout

```css
/* Add to your CSS file */
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group select:disabled {
  background-color: #f5f5f5;
  color: #666;
}

.template-library table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.template-library th,
.template-library td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.template-library th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}
```

## Backend API Updates

### 1. Update Scorecard Template Model

**File:** `backend/src/models/scorecard-template.js`

```javascript
const scorecardTemplateSchema = new mongoose.Schema({
  name: { type: String, required: true },
  mainStage: { 
    type: String, 
    required: true,
    enum: ['ONBOARDING', 'BUSINESS_CASE_REVIEW', 'DUE_DILIGENCE', 'ASSESSMENT_REPORT', 'APPLICATION_APPROVAL']
  },
  subStage: { 
    type: String, 
    required: true,
    enum: [
      'BENEFICIARY_REGISTRATION', 'PRE_SCREENING',
      'DOCUMENT_COLLECTION', 'DESKTOP_ANALYSIS',
      'DATA_VALIDATION', 'SME_INTERVIEW', 'SITE_VISIT',
      'REPORT_COMPLETION', 'REPORT_QUALITY_CHECK', 'REPORT_REVIEW',
      'CORPORATE_APPROVAL_1', 'CORPORATE_APPROVAL_2', 'CORPORATE_APPROVAL_3', 'CORPORATE_APPROVAL_4'
    ]
  },
  category: { type: String }, // Keep for backward compatibility
  description: String,
  scope: { 
    type: String, 
    enum: ['Programme Specific', 'Universal'],
    default: 'Programme Specific'
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'draft'],
    default: 'active'
  },
  version: { type: String, default: '1.0' },
  passingScore: { type: Number, default: 70 },
  allowCustomCriteria: { type: Boolean, default: true },
  requireAllCriteria: { type: Boolean, default: false },
  criteria: [{
    id: String,
    text: String,
    description: String,
    weight: Number,
    required: Boolean,
    category: String
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

### 2. Add API Endpoint for Stage/Sub-Stage Options

**File:** `backend/src/routes/scorecard-templates.js`

```javascript
// GET /api/v1/scorecard-templates/stage-options
router.get('/stage-options', async (req, res) => {
  try {
    const stageOptions = {
      mainStages: [
        { value: 'ONBOARDING', label: 'Onboarding' },
        { value: 'BUSINESS_CASE_REVIEW', label: 'Business Case Review' },
        { value: 'DUE_DILIGENCE', label: 'Due Diligence' },
        { value: 'ASSESSMENT_REPORT', label: 'Assessment Report' },
        { value: 'APPLICATION_APPROVAL', label: 'Application Approval' }
      ],
      subStages: {
        'ONBOARDING': [
          { value: 'BENEFICIARY_REGISTRATION', label: 'Beneficiary Registration' },
          { value: 'PRE_SCREENING', label: 'Pre-Screening' }
        ],
        'BUSINESS_CASE_REVIEW': [
          { value: 'DOCUMENT_COLLECTION', label: 'Document Collection' },
          { value: 'DESKTOP_ANALYSIS', label: 'Desktop Analysis' }
        ],
        'DUE_DILIGENCE': [
          { value: 'DATA_VALIDATION', label: 'Data Validation' },
          { value: 'SME_INTERVIEW', label: 'SME Interview' },
          { value: 'SITE_VISIT', label: 'Site Visit' }
        ],
        'ASSESSMENT_REPORT': [
          { value: 'REPORT_COMPLETION', label: 'Report Completion' },
          { value: 'REPORT_QUALITY_CHECK', label: 'Report Quality Check' },
          { value: 'REPORT_REVIEW', label: 'Report Review' }
        ],
        'APPLICATION_APPROVAL': [
          { value: 'CORPORATE_APPROVAL_1', label: 'Corporate Approval Level 1' },
          { value: 'CORPORATE_APPROVAL_2', label: 'Corporate Approval Level 2' },
          { value: 'CORPORATE_APPROVAL_3', label: 'Corporate Approval Level 3' },
          { value: 'CORPORATE_APPROVAL_4', label: 'Corporate Approval Level 4' }
        ]
      }
    };
    
    res.json(stageOptions);
  } catch (error) {
    console.error('Error fetching stage options:', error);
    res.status(500).json({ message: 'Failed to fetch stage options' });
  }
});
```

## Implementation Steps

### Step 1: Update Frontend Component
1. Locate the Scorecard Management component file
2. Replace the single "Category" dropdown with Stage and Sub-Stage dropdowns
3. Add the stage/sub-stage mapping logic
4. Update form handling

### Step 2: Update Backend API
1. Add the stage-options endpoint
2. Update the scorecard template model if needed
3. Test the API endpoint

### Step 3: Test the Changes
1. Navigate to Scorecard Management
2. Verify "Stage" and "Sub-Stage" dropdowns appear
3. Test that selecting a stage populates sub-stage options
4. Create a test template to verify functionality

### Step 4: Verify Integration
1. Check that created templates appear in the library
2. Test that application scorecards can use the new templates
3. Verify the complete workflow works

## Expected Result

After implementing these changes:
1. ✅ "Category" dropdown becomes "Stage" dropdown
2. ✅ New "Sub-Stage" dropdown appears
3. ✅ Sub-stages populate based on selected stage
4. ✅ Templates can be created for specific stage/sub-stage combinations
5. ✅ Application scorecards work with the new template structure

This fix addresses the frontend interface issues and provides the proper stage/sub-stage selection functionality for scorecard template creation.
