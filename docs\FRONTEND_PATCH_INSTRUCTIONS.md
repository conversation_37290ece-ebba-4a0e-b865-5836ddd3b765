# Frontend Patch Instructions - Scorecard Management Fix

## 🚨 CRITICAL: The frontend application files need to be updated

The backend API has been updated, but the frontend interface still shows the old "Category" dropdown. Here's how to fix it:

## 📁 LOCATE THE FRONTEND FILES

The Scorecard Management interface is likely in one of these locations:
- `frontend/src/components/ScorecardManagement.jsx` (React)
- `frontend/src/views/ScorecardManagement.vue` (Vue)
- `frontend/src/app/scorecard-management/` (Angular)
- `public/js/scorecard-management.js` (Vanilla JS)
- `templates/scorecard-management.html` (Server-side templates)

## 🔍 FIND THE CURRENT CODE

Look for this pattern in your frontend files:

```html
<!-- CURRENT BROKEN CODE - FIND THIS -->
<select name="category" id="category">
  <option value="">Select Category</option>
  <option value="onboarding">Onboarding</option>
  <option value="business-case-review">Business Case Review</option>
  <option value="due-diligence">Due Diligence</option>
  <option value="assessment-report">Assessment Report</option>
  <option value="application-approval">Application Approval</option>
</select>
```

OR this pattern:

```javascript
// CURRENT BROKEN CODE - FIND THIS
<FormControl>
  <InputLabel>Category*</InputLabel>
  <Select name="category" value={category} onChange={handleCategoryChange}>
    <MenuItem value="">Select Category</MenuItem>
    <MenuItem value="onboarding">Onboarding</MenuItem>
    <MenuItem value="business-case-review">Business Case Review</MenuItem>
    // ... more options
  </Select>
</FormControl>
```

## 🛠️ REPLACE WITH THIS CODE

### For HTML/JavaScript Applications:

```html
<!-- REPLACE THE SINGLE CATEGORY DROPDOWN WITH THESE TWO DROPDOWNS -->
<div class="form-row">
  <div class="form-group">
    <label for="mainStage">Stage*</label>
    <select id="mainStage" name="mainStage" required onchange="updateSubStages()">
      <option value="">Select Stage</option>
      <option value="ONBOARDING">Onboarding</option>
      <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
      <option value="DUE_DILIGENCE">Due Diligence</option>
      <option value="ASSESSMENT_REPORT">Assessment Report</option>
      <option value="APPLICATION_APPROVAL">Application Approval</option>
    </select>
  </div>
  
  <div class="form-group">
    <label for="subStage">Sub-Stage*</label>
    <select id="subStage" name="subStage" required disabled>
      <option value="">Select Sub-Stage</option>
    </select>
  </div>
</div>

<script>
// ADD THIS JAVASCRIPT CODE
const stageSubStageMap = {
  'ONBOARDING': [
    { value: 'beneficiary-registration', label: 'Beneficiary Registration' },
    { value: 'pre-screening', label: 'Pre-Screening' }
  ],
  'BUSINESS_CASE_REVIEW': [
    { value: 'document-collection', label: 'Document Collection' },
    { value: 'desktop-analysis', label: 'Desktop Analysis' },
    { value: 'data-validation', label: 'Data Validation' }
  ],
  'DUE_DILIGENCE': [
    { value: 'sme-interview', label: 'SME Interview' },
    { value: 'site-visit', label: 'Site Visit' }
  ],
  'ASSESSMENT_REPORT': [
    { value: 'report-completion', label: 'Report Completion' },
    { value: 'report-quality-check', label: 'Report Quality Check' },
    { value: 'report-review', label: 'Report Review' }
  ],
  'APPLICATION_APPROVAL': [
    { value: 'committee-review', label: 'Committee Review' },
    { value: 'final-decision', label: 'Final Decision' },
    { value: 'approval-documentation', label: 'Approval Documentation' },
    { value: 'corporate-approval-1', label: 'Corporate Approval Level 1' },
    { value: 'corporate-approval-2', label: 'Corporate Approval Level 2' },
    { value: 'corporate-approval-3', label: 'Corporate Approval Level 3' },
    { value: 'corporate-approval-4', label: 'Corporate Approval Level 4' }
  ]
};

function updateSubStages() {
  const mainStageSelect = document.getElementById('mainStage');
  const subStageSelect = document.getElementById('subStage');
  const selectedStage = mainStageSelect.value;
  
  // Clear sub-stage options
  subStageSelect.innerHTML = '<option value="">Select Sub-Stage</option>';
  
  if (selectedStage && stageSubStageMap[selectedStage]) {
    // Enable sub-stage dropdown
    subStageSelect.disabled = false;
    
    // Populate sub-stage options
    stageSubStageMap[selectedStage].forEach(subStage => {
      const option = document.createElement('option');
      option.value = subStage.value;
      option.textContent = subStage.label;
      subStageSelect.appendChild(option);
    });
  } else {
    // Disable sub-stage dropdown
    subStageSelect.disabled = true;
  }
}
</script>
```

### For React Applications:

```jsx
// REPLACE THE CATEGORY DROPDOWN WITH THIS CODE
import React, { useState, useEffect } from 'react';

const [formData, setFormData] = useState({
  templateName: '',
  mainStage: '',
  subStage: '',
  description: '',
  passingScore: 70
});

const stageSubStageMap = {
  'ONBOARDING': [
    { value: 'beneficiary-registration', label: 'Beneficiary Registration' },
    { value: 'pre-screening', label: 'Pre-Screening' }
  ],
  'BUSINESS_CASE_REVIEW': [
    { value: 'document-collection', label: 'Document Collection' },
    { value: 'desktop-analysis', label: 'Desktop Analysis' },
    { value: 'data-validation', label: 'Data Validation' }
  ],
  'DUE_DILIGENCE': [
    { value: 'sme-interview', label: 'SME Interview' },
    { value: 'site-visit', label: 'Site Visit' }
  ],
  'ASSESSMENT_REPORT': [
    { value: 'report-completion', label: 'Report Completion' },
    { value: 'report-quality-check', label: 'Report Quality Check' },
    { value: 'report-review', label: 'Report Review' }
  ],
  'APPLICATION_APPROVAL': [
    { value: 'committee-review', label: 'Committee Review' },
    { value: 'final-decision', label: 'Final Decision' },
    { value: 'approval-documentation', label: 'Approval Documentation' },
    { value: 'corporate-approval-1', label: 'Corporate Approval Level 1' },
    { value: 'corporate-approval-2', label: 'Corporate Approval Level 2' },
    { value: 'corporate-approval-3', label: 'Corporate Approval Level 3' },
    { value: 'corporate-approval-4', label: 'Corporate Approval Level 4' }
  ]
};

const handleStageChange = (e) => {
  const mainStage = e.target.value;
  setFormData({
    ...formData,
    mainStage: mainStage,
    subStage: '' // Reset sub-stage when main stage changes
  });
};

const handleSubStageChange = (e) => {
  setFormData({
    ...formData,
    subStage: e.target.value
  });
};

// REPLACE THE CATEGORY DROPDOWN WITH THIS JSX
<div className="form-row">
  <div className="form-group">
    <label htmlFor="mainStage">Stage*</label>
    <select 
      id="mainStage" 
      name="mainStage" 
      value={formData.mainStage} 
      onChange={handleStageChange}
      required
    >
      <option value="">Select Stage</option>
      <option value="ONBOARDING">Onboarding</option>
      <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
      <option value="DUE_DILIGENCE">Due Diligence</option>
      <option value="ASSESSMENT_REPORT">Assessment Report</option>
      <option value="APPLICATION_APPROVAL">Application Approval</option>
    </select>
  </div>
  
  <div className="form-group">
    <label htmlFor="subStage">Sub-Stage*</label>
    <select 
      id="subStage" 
      name="subStage" 
      value={formData.subStage} 
      onChange={handleSubStageChange}
      required
      disabled={!formData.mainStage}
    >
      <option value="">Select Sub-Stage</option>
      {formData.mainStage && stageSubStageMap[formData.mainStage]?.map(subStage => (
        <option key={subStage.value} value={subStage.value}>
          {subStage.label}
        </option>
      ))}
    </select>
  </div>
</div>
```

### For Vue Applications:

```vue
<!-- REPLACE THE CATEGORY DROPDOWN WITH THIS CODE -->
<template>
  <div class="form-row">
    <div class="form-group">
      <label for="mainStage">Stage*</label>
      <select 
        id="mainStage" 
        v-model="formData.mainStage" 
        @change="onStageChange"
        required
      >
        <option value="">Select Stage</option>
        <option value="ONBOARDING">Onboarding</option>
        <option value="BUSINESS_CASE_REVIEW">Business Case Review</option>
        <option value="DUE_DILIGENCE">Due Diligence</option>
        <option value="ASSESSMENT_REPORT">Assessment Report</option>
        <option value="APPLICATION_APPROVAL">Application Approval</option>
      </select>
    </div>
    
    <div class="form-group">
      <label for="subStage">Sub-Stage*</label>
      <select 
        id="subStage" 
        v-model="formData.subStage" 
        :disabled="!formData.mainStage"
        required
      >
        <option value="">Select Sub-Stage</option>
        <option 
          v-for="subStage in availableSubStages" 
          :key="subStage.value" 
          :value="subStage.value"
        >
          {{ subStage.label }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        templateName: '',
        mainStage: '',
        subStage: '',
        description: '',
        passingScore: 70
      },
      stageSubStageMap: {
        'ONBOARDING': [
          { value: 'beneficiary-registration', label: 'Beneficiary Registration' },
          { value: 'pre-screening', label: 'Pre-Screening' }
        ],
        'BUSINESS_CASE_REVIEW': [
          { value: 'document-collection', label: 'Document Collection' },
          { value: 'desktop-analysis', label: 'Desktop Analysis' },
          { value: 'data-validation', label: 'Data Validation' }
        ],
        'DUE_DILIGENCE': [
          { value: 'sme-interview', label: 'SME Interview' },
          { value: 'site-visit', label: 'Site Visit' }
        ],
        'ASSESSMENT_REPORT': [
          { value: 'report-completion', label: 'Report Completion' },
          { value: 'report-quality-check', label: 'Report Quality Check' },
          { value: 'report-review', label: 'Report Review' }
        ],
        'APPLICATION_APPROVAL': [
          { value: 'committee-review', label: 'Committee Review' },
          { value: 'final-decision', label: 'Final Decision' },
          { value: 'approval-documentation', label: 'Approval Documentation' },
          { value: 'corporate-approval-1', label: 'Corporate Approval Level 1' },
          { value: 'corporate-approval-2', label: 'Corporate Approval Level 2' },
          { value: 'corporate-approval-3', label: 'Corporate Approval Level 3' },
          { value: 'corporate-approval-4', label: 'Corporate Approval Level 4' }
        ]
      }
    }
  },
  computed: {
    availableSubStages() {
      return this.stageSubStageMap[this.formData.mainStage] || [];
    }
  },
  methods: {
    onStageChange() {
      this.formData.subStage = ''; // Reset sub-stage when main stage changes
    }
  }
}
</script>
```

## 🎨 ADD CSS STYLES

Add this CSS to make the dropdowns look good:

```css
.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group select:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
```

## 🔄 UPDATE FORM SUBMISSION

Update your form submission code to use both mainStage and subStage:

```javascript
// UPDATE YOUR FORM SUBMISSION CODE
const templateData = {
  name: formData.templateName,
  mainStage: formData.mainStage,
  subStage: formData.subStage,
  category: formData.subStage, // Use subStage as category for backward compatibility
  description: formData.description,
  passingScore: formData.passingScore,
  // ... other fields
};

// Send to API
fetch('/api/v1/scorecard-templates', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(templateData)
});
```

## 🚀 DEPLOYMENT STEPS

1. **Find your frontend scorecard management file**
2. **Locate the category dropdown code**
3. **Replace it with the stage/sub-stage dropdowns above**
4. **Add the CSS styles**
5. **Update form submission logic**
6. **Test the interface**
7. **Deploy the changes**

## ✅ VERIFICATION

After making these changes, you should see:
- ✅ "Stage" dropdown instead of "Category"
- ✅ "Sub-Stage" dropdown that populates based on stage selection
- ✅ Proper form validation
- ✅ Template creation working with stage/sub-stage data

The backend API is already updated and ready to receive the new data structure.
