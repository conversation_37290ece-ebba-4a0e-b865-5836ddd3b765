const mongoose = require('mongoose');

const riskFactorSchema = new mongoose.Schema({
  factor: {
    type: String,
    required: true,
    enum: ['CREDIT_SCORE', 'DEBT_TO_INCOME', 'TIME_IN_BUSINESS', 'INDUSTRY_RISK', 'COLLATERAL_COVERAGE', 'REVENUE_STABILITY', 'CASH_FLOW_RATIO']
  },
  weight: {
    type: Number,
    required: true,
    min: 0,
    max: 1
  },
  ranges: [{
    min: Number,
    max: Number,
    adjustment: Number // Interest rate adjustment in percentage points
  }]
});

const pricingRuleSchema = new mongoose.Schema({
  ruleId: {
    type: String,
    required: true,
    unique: true
  },
  ruleName: {
    type: String,
    required: true
  },
  productId: {
    type: String,
    required: true,
    ref: 'LoanProduct'
  },
  riskFactors: [riskFactorSchema],
  baseRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  marginRange: {
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 10
    }
  },
  fees: {
    originationFeePercent: {
      type: Number,
      default: 0,
      min: 0,
      max: 10
    },
    processingFeeFixed: {
      type: Number,
      default: 0,
      min: 0
    },
    lateFeePercent: {
      type: Number,
      default: 0,
      min: 0,
      max: 10
    },
    prepaymentPenaltyPercent: {
      type: Number,
      default: 0,
      min: 0,
      max: 10
    }
  },
  termAdjustments: [{
    minTerm: Number,
    maxTerm: Number,
    adjustment: Number // Interest rate adjustment
  }],
  amountAdjustments: [{
    minAmount: Number,
    maxAmount: Number,
    adjustment: Number // Interest rate adjustment
  }],
  active: {
    type: Boolean,
    default: true
  },
  effectiveDate: {
    type: Date,
    default: Date.now
  },
  expiryDate: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Generate rule ID if not provided
pricingRuleSchema.pre('save', async function(next) {
  if (!this.ruleId) {
    const count = await this.constructor.countDocuments();
    this.ruleId = `RULE-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Validate that weights sum to 1
pricingRuleSchema.pre('save', function(next) {
  if (this.riskFactors && this.riskFactors.length > 0) {
    const totalWeight = this.riskFactors.reduce((sum, factor) => sum + factor.weight, 0);
    if (Math.abs(totalWeight - 1) > 0.001) {
      next(new Error('Risk factor weights must sum to 1'));
    }
  }
  next();
});

const PricingRule = mongoose.model('PricingRule', pricingRuleSchema);

module.exports = PricingRule;