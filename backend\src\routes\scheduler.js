const express = require('express');
const router = express.Router();
const Interview = require('../models/interview');
const SiteVisit = require('../models/site-visit');
const Application = require('../models/application');

/**
 * Get all scheduled events within a date range
 * GET /api/scheduler/events
 */
router.get('/events', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Get interviews within date range
    const interviews = await Interview.find({
      scheduledDate: { $gte: start, $lte: end }
    }).lean();
    
    // Get site visits within date range
    const siteVisits = await SiteVisit.find({
      scheduledDate: { $gte: start, $lte: end }
    }).lean();
    
    // Format interviews as events
    const interviewEvents = interviews.map(interview => ({
      id: interview._id.toString(),
      title: interview.title || 'Interview',
      start: interview.scheduledDate,
      end: new Date(new Date(interview.scheduledDate).getTime() + (interview.duration || 60) * 60000),
      type: 'interview',
      applicationId: interview.applicationId,
      status: interview.status
    }));
    
    // Format site visits as events
    const siteVisitEvents = siteVisits.map(siteVisit => ({
      id: siteVisit._id.toString(),
      title: siteVisit.title || 'Site Visit',
      start: siteVisit.scheduledDate,
      end: new Date(new Date(siteVisit.scheduledDate).getTime() + (siteVisit.duration || 120) * 60000),
      type: 'siteVisit',
      applicationId: siteVisit.applicationId,
      status: siteVisit.status,
      location: siteVisit.location?.address
    }));
    
    // Combine events
    const events = [...interviewEvents, ...siteVisitEvents];
    
    res.json(events);
  } catch (error) {
    console.error('Error fetching scheduled events:', error);
    res.status(500).json({ message: 'Error fetching scheduled events', error: error.message });
  }
});

/**
 * Get events for a specific application
 * GET /api/scheduler/application/:applicationId/events
 */
router.get('/application/:applicationId/events', async (req, res) => {
  try {
    const { applicationId } = req.params;
    
    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }
    
    // Get interviews for application
    const interviews = await Interview.find({ applicationId }).lean();
    
    // Get site visits for application
    const siteVisits = await SiteVisit.find({ applicationId }).lean();
    
    // Format interviews as events
    const interviewEvents = interviews.map(interview => ({
      id: interview._id.toString(),
      title: interview.title || 'Interview',
      start: interview.scheduledDate,
      end: new Date(new Date(interview.scheduledDate).getTime() + (interview.duration || 60) * 60000),
      type: 'interview',
      applicationId: interview.applicationId,
      status: interview.status
    }));
    
    // Format site visits as events
    const siteVisitEvents = siteVisits.map(siteVisit => ({
      id: siteVisit._id.toString(),
      title: siteVisit.title || 'Site Visit',
      start: siteVisit.scheduledDate,
      end: new Date(new Date(siteVisit.scheduledDate).getTime() + (siteVisit.duration || 120) * 60000),
      type: 'siteVisit',
      applicationId: siteVisit.applicationId,
      status: siteVisit.status,
      location: siteVisit.location?.address
    }));
    
    // Combine events
    const events = [...interviewEvents, ...siteVisitEvents];
    
    res.json(events);
  } catch (error) {
    console.error('Error fetching application events:', error);
    res.status(500).json({ message: 'Error fetching application events', error: error.message });
  }
});

/**
 * Get upcoming events
 * GET /api/scheduler/upcoming
 */
router.get('/upcoming', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    const now = new Date();
    const future = new Date();
    future.setDate(future.getDate() + parseInt(days));
    
    // Get upcoming interviews
    const interviews = await Interview.find({
      scheduledDate: { $gte: now, $lte: future },
      status: 'SCHEDULED'
    }).lean();
    
    // Get upcoming site visits
    const siteVisits = await SiteVisit.find({
      scheduledDate: { $gte: now, $lte: future },
      status: 'SCHEDULED'
    }).lean();
    
    // Format interviews as events
    const interviewEvents = interviews.map(interview => ({
      id: interview._id.toString(),
      title: interview.title || 'Interview',
      start: interview.scheduledDate,
      end: new Date(new Date(interview.scheduledDate).getTime() + (interview.duration || 60) * 60000),
      type: 'interview',
      applicationId: interview.applicationId,
      status: interview.status
    }));
    
    // Format site visits as events
    const siteVisitEvents = siteVisits.map(siteVisit => ({
      id: siteVisit._id.toString(),
      title: siteVisit.title || 'Site Visit',
      start: siteVisit.scheduledDate,
      end: new Date(new Date(siteVisit.scheduledDate).getTime() + (siteVisit.duration || 120) * 60000),
      type: 'siteVisit',
      applicationId: siteVisit.applicationId,
      status: siteVisit.status,
      location: siteVisit.location?.address
    }));
    
    // Combine events
    const events = [...interviewEvents, ...siteVisitEvents];
    
    res.json(events);
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    res.status(500).json({ message: 'Error fetching upcoming events', error: error.message });
  }
});

/**
 * Schedule an interview
 * POST /api/scheduler/interview
 */
router.post('/interview', async (req, res) => {
  try {
    const interviewData = req.body;
    
    if (!interviewData.applicationId || !interviewData.scheduledDate) {
      return res.status(400).json({ message: 'Application ID and scheduled date are required' });
    }
    
    // Check if application exists
    const application = await Application.findById(interviewData.applicationId);
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Create interview
    const interview = new Interview({
      applicationId: interviewData.applicationId,
      title: interviewData.title || 'Interview',
      scheduledDate: new Date(interviewData.scheduledDate),
      duration: interviewData.duration || 60,
      type: interviewData.type || 'ONLINE',
      primaryInterviewer: interviewData.primaryInterviewer,
      interviewee: interviewData.interviewee || 'Applicant',
      status: 'SCHEDULED',
      description: interviewData.description
    });
    
    await interview.save();
    
    // Update application stage if needed
    if (application.currentSubStage !== 'SME_INTERVIEW') {
      application.currentMainStage = 'DUE_DILIGENCE';
      application.currentSubStage = 'SME_INTERVIEW';
      application.currentStageStatus = 'ACTIVE';
      await application.save();
    }
    
    res.status(201).json(interview);
  } catch (error) {
    console.error('Error scheduling interview:', error);
    res.status(500).json({ message: 'Error scheduling interview', error: error.message });
  }
});

/**
 * Schedule a site visit
 * POST /api/scheduler/site-visit
 */
router.post('/site-visit', async (req, res) => {
  try {
    const siteVisitData = req.body;
    
    if (!siteVisitData.applicationId || !siteVisitData.scheduledDate) {
      return res.status(400).json({ message: 'Application ID and scheduled date are required' });
    }
    
    // Check if application exists
    const application = await Application.findById(siteVisitData.applicationId);
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    // Create site visit
    const siteVisit = new SiteVisit({
      applicationId: siteVisitData.applicationId,
      title: siteVisitData.title || 'Site Visit',
      scheduledDate: new Date(siteVisitData.scheduledDate),
      duration: siteVisitData.duration || 120,
      location: siteVisitData.location || { address: 'TBD' },
      conductedBy: siteVisitData.conductedBy,
      status: 'SCHEDULED',
      description: siteVisitData.description
    });
    
    await siteVisit.save();
    
    // Update application stage if needed
    if (application.currentSubStage !== 'SITE_VISIT') {
      application.currentMainStage = 'DUE_DILIGENCE';
      application.currentSubStage = 'SITE_VISIT';
      application.currentStageStatus = 'ACTIVE';
      await application.save();
    }
    
    res.status(201).json(siteVisit);
  } catch (error) {
    console.error('Error scheduling site visit:', error);
    res.status(500).json({ message: 'Error scheduling site visit', error: error.message });
  }
});

/**
 * Reschedule a site visit
 * PUT /api/scheduler/site-visit/:siteVisitId
 */
router.put('/site-visit/:siteVisitId', async (req, res) => {
  try {
    const { siteVisitId } = req.params;
    const { scheduledDate, duration } = req.body;
    
    if (!scheduledDate) {
      return res.status(400).json({ message: 'Scheduled date is required' });
    }
    
    // Find site visit
    const siteVisit = await SiteVisit.findById(siteVisitId);
    if (!siteVisit) {
      return res.status(404).json({ message: 'Site visit not found' });
    }
    
    // Update site visit
    siteVisit.scheduledDate = new Date(scheduledDate);
    if (duration) {
      siteVisit.duration = duration;
    }
    
    await siteVisit.save();
    
    res.json(siteVisit);
  } catch (error) {
    console.error('Error rescheduling site visit:', error);
    res.status(500).json({ message: 'Error rescheduling site visit', error: error.message });
  }
});

/**
 * Cancel an interview
 * DELETE /api/scheduler/interview/:interviewId
 */
router.delete('/interview/:interviewId', async (req, res) => {
  try {
    const { interviewId } = req.params;
    
    // Find interview
    const interview = await Interview.findById(interviewId);
    if (!interview) {
      return res.status(404).json({ message: 'Interview not found' });
    }
    
    // Update interview status
    interview.status = 'CANCELLED';
    await interview.save();
    
    res.json(interview);
  } catch (error) {
    console.error('Error cancelling interview:', error);
    res.status(500).json({ message: 'Error cancelling interview', error: error.message });
  }
});

/**
 * Cancel a site visit
 * DELETE /api/scheduler/site-visit/:siteVisitId
 */
router.delete('/site-visit/:siteVisitId', async (req, res) => {
  try {
    const { siteVisitId } = req.params;
    
    // Find site visit
    const siteVisit = await SiteVisit.findById(siteVisitId);
    if (!siteVisit) {
      return res.status(404).json({ message: 'Site visit not found' });
    }
    
    // Update site visit status
    siteVisit.status = 'CANCELLED';
    await siteVisit.save();
    
    res.json(siteVisit);
  } catch (error) {
    console.error('Error cancelling site visit:', error);
    res.status(500).json({ message: 'Error cancelling site visit', error: error.message });
  }
});

/**
 * Check for scheduling conflicts
 * POST /api/scheduler/check-conflicts
 */
router.post('/check-conflicts', async (req, res) => {
  try {
    const { scheduledDate, duration, participants } = req.body;
    
    if (!scheduledDate || !duration || !participants || !Array.isArray(participants)) {
      return res.status(400).json({ message: 'Scheduled date, duration, and participants are required' });
    }
    
    const start = new Date(scheduledDate);
    const end = new Date(new Date(scheduledDate).getTime() + duration * 60000);
    
    // Find interviews with conflicts
    const interviews = await Interview.find({
      status: 'SCHEDULED',
      $or: [
        // Interview starts during the proposed time
        {
          scheduledDate: { $gte: start, $lt: end }
        },
        // Interview ends during the proposed time
        {
          $expr: {
            $and: [
              { $gte: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, start.getTime()] },
              { $lt: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, end.getTime()] }
            ]
          }
        },
        // Interview spans the entire proposed time
        {
          scheduledDate: { $lt: start },
          $expr: {
            $gte: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, end.getTime()]
          }
        }
      ],
      $or: participants.map(participant => ({
        $or: [
          { primaryInterviewer: participant },
          { interviewers: participant }
        ]
      }))
    }).lean();
    
    // Find site visits with conflicts
    const siteVisits = await SiteVisit.find({
      status: 'SCHEDULED',
      $or: [
        // Site visit starts during the proposed time
        {
          scheduledDate: { $gte: start, $lt: end }
        },
        // Site visit ends during the proposed time
        {
          $expr: {
            $and: [
              { $gte: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, start.getTime()] },
              { $lt: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, end.getTime()] }
            ]
          }
        },
        // Site visit spans the entire proposed time
        {
          scheduledDate: { $lt: start },
          $expr: {
            $gte: [{ $add: ['$scheduledDate', { $multiply: ['$duration', 60000] }] }, end.getTime()]
          }
        }
      ],
      conductedBy: { $in: participants }
    }).lean();
    
    // Format interviews as events
    const interviewEvents = interviews.map(interview => ({
      id: interview._id.toString(),
      title: interview.title || 'Interview',
      start: interview.scheduledDate,
      end: new Date(new Date(interview.scheduledDate).getTime() + (interview.duration || 60) * 60000),
      type: 'interview',
      applicationId: interview.applicationId,
      status: interview.status
    }));
    
    // Format site visits as events
    const siteVisitEvents = siteVisits.map(siteVisit => ({
      id: siteVisit._id.toString(),
      title: siteVisit.title || 'Site Visit',
      start: siteVisit.scheduledDate,
      end: new Date(new Date(siteVisit.scheduledDate).getTime() + (siteVisit.duration || 120) * 60000),
      type: 'siteVisit',
      applicationId: siteVisit.applicationId,
      status: siteVisit.status,
      location: siteVisit.location?.address
    }));
    
    // Combine conflicts
    const conflicts = [...interviewEvents, ...siteVisitEvents];
    
    res.json({
      hasConflicts: conflicts.length > 0,
      conflicts
    });
  } catch (error) {
    console.error('Error checking conflicts:', error);
    res.status(500).json({ message: 'Error checking conflicts', error: error.message });
  }
});

module.exports = router;
