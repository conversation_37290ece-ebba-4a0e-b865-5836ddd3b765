const express = require('express');
const router = express.Router();
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const LoanManagementService = require('../services/loan-management.service');
const LoanProduct = require('../models/loan-product');
const PricingRule = require('../models/pricing-rule');
const LoanOffer = require('../models/loan-offer');
const Loan = require('../models/loan');

// Loan Products Routes
router.get('/products', authenticateToken, async (req, res) => {
  try {
    const { active, productType } = req.query;
    const filters = {};
    
    if (active !== undefined) filters.active = active === 'true';
    if (productType) filters.productType = productType;
    
    const result = await LoanManagementService.getLoanProducts(filters);
    
    if (result.success) {
      res.json(result.products);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error fetching loan products:', error);
    res.status(500).json({ message: 'Failed to fetch loan products' });
  }
});

router.get('/products/:productId', authenticateToken, async (req, res) => {
  try {
    const product = await LoanProduct.findOne({ productId: req.params.productId })
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    if (!product) {
      return res.status(404).json({ message: 'Loan product not found' });
    }
    
    res.json(product);
  } catch (error) {
    console.error('Error fetching loan product:', error);
    res.status(500).json({ message: 'Failed to fetch loan product' });
  }
});

router.post('/products', authenticateToken, authorizeRoles(['admin', 'manager', 'loan-officer']), async (req, res) => {
  try {
    const result = await LoanManagementService.createLoanProduct(req.body, req.user.id);
    
    if (result.success) {
      res.status(201).json(result.product);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error creating loan product:', error);
    res.status(500).json({ message: 'Failed to create loan product' });
  }
});

router.put('/products/:productId', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const result = await LoanManagementService.updateLoanProduct(
      req.params.productId,
      req.body,
      req.user.id
    );
    
    if (result.success) {
      res.json(result.product);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error updating loan product:', error);
    res.status(500).json({ message: 'Failed to update loan product' });
  }
});

// Pricing Rules Routes
router.get('/pricing-rules', authenticateToken, async (req, res) => {
  try {
    const { productId, active } = req.query;
    const filters = {};
    
    if (productId) filters.productId = productId;
    if (active !== undefined) filters.active = active === 'true';
    
    const rules = await PricingRule.find(filters)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    res.json(rules);
  } catch (error) {
    console.error('Error fetching pricing rules:', error);
    res.status(500).json({ message: 'Failed to fetch pricing rules' });
  }
});

router.post('/pricing-rules', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const result = await LoanManagementService.createPricingRule(req.body, req.user.id);
    
    if (result.success) {
      res.status(201).json(result.rule);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error creating pricing rule:', error);
    res.status(500).json({ message: 'Failed to create pricing rule' });
  }
});

// Loan Pricing Calculation
router.post('/calculate-pricing', authenticateToken, async (req, res) => {
  try {
    const { applicationId, productId, requestedAmount, requestedTerm } = req.body;
    
    if (!applicationId || !productId || !requestedAmount || !requestedTerm) {
      return res.status(400).json({ 
        message: 'Missing required fields: applicationId, productId, requestedAmount, requestedTerm' 
      });
    }
    
    const result = await LoanManagementService.calculateLoanPricing(
      applicationId,
      productId,
      requestedAmount,
      requestedTerm
    );
    
    if (result.success) {
      res.json(result.pricing);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error calculating loan pricing:', error);
    res.status(500).json({ message: 'Failed to calculate loan pricing' });
  }
});

// Loan Offers Routes
router.get('/offers', authenticateToken, async (req, res) => {
  try {
    const { applicationId, status, validOnly } = req.query;
    const filters = {};
    
    if (applicationId) filters.applicationId = applicationId;
    if (status) filters.status = status;
    if (validOnly === 'true') {
      filters.validUntil = { $gte: new Date() };
      filters.status = { $nin: ['REJECTED', 'EXPIRED', 'WITHDRAWN'] };
    }
    
    const offers = await LoanOffer.find(filters)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 });
    
    res.json(offers);
  } catch (error) {
    console.error('Error fetching loan offers:', error);
    res.status(500).json({ message: 'Failed to fetch loan offers' });
  }
});

router.get('/offers/:offerId', authenticateToken, async (req, res) => {
  try {
    const offer = await LoanOffer.findOne({ offerId: req.params.offerId })
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    if (!offer) {
      return res.status(404).json({ message: 'Loan offer not found' });
    }
    
    res.json(offer);
  } catch (error) {
    console.error('Error fetching loan offer:', error);
    res.status(500).json({ message: 'Failed to fetch loan offer' });
  }
});

router.post('/offers', authenticateToken, authorizeRoles(['admin', 'manager', 'loan-officer']), async (req, res) => {
  try {
    const result = await LoanManagementService.generateLoanOffer(req.body, req.user.id);
    
    if (result.success) {
      res.status(201).json(result.offer);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error generating loan offer:', error);
    res.status(500).json({ message: 'Failed to generate loan offer' });
  }
});

router.put('/offers/:offerId/status', authenticateToken, async (req, res) => {
  try {
    const { status, reason } = req.body;
    
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }
    
    const result = await LoanManagementService.updateLoanOfferStatus(
      req.params.offerId,
      status,
      req.user.id,
      reason
    );
    
    if (result.success) {
      res.json(result.offer);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error updating loan offer status:', error);
    res.status(500).json({ message: 'Failed to update loan offer status' });
  }
});

// Loan Management Routes
router.get('/loans', authenticateToken, async (req, res) => {
  try {
    const { status, borrowerId, applicationId } = req.query;
    const filters = {};
    
    if (status) filters.status = status;
    if (borrowerId) filters.borrowerId = borrowerId;
    if (applicationId) filters.applicationId = applicationId;
    
    const loans = await Loan.find(filters)
      .populate('borrowerId', 'name email')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 });
    
    res.json(loans);
  } catch (error) {
    console.error('Error fetching loans:', error);
    res.status(500).json({ message: 'Failed to fetch loans' });
  }
});

router.get('/loans/:loanNumber', authenticateToken, async (req, res) => {
  try {
    const loan = await Loan.findOne({ loanNumber: req.params.loanNumber })
      .populate('borrowerId', 'name email')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');
    
    if (!loan) {
      return res.status(404).json({ message: 'Loan not found' });
    }
    
    res.json(loan);
  } catch (error) {
    console.error('Error fetching loan:', error);
    res.status(500).json({ message: 'Failed to fetch loan' });
  }
});

router.post('/loans/:loanNumber/disburse', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const result = await LoanManagementService.disburseLoan(
      req.params.loanNumber,
      req.body,
      req.user.id
    );
    
    if (result.success) {
      res.json(result.loan);
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error disbursing loan:', error);
    res.status(500).json({ message: 'Failed to disburse loan' });
  }
});

router.post('/loans/:loanNumber/payments', authenticateToken, async (req, res) => {
  try {
    const result = await LoanManagementService.recordPayment(
      req.params.loanNumber,
      req.body,
      req.user.id
    );
    
    if (result.success) {
      res.json({
        loan: result.loan,
        payment: result.payment
      });
    } else {
      res.status(400).json({ message: result.error });
    }
  } catch (error) {
    console.error('Error recording payment:', error);
    res.status(500).json({ message: 'Failed to record payment' });
  }
});

// Loan Analytics Routes
router.get('/analytics/portfolio-summary', authenticateToken, async (req, res) => {
  try {
    const loans = await Loan.find({});
    
    const summary = {
      totalLoans: loans.length,
      activeLoans: loans.filter(l => l.status === 'ACTIVE').length,
      totalDisbursed: loans.reduce((sum, l) => sum + (l.disbursedAmount || 0), 0),
      totalOutstanding: loans.reduce((sum, l) => sum + (l.totalOutstanding || 0), 0),
      totalCollected: loans.reduce((sum, l) => sum + l.totalPaidPrincipal + l.totalPaidInterest + l.totalPaidFees, 0),
      averageInterestRate: loans.length > 0 ? 
        loans.reduce((sum, l) => sum + l.interestRate, 0) / loans.length : 0,
      defaultRate: loans.length > 0 ?
        (loans.filter(l => l.status === 'DEFAULTED').length / loans.length) * 100 : 0
    };
    
    res.json(summary);
  } catch (error) {
    console.error('Error fetching portfolio summary:', error);
    res.status(500).json({ message: 'Failed to fetch portfolio summary' });
  }
});

router.get('/analytics/payment-schedule', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const dateFilter = {};
    
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);
    
    const loans = await Loan.find({ status: 'ACTIVE' });
    
    const upcomingPayments = [];
    loans.forEach(loan => {
      loan.payments.forEach(payment => {
        if (payment.status === 'SCHEDULED' || payment.status === 'PENDING') {
          if (!startDate || !endDate || 
              (payment.dueDate >= dateFilter.$gte && payment.dueDate <= dateFilter.$lte)) {
            upcomingPayments.push({
              loanNumber: loan.loanNumber,
              paymentId: payment.paymentId,
              dueDate: payment.dueDate,
              amount: payment.totalAmount,
              status: payment.status
            });
          }
        }
      });
    });
    
    upcomingPayments.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));
    
    res.json(upcomingPayments);
  } catch (error) {
    console.error('Error fetching payment schedule:', error);
    res.status(500).json({ message: 'Failed to fetch payment schedule' });
  }
});

module.exports = router;