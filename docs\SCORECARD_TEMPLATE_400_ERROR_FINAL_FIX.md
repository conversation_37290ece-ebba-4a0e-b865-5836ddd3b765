# Scorecard Template 400 Error - Root Cause and Fix

## Root Cause Identified

The 400 Bad Request error is likely caused by one or more of these issues:

1. **Category Enum Validation**: The backend model has strict enum validation for the `category` field
2. **Funding Programmes Format**: The `fundingProgrammes` field expects MongoDB ObjectIds, but the frontend might be sending strings
3. **Missing Required Fields**: Some fields might not be properly set during data transformation

## Comprehensive Fix

### 1. Update Frontend Template Service to Handle ObjectIds

**File:** `frontend/src/app/Components/scorecard-management/services/scorecard-template.service.ts`

```typescript
createTemplate(template: ScorecardTemplate): Observable<ScorecardTemplate> {
  // ... existing logging ...
  
  // Ensure fundingProgrammes are valid ObjectIds or empty array
  const fundingProgrammes = template.fundingProgrammes || [];
  const validProgrammes = fundingProgrammes.filter(id => {
    // Check if it's a valid MongoDB ObjectId format (24 hex characters)
    return typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id);
  });
  
  const templateData = {
    ...template,
    id: undefined, // Remove id for new templates - let MongoDB generate it
    fundingProgrammes: validProgrammes,
    version: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 0,
    allowCustomCriteria: template.allowCustomCriteria ?? true,
    requireAllCriteria: template.requireAllCriteria ?? false
  };
  
  // Remove any undefined or null fields
  Object.keys(templateData).forEach(key => {
    if (templateData[key] === undefined || templateData[key] === null) {
      delete templateData[key];
    }
  });
  
  // ... rest of the method
}
```

### 2. Update Template Form Component

**File:** `frontend/src/app/Components/scorecard-management/template-form/template-form.component.ts`

Update the `buildTemplateData()` method:

```typescript
private buildTemplateData(): ScorecardTemplate {
  const formValue = this.templateForm.value;
  
  // ... existing logging ...
  
  // Ensure category matches backend enum exactly
  let category = formValue.category || formValue.subStage;
  
  // Map frontend values to backend enum values if needed
  const categoryMapping: { [key: string]: string } = {
    'pre-screening': 'pre-screening',
    'beneficiary-registration': 'beneficiary-registration',
    // Add other mappings as needed
  };
  
  if (category && categoryMapping[category]) {
    category = categoryMapping[category];
  }
  
  const templateData: any = {
    // Don't include id for new templates
    name: formValue.name,
    description: formValue.description || '',
    category: category,
    templateScope: formValue.templateScope,
    fundingProgrammes: formValue.templateScope === 'global' ? [] : (formValue.fundingProgrammes || []),
    version: this.template?.version || 1,
    isActive: true,
    allowCustomCriteria: formValue.allowCustomCriteria ?? true,
    requireAllCriteria: formValue.requireAllCriteria ?? false,
    passingScore: formValue.passingScore || null,
    criteria: formValue.criteria.map((criterion: any, index: number) => ({
      ...criterion,
      order: index + 1
    })),
    createdAt: this.template?.createdAt || new Date(),
    updatedAt: new Date(),
    usageCount: this.template?.usageCount || 0
  };
  
  // ... rest of the method
}
```

### 3. Update Mock Funding Programmes

Since the backend expects ObjectIds, update the mock funding programmes to use valid ObjectId format:

**File:** `frontend/src/app/Components/scorecard-management/services/scorecard-template.service.ts`

```typescript
getMockFundingProgrammes(): Observable<FundingProgramme[]> {
  const mockProgrammes: FundingProgramme[] = [
    {
      id: '507f1f77bcf86cd799439011', // Valid 24-char hex ObjectId
      name: 'Skills Development',
      // ... rest of the object
    },
    {
      id: '507f1f77bcf86cd799439012', // Valid 24-char hex ObjectId
      name: 'Tech Innovation',
      // ... rest of the object
    },
    {
      id: '507f1f77bcf86cd799439013', // Valid 24-char hex ObjectId
      name: 'Green Energy',
      // ... rest of the object
    }
  ];
  
  return of(mockProgrammes);
}
```

### 4. Backend Route Enhancement

Add better error handling to provide more specific error messages:

**File:** `backend/src/routes/scorecard-templates.js`

```javascript
router.post('/', async (req, res) => {
  // ... existing logging ...
  
  try {
    const templateData = req.body;
    
    // Remove id field for new templates
    if (templateData.id === '' || templateData.id === null) {
      delete templateData.id;
    }
    
    // Validate fundingProgrammes are valid ObjectIds
    if (templateData.fundingProgrammes && Array.isArray(templateData.fundingProgrammes)) {
      const invalidIds = templateData.fundingProgrammes.filter(id => {
        return !mongoose.Types.ObjectId.isValid(id);
      });
      
      if (invalidIds.length > 0) {
        console.log('[DEBUG] Invalid funding programme IDs:', invalidIds);
        return res.status(400).json({
          error: 'Validation error',
          message: 'Invalid funding programme IDs',
          details: {
            invalidIds: invalidIds
          }
        });
      }
    }
    
    // ... rest of the route handler
  } catch (err) {
    // Enhanced error handling
    console.error('[DEBUG] ========== ERROR creating template ==========');
    console.error('[DEBUG] Error name:', err.name);
    console.error('[DEBUG] Error message:', err.message);
    
    if (err.name === 'ValidationError') {
      // Extract specific field errors
      const fieldErrors = {};
      if (err.errors) {
        Object.keys(err.errors).forEach(field => {
          fieldErrors[field] = err.errors[field].message;
        });
      }
      
      return res.status(400).json({ 
        error: 'Validation error', 
        message: err.message,
        fieldErrors: fieldErrors
      });
    }
    
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});
```

## Testing the Fix

1. **Clear browser cache** to ensure no stale data
2. **Restart both servers**
3. **Test with the debug logging** enabled
4. **Check the console** for the exact values being sent
5. **Verify in Network tab** that the payload matches expected format

## Expected Payload Format

```json
{
  "name": "Test Template",
  "description": "Test description",
  "category": "pre-screening",
  "templateScope": "programme-specific",
  "fundingProgrammes": ["507f1f77bcf86cd799439011"],
  "applicableStages": ["ONBOARDING"],
  "applicableSubstages": ["pre-screening"],
  "criteria": [
    {
      "id": "criterion_123",
      "name": "Test Criterion",
      "weight": 100,
      "maxScore": 10,
      "scoreType": "numeric",
      "required": false,
      "order": 1
    }
  ],
  "allowCustomCriteria": true,
  "requireAllCriteria": false,
  "version": 1,
  "isActive": true,
  "usageCount": 0
}
```

## Key Points

1. Don't send `id` field for new templates
2. Ensure `fundingProgrammes` contains valid 24-character hex ObjectIds
3. `category` must match one of the enum values exactly
4. All criteria must have required fields
5. Total criteria weight must equal 100%