# Scorecard Template 400 Bad Request Error Fix

## Issue Summary
Users were receiving a 400 Bad Request error when trying to create scorecard templates. The error occurred because of a field name mismatch between the frontend and backend.

## Root Cause
The frontend was sending:
- `mainStage` (single value)
- `subStage` (single value)

But the backend expected:
- `applicableStages` (array)
- `applicableSubstages` (array)

## Solution Implemented

### Frontend Fix (template-form.component.ts)
Modified the `buildTemplateData()` method to transform the UI fields into the backend's expected format:

```typescript
// Transform mainStage and subStage to backend's expected format
if (formValue.mainStage) {
  templateData.applicableStages = [formValue.mainStage];
}

if (formValue.subStage) {
  templateData.applicableSubstages = [formValue.subStage];
}
```

### Backend Enhancement
Added detailed debug logging to help diagnose field issues:

```javascript
console.log('[DEBUG] Checking required fields:');
console.log('[DEBUG] - name:', req.body.name);
console.log('[DEBUG] - category:', req.body.category);
console.log('[DEBUG] - applicableStages:', req.body.applicableStages);
console.log('[DEBUG] - applicableSubstages:', req.body.applicableSubstages);
console.log('[DEBUG] - mainStage:', req.body.mainStage);
console.log('[DEBUG] - subStage:', req.body.subStage);
```

## Testing Instructions

1. Navigate to the Scorecard Management section
2. Click "Create New Template"
3. Fill in the form:
   - Template Name: "Test Template"
   - Select a Stage (e.g., "Onboarding")
   - Select a Sub-Stage (e.g., "Pre-Screening")
   - Add at least one criterion with weight totaling 100%
4. Click "Create Template"
5. The template should be created successfully without the 400 error

## Files Modified

1. `frontend/src/app/Components/scorecard-management/template-form/template-form.component.ts`
   - Updated `buildTemplateData()` method to include `applicableStages` and `applicableSubstages` arrays

2. `backend/src/routes/scorecard-templates.js`
   - Added enhanced debug logging for better error diagnosis

## Backward Compatibility
The fix maintains backward compatibility by:
- Keeping the original `mainStage` and `subStage` fields in the data
- Setting `category` to the `subStage` value as before
- Converting single values to arrays for the backend

## Future Considerations
Consider updating the backend to accept both formats (single values and arrays) for better flexibility, or fully migrate to the new single-value format if arrays are not needed.