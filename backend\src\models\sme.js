const mongoose = require('mongoose');

const smeSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    required: true
  },
  businessName: {
    type: String,
    required: true,
    trim: true
  },
  tradingName: {
    type: String,
    trim: true
  },
  registrationNumber: {
    type: String,
    unique: true,
    sparse: true, // Allows null values but ensures uniqueness when present
    trim: true
  },
  businessType: {
    type: String,
    enum: [
      'Sole Proprietorship',
      'Partnership',
      'Private Company',
      'Public Company',
      'Close Corporation',
      'Trust',
      'NPO',
      'Other'
    ],
    required: true
  },
  businessDescription: {
    type: String,
    trim: true
  },
  industry: {
    type: String,
    trim: true
  },
  sector: {
    type: String,
    trim: true
  },
  subSector: {
    type: String,
    trim: true
  },
  businessAddress: {
    street: String,
    city: String,
    province: String,
    postalCode: String,
    country: {
      type: String,
      default: 'South Africa'
    }
  },
  postalAddress: {
    street: String,
    city: String,
    province: String,
    postalCode: String,
    country: {
      type: String,
      default: 'South Africa'
    }
  },
  contactDetails: {
    primaryPhone: String,
    alternativePhone: String,
    fax: String,
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    website: String
  },
  ownershipDetails: {
    owners: [{
      name: String,
      idNumber: String,
      shareholding: Number, // Percentage
      role: String
    }],
    blackOwnership: {
      type: Number,
      min: 0,
      max: 100
    },
    womenOwnership: {
      type: Number,
      min: 0,
      max: 100
    },
    youthOwnership: {
      type: Number,
      min: 0,
      max: 100
    },
    disabledOwnership: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  financialInformation: {
    annualTurnover: {
      type: Number,
      min: 0
    },
    netProfit: {
      type: Number
    },
    totalAssets: {
      type: Number,
      min: 0
    },
    totalLiabilities: {
      type: Number,
      min: 0
    },
    bankDetails: {
      bankName: String,
      accountNumber: String,
      branchCode: String,
      accountType: String
    },
    taxNumber: String,
    vatNumber: String,
    taxCompliant: {
      type: Boolean,
      default: false
    }
  },
  operationalDetails: {
    numberOfEmployees: {
      type: Number,
      min: 0,
      default: 0
    },
    yearEstablished: Number,
    yearsInOperation: Number,
    operatingLicenses: [String],
    certifications: [String],
    keyProducts: [String],
    keyServices: [String],
    targetMarkets: [String],
    competitiveAdvantages: [String]
  },
  complianceAndCertifications: {
    beeLevel: {
      type: Number,
      min: 1,
      max: 8
    },
    beeScore: {
      type: Number,
      min: 0,
      max: 100
    },
    beeCertificate: {
      filename: String,
      path: String,
      uploadDate: Date,
      expiryDate: Date
    },
    cidbRegistration: String,
    nhbrcRegistration: String,
    professionalRegistrations: [String],
    insuranceCover: {
      publicLiability: {
        amount: Number,
        provider: String,
        expiryDate: Date
      },
      professionalIndemnity: {
        amount: Number,
        provider: String,
        expiryDate: Date
      }
    }
  },
  documents: [{
    type: {
      type: String,
      enum: [
        'registration_certificate',
        'tax_clearance',
        'bee_certificate',
        'bank_statement',
        'financial_statements',
        'vat_certificate',
        'cidb_certificate',
        'insurance_certificate',
        'other'
      ]
    },
    filename: String,
    originalName: String,
    path: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    verified: {
      type: Boolean,
      default: false
    },
    expiryDate: Date
  }],
  // Relationships
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor'
  },
  // Exxaro-specific fields
  isExxaroSupplier: {
    type: Boolean,
    default: false
  },
  exxaroSupplierNumber: String,
  exxaroCategories: [String],
  // Status and verification
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending'],
    default: 'pending'
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  verificationNotes: String,
  // Risk assessment
  riskRating: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  riskFactors: [String],
  // Audit fields
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastReviewDate: Date,
  nextReviewDate: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
smeSchema.index({ id: 1 });
smeSchema.index({ businessName: 1 });
smeSchema.index({ registrationNumber: 1 });
smeSchema.index({ industry: 1 });
smeSchema.index({ sector: 1 });
smeSchema.index({ status: 1 });
smeSchema.index({ verificationStatus: 1 });
smeSchema.index({ corporateSponsorId: 1 });
smeSchema.index({ isExxaroSupplier: 1 });
smeSchema.index({ createdAt: -1 });
smeSchema.index({ 'contactDetails.email': 1 });

// Text index for search functionality
smeSchema.index({
  businessName: 'text',
  tradingName: 'text',
  businessDescription: 'text',
  industry: 'text',
  sector: 'text'
});

// Virtual for business age
smeSchema.virtual('businessAge').get(function() {
  if (this.yearEstablished) {
    return new Date().getFullYear() - this.yearEstablished;
  }
  return null;
});

// Virtual for total ownership percentages validation
smeSchema.virtual('totalOwnership').get(function() {
  if (this.ownershipDetails?.owners?.length > 0) {
    return this.ownershipDetails.owners.reduce((total, owner) => total + (owner.shareholding || 0), 0);
  }
  return 0;
});

// Virtual for BEE compliance status
smeSchema.virtual('beeCompliant').get(function() {
  return this.complianceAndCertifications?.beeLevel && this.complianceAndCertifications.beeLevel <= 4;
});

// Pre-save middleware
smeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate years in operation if not provided
  if (this.yearEstablished && !this.operationalDetails?.yearsInOperation) {
    this.operationalDetails = this.operationalDetails || {};
    this.operationalDetails.yearsInOperation = new Date().getFullYear() - this.yearEstablished;
  }
  
  next();
});

// Validation for ownership percentages
smeSchema.pre('save', function(next) {
  if (this.ownershipDetails?.owners?.length > 0) {
    const totalOwnership = this.totalOwnership;
    if (totalOwnership > 100) {
      const error = new Error('Total ownership cannot exceed 100%');
      return next(error);
    }
  }
  next();
});

// Static methods
smeSchema.statics.findByIndustry = function(industry) {
  return this.find({ industry: new RegExp(industry, 'i') });
};

smeSchema.statics.findVerified = function() {
  return this.find({ verificationStatus: 'verified' });
};

smeSchema.statics.findByTurnoverRange = function(min, max) {
  const query = {};
  if (min !== undefined) query['financialInformation.annualTurnover'] = { $gte: min };
  if (max !== undefined) {
    query['financialInformation.annualTurnover'] = query['financialInformation.annualTurnover'] || {};
    query['financialInformation.annualTurnover'].$lte = max;
  }
  return this.find(query);
};

// Instance methods
smeSchema.methods.verify = function(notes) {
  this.verificationStatus = 'verified';
  if (notes) {
    this.verificationNotes = notes;
  }
  return this.save();
};

smeSchema.methods.reject = function(notes) {
  this.verificationStatus = 'rejected';
  if (notes) {
    this.verificationNotes = notes;
  }
  return this.save();
};

smeSchema.methods.calculateRiskRating = function() {
  let riskScore = 0;
  const riskFactors = [];
  
  // Financial risk factors
  if (!this.financialInformation?.taxCompliant) {
    riskScore += 2;
    riskFactors.push('Tax non-compliance');
  }
  
  if (this.operationalDetails?.yearsInOperation < 2) {
    riskScore += 1;
    riskFactors.push('New business (less than 2 years)');
  }
  
  if (!this.complianceAndCertifications?.beeLevel || this.complianceAndCertifications.beeLevel > 4) {
    riskScore += 1;
    riskFactors.push('Low BEE level');
  }
  
  // Determine risk rating
  if (riskScore <= 1) {
    this.riskRating = 'low';
  } else if (riskScore <= 3) {
    this.riskRating = 'medium';
  } else {
    this.riskRating = 'high';
  }
  
  this.riskFactors = riskFactors;
  return this.riskRating;
};

module.exports = mongoose.model('SME', smeSchema);
