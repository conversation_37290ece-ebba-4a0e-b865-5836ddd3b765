{"name": "funding-screening-app-backend", "version": "1.0.0", "description": "Backend API for the Funding Screening Application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node src/seed.js", "seed-auth": "node src/seed-auth-data.js", "setup-notifications": "node setup-notifications.js", "test-notifications": "node src/test-notification-system.js", "test-api": "node test-api.js", "migrate-funding": "node run-funding-migration.js", "migrate-notifications": "node src/migrations/create-notification-system.js", "start-mongodb": "node start-mongodb-server.js", "dev-with-db": "concurrently \"npm run start-mongodb\" \"npm run dev\"", "env:dev": "node switch-env.js development", "env:prod": "node switch-env.js production", "env:current": "node switch-env.js --current", "env:help": "node switch-env.js --help"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "concurrently": "^8.2.2", "cors": "^2.8.5", "debug": "^4.4.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "mongodb-memory-server": "^9.5.0", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^6.10.1", "nodemon": "^3.0.1", "pdfkit": "^0.14.0", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "ws": "^8.14.2", "yamljs": "^0.3.0"}}