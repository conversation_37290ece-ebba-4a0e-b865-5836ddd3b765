const mongoose = require('mongoose');

const notificationTemplateSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  type: {
    type: String,
    required: true,
    enum: [
      'APPLICATION_STATUS_CHANGE',
      'INTERVIEW_SCHEDULED',
      'SITE_VISIT_SCHEDULED',
      'DOCUMENT_REQUIRED',
      'APPROVAL_REQUIRED',
      'DEADLINE_REMINDER',
      'SYSTEM_ANNOUNCEMENT',
      'MEETING_INVITATION',
      'REPORT_READY',
      'SCORECARD_COMPLETED',
      'CUSTOM'
    ]
  },
  category: {
    type: String,
    required: true,
    enum: ['APPLICATION', 'INTERVIEW', 'SITE_VISIT', 'DOCUMENT', 'APPROVAL', 'SYSTEM', 'MEETING', 'REPORT', 'SCORECARD', 'GENERAL']
  },
  // Template content with placeholders
  templates: {
    inApp: {
      title: {
        type: String,
        required: true,
        maxlength: 200
      },
      message: {
        type: String,
        required: true,
        maxlength: 1000
      },
      actions: [{
        label: {
          type: String,
          required: true,
          maxlength: 50
        },
        action: {
          type: String,
          required: true,
          enum: ['NAVIGATE', 'API_CALL', 'DOWNLOAD', 'EXTERNAL_LINK']
        },
        url: String,
        method: {
          type: String,
          enum: ['GET', 'POST', 'PUT', 'DELETE'],
          default: 'GET'
        },
        payload: mongoose.Schema.Types.Mixed,
        style: {
          type: String,
          enum: ['PRIMARY', 'SECONDARY', 'SUCCESS', 'WARNING', 'DANGER'],
          default: 'PRIMARY'
        }
      }]
    },
    email: {
      subject: {
        type: String,
        required: true,
        maxlength: 200
      },
      htmlBody: {
        type: String,
        required: true
      },
      textBody: {
        type: String,
        required: true
      },
      attachments: [{
        name: String,
        type: {
          type: String,
          enum: ['STATIC', 'DYNAMIC', 'GENERATED']
        },
        source: String, // File path or generation function name
        mimeType: String
      }]
    },
    sms: {
      message: {
        type: String,
        required: true,
        maxlength: 160
      }
    },
    push: {
      title: {
        type: String,
        required: true,
        maxlength: 100
      },
      body: {
        type: String,
        required: true,
        maxlength: 200
      },
      icon: String,
      badge: String,
      sound: String,
      clickAction: String
    }
  },
  // Default settings
  defaultSettings: {
    priority: {
      type: String,
      enum: ['LOW', 'MEDIUM', 'HIGH', 'URGENT'],
      default: 'MEDIUM'
    },
    deliveryMethods: {
      inApp: {
        type: Boolean,
        default: true
      },
      email: {
        type: Boolean,
        default: false
      },
      sms: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: false
      }
    },
    targetAudience: {
      type: String,
      enum: ['ALL_USERS', 'SPECIFIC_USERS', 'ROLE_BASED', 'ENTITY_BASED'],
      default: 'SPECIFIC_USERS'
    },
    expiryHours: {
      type: Number,
      min: 1,
      max: 8760, // 1 year
      default: 168 // 1 week
    }
  },
  // Template variables/placeholders
  variables: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    type: {
      type: String,
      enum: ['STRING', 'NUMBER', 'DATE', 'BOOLEAN', 'OBJECT', 'ARRAY'],
      default: 'STRING'
    },
    required: {
      type: Boolean,
      default: false
    },
    defaultValue: mongoose.Schema.Types.Mixed,
    validation: {
      pattern: String, // Regex pattern for validation
      minLength: Number,
      maxLength: Number,
      min: Number,
      max: Number
    }
  }],
  // Conditional logic for template selection
  conditions: [{
    field: String, // Field to check (e.g., 'user.role', 'entity.type')
    operator: {
      type: String,
      enum: ['EQUALS', 'NOT_EQUALS', 'CONTAINS', 'NOT_CONTAINS', 'IN', 'NOT_IN', 'GREATER_THAN', 'LESS_THAN']
    },
    value: mongoose.Schema.Types.Mixed,
    templateOverride: {
      inApp: {
        title: String,
        message: String
      },
      email: {
        subject: String,
        htmlBody: String,
        textBody: String
      },
      sms: {
        message: String
      },
      push: {
        title: String,
        body: String
      }
    }
  }],
  // Localization support
  localizations: [{
    locale: {
      type: String,
      required: true
    },
    templates: {
      inApp: {
        title: String,
        message: String
      },
      email: {
        subject: String,
        htmlBody: String,
        textBody: String
      },
      sms: {
        message: String
      },
      push: {
        title: String,
        body: String
      }
    }
  }],
  // Template status
  isActive: {
    type: Boolean,
    default: true
  },
  isSystem: {
    type: Boolean,
    default: false // System templates cannot be deleted
  },
  version: {
    type: Number,
    default: 1
  },
  // Usage tracking
  usageCount: {
    type: Number,
    default: 0
  },
  lastUsed: Date,
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
notificationTemplateSchema.index({ type: 1, category: 1 });
notificationTemplateSchema.index({ isActive: 1, isSystem: 1 });
notificationTemplateSchema.index({ name: 1 });
notificationTemplateSchema.index({ createdBy: 1 });

// Pre-save middleware
notificationTemplateSchema.pre('save', async function(next) {
  try {
    // Generate template ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(4, '0');
      this.id = `TMPL-2025-${nextNumber}`;
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Method to render template with variables
notificationTemplateSchema.methods.render = function(variables = {}, locale = 'en') {
  // Get the appropriate template based on locale
  let templates = this.templates;
  
  // Check for localized version
  const localization = this.localizations.find(l => l.locale === locale);
  if (localization) {
    templates = {
      ...templates,
      ...localization.templates
    };
  }
  
  // Helper function to replace placeholders
  const replacePlaceholders = (text, vars) => {
    if (!text) return text;
    
    return text.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = getNestedValue(vars, path);
      return value !== undefined ? value : match;
    });
  };
  
  // Helper function to get nested object values
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  };
  
  // Render each template type
  const rendered = {};
  
  if (templates.inApp) {
    rendered.inApp = {
      title: replacePlaceholders(templates.inApp.title, variables),
      message: replacePlaceholders(templates.inApp.message, variables),
      actions: templates.inApp.actions?.map(action => ({
        ...action,
        label: replacePlaceholders(action.label, variables),
        url: replacePlaceholders(action.url, variables)
      })) || []
    };
  }
  
  if (templates.email) {
    rendered.email = {
      subject: replacePlaceholders(templates.email.subject, variables),
      htmlBody: replacePlaceholders(templates.email.htmlBody, variables),
      textBody: replacePlaceholders(templates.email.textBody, variables),
      attachments: templates.email.attachments || []
    };
  }
  
  if (templates.sms) {
    rendered.sms = {
      message: replacePlaceholders(templates.sms.message, variables)
    };
  }
  
  if (templates.push) {
    rendered.push = {
      title: replacePlaceholders(templates.push.title, variables),
      body: replacePlaceholders(templates.push.body, variables),
      icon: templates.push.icon,
      badge: templates.push.badge,
      sound: templates.push.sound,
      clickAction: replacePlaceholders(templates.push.clickAction, variables)
    };
  }
  
  return rendered;
};

// Method to validate variables
notificationTemplateSchema.methods.validateVariables = function(variables = {}) {
  const errors = [];
  
  for (const varDef of this.variables) {
    const value = variables[varDef.name];
    
    // Check required variables
    if (varDef.required && (value === undefined || value === null || value === '')) {
      errors.push(`Variable '${varDef.name}' is required`);
      continue;
    }
    
    // Skip validation if value is not provided and not required
    if (value === undefined || value === null) {
      continue;
    }
    
    // Type validation
    if (varDef.type === 'NUMBER' && typeof value !== 'number') {
      errors.push(`Variable '${varDef.name}' must be a number`);
    } else if (varDef.type === 'BOOLEAN' && typeof value !== 'boolean') {
      errors.push(`Variable '${varDef.name}' must be a boolean`);
    } else if (varDef.type === 'DATE' && !(value instanceof Date) && isNaN(Date.parse(value))) {
      errors.push(`Variable '${varDef.name}' must be a valid date`);
    } else if (varDef.type === 'ARRAY' && !Array.isArray(value)) {
      errors.push(`Variable '${varDef.name}' must be an array`);
    } else if (varDef.type === 'OBJECT' && typeof value !== 'object') {
      errors.push(`Variable '${varDef.name}' must be an object`);
    }
    
    // String validation
    if (varDef.type === 'STRING' && typeof value === 'string') {
      if (varDef.validation) {
        if (varDef.validation.minLength && value.length < varDef.validation.minLength) {
          errors.push(`Variable '${varDef.name}' must be at least ${varDef.validation.minLength} characters`);
        }
        if (varDef.validation.maxLength && value.length > varDef.validation.maxLength) {
          errors.push(`Variable '${varDef.name}' must be at most ${varDef.validation.maxLength} characters`);
        }
        if (varDef.validation.pattern && !new RegExp(varDef.validation.pattern).test(value)) {
          errors.push(`Variable '${varDef.name}' does not match required pattern`);
        }
      }
    }
    
    // Number validation
    if (varDef.type === 'NUMBER' && typeof value === 'number') {
      if (varDef.validation) {
        if (varDef.validation.min !== undefined && value < varDef.validation.min) {
          errors.push(`Variable '${varDef.name}' must be at least ${varDef.validation.min}`);
        }
        if (varDef.validation.max !== undefined && value > varDef.validation.max) {
          errors.push(`Variable '${varDef.name}' must be at most ${varDef.validation.max}`);
        }
      }
    }
  }
  
  return errors;
};

// Method to increment usage count
notificationTemplateSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

// Static method to find templates by type and category
notificationTemplateSchema.statics.findByTypeAndCategory = function(type, category) {
  return this.find({
    type: type,
    category: category,
    isActive: true
  }).sort({ name: 1 });
};

// Static method to get system templates
notificationTemplateSchema.statics.getSystemTemplates = function() {
  return this.find({
    isSystem: true,
    isActive: true
  }).sort({ type: 1, name: 1 });
};

const NotificationTemplate = mongoose.model('NotificationTemplate', notificationTemplateSchema);

module.exports = NotificationTemplate;