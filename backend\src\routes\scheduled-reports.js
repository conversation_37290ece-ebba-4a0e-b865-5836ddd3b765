const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const ScheduledReport = require('../models/scheduled-report');
const Report = require('../models/report');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const nodemailer = require('nodemailer');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const { Parser } = require('json2csv');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Get all scheduled reports
router.get('/', authenticateToken, async (req, res) => {
  try {
    const query = {};
    
    // Handle status filter
    if (req.query.status && ['active', 'paused', 'completed', 'error'].includes(req.query.status)) {
      query.status = req.query.status;
    }
    
    // Add user filter if not admin
    if (!req.user.roles.includes('admin')) {
      query.createdBy = req.user.id;
    }
    
    const scheduledReports = await ScheduledReport.find(query)
      .populate('reportId', 'name type')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(parseInt(req.query.limit) || 100)
      .skip(parseInt(req.query.skip) || 0);
    
    const total = await ScheduledReport.countDocuments(query);
    
    res.json({
      scheduledReports,
      total,
      limit: parseInt(req.query.limit) || 100,
      skip: parseInt(req.query.skip) || 0
    });
  } catch (err) {
    console.error('Error fetching scheduled reports:', err);
    res.status(500).json({ message: 'Error fetching scheduled reports', error: err.message });
  }
});

// Get a specific scheduled report by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id)
      .populate('reportId')
      .populate('createdBy', 'firstName lastName');
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has access to this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to access this scheduled report' });
    }
    
    res.json(scheduledReport);
  } catch (err) {
    console.error('Error fetching scheduled report:', err);
    res.status(500).json({ message: 'Error fetching scheduled report', error: err.message });
  }
});

// Create a new scheduled report
router.post('/', authenticateToken, async (req, res) => {
  try {
    // Verify report exists
    const reportExists = await Report.exists({ _id: req.body.reportId });
    if (!reportExists) {
      return res.status(400).json({ message: 'Report not found' });
    }
    
    const newScheduledReport = new ScheduledReport({
      ...req.body,
      createdBy: req.user.id
    });
    
    // Calculate next execution time
    newScheduledReport.calculateNextExecutionTime();
    
    const savedScheduledReport = await newScheduledReport.save();
    
    res.status(201).json(savedScheduledReport);
  } catch (err) {
    console.error('Error creating scheduled report:', err);
    res.status(400).json({ message: 'Error creating scheduled report', error: err.message });
  }
});

// Update an existing scheduled report
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to update this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this scheduled report' });
    }
    
    // If report ID is being updated, verify it exists
    if (req.body.reportId && req.body.reportId !== scheduledReport.reportId.toString()) {
      const reportExists = await Report.exists({ _id: req.body.reportId });
      if (!reportExists) {
        return res.status(400).json({ message: 'Report not found' });
      }
    }
    
    // Update the scheduled report
    Object.assign(scheduledReport, req.body);
    
    // Recalculate next execution time if schedule changed
    if (req.body.schedule || req.body.status === 'active') {
      scheduledReport.calculateNextExecutionTime();
    }
    
    const updatedScheduledReport = await scheduledReport.save();
    
    res.json(updatedScheduledReport);
  } catch (err) {
    console.error('Error updating scheduled report:', err);
    res.status(400).json({ message: 'Error updating scheduled report', error: err.message });
  }
});

// Delete a scheduled report
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to delete this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to delete this scheduled report' });
    }
    
    await ScheduledReport.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Scheduled report deleted successfully' });
  } catch (err) {
    console.error('Error deleting scheduled report:', err);
    res.status(500).json({ message: 'Error deleting scheduled report', error: err.message });
  }
});

// Update schedule configuration
router.put('/:id/schedule', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to update this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this scheduled report' });
    }
    
    // Update schedule
    scheduledReport.schedule = req.body;
    
    // Recalculate next execution time
    scheduledReport.calculateNextExecutionTime();
    
    const updatedScheduledReport = await scheduledReport.save();
    
    res.json(updatedScheduledReport);
  } catch (err) {
    console.error('Error updating schedule:', err);
    res.status(400).json({ message: 'Error updating schedule', error: err.message });
  }
});

// Update delivery configuration
router.put('/:id/delivery', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to update this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this scheduled report' });
    }
    
    // Update delivery
    scheduledReport.delivery = req.body;
    
    const updatedScheduledReport = await scheduledReport.save();
    
    res.json(updatedScheduledReport);
  } catch (err) {
    console.error('Error updating delivery:', err);
    res.status(400).json({ message: 'Error updating delivery', error: err.message });
  }
});

// Pause a scheduled report
router.put('/:id/pause', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to update this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this scheduled report' });
    }
    
    // Update status
    scheduledReport.status = 'paused';
    
    const updatedScheduledReport = await scheduledReport.save();
    
    res.json(updatedScheduledReport);
  } catch (err) {
    console.error('Error pausing scheduled report:', err);
    res.status(500).json({ message: 'Error pausing scheduled report', error: err.message });
  }
});

// Resume a scheduled report
router.put('/:id/resume', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to update this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to update this scheduled report' });
    }
    
    // Update status
    scheduledReport.status = 'active';
    
    // Recalculate next execution time
    scheduledReport.calculateNextExecutionTime();
    
    const updatedScheduledReport = await scheduledReport.save();
    
    res.json(updatedScheduledReport);
  } catch (err) {
    console.error('Error resuming scheduled report:', err);
    res.status(500).json({ message: 'Error resuming scheduled report', error: err.message });
  }
});

// Run a scheduled report immediately
router.post('/:id/run-now', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id)
      .populate('reportId');
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to run this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to run this scheduled report' });
    }
    
    // Create execution record
    const executionId = uuidv4();
    const execution = {
      executionId,
      scheduledTime: new Date(),
      status: 'running'
    };
    
    scheduledReport.executions.push(execution);
    await scheduledReport.save();
    
    // Run the report
    try {
      // Generate the report
      const report = await Report.findById(scheduledReport.reportId);
      
      if (!report) {
        throw new Error('Report not found');
      }
      
      // Generate report data based on type
      let reportData;
      
      switch (report.type) {
        case 'status':
          reportData = await generateStatusReport(report.filters);
          break;
        case 'processing-time':
          reportData = await generateProcessingTimeReport(report.filters);
          break;
        case 'approval-rates':
          reportData = await generateApprovalRatesReport(report.filters);
          break;
        case 'funding-distribution':
          reportData = await generateFundingDistributionReport(report.filters);
          break;
        case 'regional-analysis':
          reportData = await generateRegionalAnalysisReport(report.filters);
          break;
        case 'custom':
          reportData = await generateCustomReport(report.filters, report.columns);
          break;
        default:
          throw new Error('Invalid report type');
      }
      
      // Update report with generated data
      report.data = reportData;
      report.status = 'generated';
      report.lastGeneratedAt = new Date();
      
      await report.save();
      
      // Deliver the report
      let reportUrl;
      
      if (scheduledReport.delivery.method === 'email') {
        reportUrl = await deliverReportByEmail(report, scheduledReport);
      } else if (scheduledReport.delivery.method === 'download') {
        reportUrl = await saveReportForDownload(report, scheduledReport);
      } else if (scheduledReport.delivery.method === 'api') {
        reportUrl = await deliverReportToApi(report, scheduledReport);
      }
      
      // Update execution record
      const executionIndex = scheduledReport.executions.findIndex(e => e.executionId === executionId);
      
      if (executionIndex !== -1) {
        scheduledReport.executions[executionIndex].status = 'completed';
        scheduledReport.executions[executionIndex].executionTime = new Date();
        scheduledReport.executions[executionIndex].reportUrl = reportUrl;
        
        scheduledReport.lastExecutionTime = new Date();
        
        await scheduledReport.save();
      }
      
      res.json({
        message: 'Report executed successfully',
        execution: scheduledReport.executions[executionIndex]
      });
    } catch (err) {
      // Update execution record with error
      const executionIndex = scheduledReport.executions.findIndex(e => e.executionId === executionId);
      
      if (executionIndex !== -1) {
        scheduledReport.executions[executionIndex].status = 'failed';
        scheduledReport.executions[executionIndex].executionTime = new Date();
        scheduledReport.executions[executionIndex].error = err.message;
        
        scheduledReport.lastExecutionTime = new Date();
        
        await scheduledReport.save();
      }
      
      throw err;
    }
  } catch (err) {
    console.error('Error running scheduled report:', err);
    res.status(500).json({ message: 'Error running scheduled report', error: err.message });
  }
});

// Get execution history for a scheduled report
router.get('/:id/executions', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to access this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to access this scheduled report' });
    }
    
    // Get executions
    const limit = parseInt(req.query.limit) || 10;
    const executions = scheduledReport.executions
      .sort((a, b) => new Date(b.scheduledTime) - new Date(a.scheduledTime))
      .slice(0, limit);
    
    res.json(executions);
  } catch (err) {
    console.error('Error fetching execution history:', err);
    res.status(500).json({ message: 'Error fetching execution history', error: err.message });
  }
});

// Get a specific execution
router.get('/:id/executions/:executionId', authenticateToken, async (req, res) => {
  try {
    const scheduledReport = await ScheduledReport.findById(req.params.id);
    
    if (!scheduledReport) {
      return res.status(404).json({ message: 'Scheduled report not found' });
    }
    
    // Check if user has permission to access this scheduled report
    if (scheduledReport.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'You do not have permission to access this scheduled report' });
    }
    
    // Find execution
    const execution = scheduledReport.executions.find(e => e.executionId === req.params.executionId);
    
    if (!execution) {
      return res.status(404).json({ message: 'Execution not found' });
    }
    
    res.json(execution);
  } catch (err) {
    console.error('Error fetching execution:', err);
    res.status(500).json({ message: 'Error fetching execution', error: err.message });
  }
});

// Helper functions for report generation
// These are placeholders and would be implemented similarly to the ones in reports.js
async function generateStatusReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateProcessingTimeReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateApprovalRatesReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateFundingDistributionReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateRegionalAnalysisReport(filters) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns: [], rows: [], summary: {} };
}

async function generateCustomReport(filters, columns) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  return { columns, rows: [] };
}

// Helper functions for report delivery
async function deliverReportByEmail(report, scheduledReport) {
  // Create a transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD
    }
  });
  
  // Generate report file
  const format = scheduledReport.delivery.format;
  const reportFileName = `${report.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.${format}`;
  const reportFilePath = path.join(__dirname, '..', '..', 'temp', reportFileName);
  
  // Ensure temp directory exists
  if (!fs.existsSync(path.join(__dirname, '..', '..', 'temp'))) {
    fs.mkdirSync(path.join(__dirname, '..', '..', 'temp'), { recursive: true });
  }
  
  // Generate report file based on format
  switch (format) {
    case 'pdf':
      await generatePdfFile(report, reportFilePath);
      break;
    case 'excel':
      await generateExcelFile(report, reportFilePath);
      break;
    case 'csv':
      await generateCsvFile(report, reportFilePath);
      break;
  }
  
  // Prepare email
  const mailOptions = {
    from: process.env.SMTP_FROM,
    subject: scheduledReport.delivery.emailSubject || `${report.name} - ${new Date().toLocaleDateString()}`,
    html: scheduledReport.delivery.emailBody || `<p>Please find attached the ${report.name} report.</p>`,
    attachments: [
      {
        filename: reportFileName,
        path: reportFilePath
      }
    ]
  };
  
  // Add recipients
  if (scheduledReport.delivery.recipients && scheduledReport.delivery.recipients.length > 0) {
    const toRecipients = scheduledReport.delivery.recipients
      .filter(r => r.type === 'to')
      .map(r => r.email);
    
    const ccRecipients = scheduledReport.delivery.recipients
      .filter(r => r.type === 'cc')
      .map(r => r.email);
    
    const bccRecipients = scheduledReport.delivery.recipients
      .filter(r => r.type === 'bcc')
      .map(r => r.email);
    
    if (toRecipients.length > 0) {
      mailOptions.to = toRecipients.join(',');
    }
    
    if (ccRecipients.length > 0) {
      mailOptions.cc = ccRecipients.join(',');
    }
    
    if (bccRecipients.length > 0) {
      mailOptions.bcc = bccRecipients.join(',');
    }
  }
  
  // Send email
  await transporter.sendMail(mailOptions);
  
  // Clean up
  fs.unlinkSync(reportFilePath);
  
  return null; // No URL for email delivery
}

async function saveReportForDownload(report, scheduledReport) {
  // Generate report file
  const format = scheduledReport.delivery.format;
  const reportFileName = `${report.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.${format}`;
  const reportFilePath = path.join(__dirname, '..', '..', 'public', 'reports', reportFileName);
  
  // Ensure reports directory exists
  if (!fs.existsSync(path.join(__dirname, '..', '..', 'public', 'reports'))) {
    fs.mkdirSync(path.join(__dirname, '..', '..', 'public', 'reports'), { recursive: true });
  }
  
  // Generate report file based on format
  switch (format) {
    case 'pdf':
      await generatePdfFile(report, reportFilePath);
      break;
    case 'excel':
      await generateExcelFile(report, reportFilePath);
      break;
    case 'csv':
      await generateCsvFile(report, reportFilePath);
      break;
  }
  
  return `/reports/${reportFileName}`;
}

async function deliverReportToApi(report, scheduledReport) {
  // This would typically send the report to an external API
  // This is a placeholder implementation
  return null;
}

// Helper functions for file generation
async function generatePdfFile(report, filePath) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  const doc = new PDFDocument();
  const stream = fs.createWriteStream(filePath);
  
  doc.pipe(stream);
  doc.text('Report content would go here');
  doc.end();
  
  return new Promise((resolve, reject) => {
    stream.on('finish', resolve);
    stream.on('error', reject);
  });
}

async function generateExcelFile(report, filePath) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Report');
  
  worksheet.addRow(['Report content would go here']);
  
  await workbook.xlsx.writeFile(filePath);
}

async function generateCsvFile(report, filePath) {
  // Implementation would be similar to the one in reports.js
  // This is a placeholder
  const csv = 'Report content would go here';
  
  fs.writeFileSync(filePath, csv);
}

module.exports = router;
