const express = require('express');
const router = express.Router();
const FundingProgramme = require('../models/funding-programme');
const Application = require('../models/application');
const ProgrammeAssignment = require('../models/programme-assignment');

// Get all programmes
router.get('/', async (req, res) => {
  try {
    // Filter by corporate sponsor if provided
    const filter = {};
    if (req.query.corporateSponsorId) {
      filter.corporateSponsorId = req.query.corporateSponsorId;
    }
    
    const programmes = await FundingProgramme.find(filter);
    res.json(programmes);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Create a new programme
router.post('/', async (req, res) => {
  const programme = new FundingProgramme(req.body);
  
  try {
    const newProgramme = await programme.save();
    res.status(201).json(newProgramme);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Get a specific programme
router.get('/:id', async (req, res) => {
  try {
    const programme = await FundingProgramme.findOne({ id: req.params.id });
    if (!programme) {
      return res.status(404).json({ message: 'Programme not found' });
    }
    res.json(programme);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Update a programme
router.put('/:id', async (req, res) => {
  try {
    const programme = await FundingProgramme.findOne({ id: req.params.id });
    if (!programme) {
      return res.status(404).json({ message: 'Programme not found' });
    }
    
    Object.assign(programme, req.body);
    programme.updatedAt = Date.now();
    
    const updatedProgramme = await programme.save();
    res.json(updatedProgramme);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Delete a programme
router.delete('/:id', async (req, res) => {
  try {
    const programme = await FundingProgramme.findOne({ id: req.params.id });
    if (!programme) {
      return res.status(404).json({ message: 'Programme not found' });
    }
    
    await FundingProgramme.deleteOne({ id: req.params.id });
    res.json({ message: 'Programme deleted' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Get all applications for a programme
router.get('/:id/applications', async (req, res) => {
  try {
    const applications = await Application.find({ programmeId: req.params.id });
    res.json(applications);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Get all assignments for a programme
router.get('/:id/assignments', async (req, res) => {
  try {
    const assignments = await ProgrammeAssignment.find({ programmeId: req.params.id });
    res.json(assignments);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Create a new assignment for a programme
router.post('/:id/assignments', async (req, res) => {
  try {
    const programme = await FundingProgramme.findOne({ id: req.params.id });
    if (!programme) {
      return res.status(404).json({ message: 'Programme not found' });
    }
    
    const assignment = new ProgrammeAssignment({
      ...req.body,
      programmeId: req.params.id
    });
    
    const newAssignment = await assignment.save();
    res.status(201).json(newAssignment);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
});

// Delete an assignment
router.delete('/:id/assignments/:assignmentId', async (req, res) => {
  try {
    const assignment = await ProgrammeAssignment.findOne({ id: req.params.assignmentId });
    if (!assignment) {
      return res.status(404).json({ message: 'Assignment not found' });
    }
    
    await ProgrammeAssignment.deleteOne({ id: req.params.assignmentId });
    res.json({ message: 'Assignment deleted' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

// Get statistics for a programme
router.get('/:id/statistics', async (req, res) => {
  try {
    const programme = await FundingProgramme.findOne({ id: req.params.id });
    if (!programme) {
      return res.status(404).json({ message: 'Programme not found' });
    }
    
    const applications = await Application.find({ programmeId: req.params.id });
    
    // Calculate statistics
    const totalApplications = applications.length;
    const pendingApplications = applications.filter(app => app.status === 'pending').length;
    const inReviewApplications = applications.filter(app => app.status === 'in-review').length;
    const approvedApplications = applications.filter(app => app.status === 'approved').length;
    const rejectedApplications = applications.filter(app => app.status === 'rejected').length;
    
    // Calculate total funding amount requested and approved
    const totalFundingRequested = applications.reduce((sum, app) => sum + (app.fundingAmount || 0), 0);
    const totalFundingApproved = applications
      .filter(app => app.status === 'approved')
      .reduce((sum, app) => sum + (app.fundingAmount || 0), 0);
    
    res.json({
      totalApplications,
      pendingApplications,
      inReviewApplications,
      approvedApplications,
      rejectedApplications,
      totalFundingRequested,
      totalFundingApproved,
      budget: programme.budget
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

module.exports = router;
