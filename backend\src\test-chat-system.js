const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Import models and services
const ChatRoom = require('./models/chat-room');
const ChatParticipant = require('./models/chat-participant');
const ChatMessage = require('./models/chat-message');
const User = require('./models/user');
const chatService = require('./services/chat-service');

async function testChatSystem() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get test users
    const admin = await User.findOne({ username: 'admin' });
    const manager = await User.findOne({ username: 'manager' });

    if (!admin || !manager) {
      throw new Error('Test users not found. Please run seed-auth-data.js first.');
    }

    console.log('\n=== Testing Chat System ===\n');

    // Test 1: Get user's chat rooms
    console.log('Test 1: Getting chat rooms for admin user...');
    const adminRooms = await chatService.getUserRooms(admin._id);
    console.log(`Found ${adminRooms.length} chat rooms for admin`);
    adminRooms.forEach(room => {
      console.log(`  - ${room.name || room.displayName} (${room.type})`);
    });

    // Test 2: Get messages from a room
    if (adminRooms.length > 0) {
      const firstRoom = adminRooms[0];
      console.log(`\nTest 2: Getting messages from room "${firstRoom.name || firstRoom.displayName}"...`);
      const messages = await chatService.getRoomMessages(firstRoom._id, admin._id, { limit: 5 });
      console.log(`Found ${messages.length} messages`);
      messages.forEach(msg => {
        console.log(`  - ${msg.senderId.username}: ${msg.content.text}`);
      });
    }

    // Test 3: Send a test message
    if (adminRooms.length > 0) {
      const firstRoom = adminRooms[0];
      console.log(`\nTest 3: Sending a test message to room "${firstRoom.name || firstRoom.displayName}"...`);
      
      try {
        const newMessage = await chatService.sendMessage(
          {
            roomId: firstRoom._id,
            content: {
              text: 'This is a test message from the chat system test script.',
              type: 'TEXT'
            }
          },
          admin._id
        );
        console.log('Message sent successfully!');
        console.log(`  Message ID: ${newMessage.id}`);
        console.log(`  Content: ${newMessage.content.text}`);
      } catch (error) {
        console.error('Error sending message:', error.message);
      }
    }

    // Test 4: Create a new group chat
    console.log('\nTest 4: Creating a new group chat room...');
    try {
      const newRoom = await chatService.createRoom({
        name: 'Test Group Chat',
        type: 'GROUP',
        description: 'A test group chat created by the test script',
        participants: [manager._id]
      }, admin._id);
      console.log('Group chat created successfully!');
      console.log(`  Room ID: ${newRoom._id}`);
      console.log(`  Room Name: ${newRoom.name}`);
    } catch (error) {
      console.error('Error creating group chat:', error.message);
    }

    // Test 5: Check unread counts
    console.log('\nTest 5: Checking unread message counts...');
    const managerRooms = await chatService.getUserRooms(manager._id);
    console.log(`Manager has ${managerRooms.length} chat rooms`);
    managerRooms.forEach(room => {
      const unreadCount = room.participant?.unreadCount || 0;
      console.log(`  - ${room.name || room.displayName}: ${unreadCount} unread messages`);
    });

    console.log('\n=== Chat System Test Completed Successfully ===');

  } catch (error) {
    console.error('Error testing chat system:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the test
testChatSystem();