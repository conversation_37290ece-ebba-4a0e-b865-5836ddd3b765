const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3002; // Use port 3002 to match the Angular app configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app';

// Middleware
app.use(cors({
  origin: 'http://localhost:4200', // Allow Angular dev server
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    return false;
  }
}

async function initializeServer() {
  try {
    // Connect to MongoDB
    const dbConnected = await connectToDatabase();
    
    if (!dbConnected) {
      console.error('Failed to connect to MongoDB. Server will not start.');
      process.exit(1);
    }
    
    // Import routes
    const applicationRoutes = require('./routes/applications');
    const siteVisitRoutes = require('./routes/site-visits');
    const corporateSponsorRoutes = require('./routes/corporate-sponsors');
    
    // Initialize routes
    app.use('/api/applications', applicationRoutes);
    app.use('/api/site-visits', siteVisitRoutes);
    app.use('/api/corporate-sponsors', corporateSponsorRoutes);
    
    // Health check endpoint
    app.get('/api/health', (req, res) => {
      res.status(200).json({
        status: 'ok',
        database: 'mongodb',
        version: '1.0.0',
        connectionStatus: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString()
      });
    });
    
    // Add a direct route for /applications to handle the frontend requests
    app.get('/applications', async (req, res) => {
      try {
        console.log('Received request at /applications with query:', req.query);
        
        // Import the applications routes module
        const applicationRoutes = require('./routes/applications');
        
        // Get the applications from MongoDB
        const Application = require('./models/application');
        
        // Apply any filters from query parameters
        const filter = {};
        const { page = 1, limit = 25, stage, status, programmeId, corporateSponsorId } = req.query;
        
        if (stage) filter.currentMainStage = stage;
        if (status) filter.status = status;
        if (programmeId) filter.programmeId = programmeId;
        if (corporateSponsorId) filter.corporateSponsorId = corporateSponsorId;
        
        console.log('Applying filter:', filter);
        
        // Query the database
        const applications = await Application.find(filter)
          .limit(limit * 1)
          .skip((page - 1) * limit)
          .lean();
        
        const count = await Application.countDocuments(filter);
        
        console.log(`Found ${applications.length} applications matching filter`);
        
        // Return the applications in the format expected by the frontend
        res.json({
          total: count,
          page: Number(page),
          totalPages: Math.ceil(count / limit),
          data: applications
        });
      } catch (error) {
        console.error('Error serving applications:', error);
        res.status(500).json({
          message: 'Failed to load applications',
          error: error.message
        });
      }
    });

    // Detailed database status endpoint
    app.get('/api/database-status', async (req, res) => {
      try {
        // Check if we can perform a simple query
        const collections = await mongoose.connection.db.listCollections().toArray();
        
        res.status(200).json({
          status: 'ok',
          connectionState: mongoose.connection.readyState,
          connectionStateText: ['disconnected', 'connected', 'connecting', 'disconnecting'][mongoose.connection.readyState],
          databaseName: mongoose.connection.name,
          collections: collections.map(c => c.name),
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          message: 'Database connection error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });
    
    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error('API Error:', err);
      res.status(500).json({ 
        error: 'Server error', 
        message: err.message,
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
      });
    });
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT} with MongoDB database`);
    });
  } catch (err) {
    console.error('Server initialization failed:', err);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
});

// Start the server
initializeServer();
