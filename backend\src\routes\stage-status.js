/**
 * Stage Status API Routes
 * 
 * Provides endpoints for managing application stage statuses,
 * including status transitions, validation, and administrative overrides.
 */

const express = require('express');
const router = express.Router();
const stageStatusService = require('../services/stage-status.service');
const { ApplicationMainStage, ApplicationSubStage, StageStatus } = require('../models/stage-status-enums');

/**
 * Get the current stage status for an application
 * 
 * GET /api/stage-status/:applicationId
 */
console.log('Testing stage-status router')

router.get('/test', (req, res) => {
  return res.json({ success: true, message: 'Router is working!' });
});

router.get('/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const stageStatus = await stageStatusService.getCurrentStageStatus(applicationId);
    
    res.json({
      success: true,
      stageStatus
    });
  } catch (error) {
    console.error('Error getting stage status:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get stage status'
    });
  }
});



/**
 * Get the full stage hierarchy for an application
 * 
 * GET /api/stage-status/:applicationId/hierarchy
 */
router.get('/:applicationId/hierarchy', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const stageHierarchy = await stageStatusService.getStageHierarchy(applicationId);
    
    res.json({
      success: true,
      stageHierarchy
    });
  } catch (error) {
    console.error('Error getting stage hierarchy:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get stage hierarchy'
    });
  }
});

/**
 * Update the stage status for an application
 * 
 * PUT /api/stage-status/:applicationId
 * 
 * Request body:
 * {
 *   "mainStage": "ONBOARDING",
 *   "subStage": "BENEFICIARY_REGISTRATION",
 *   "status": "ACTIVE",
 *   "userId": "user123",
 *   "notes": "Starting the onboarding process"
 * }
 */
router.put('/:applicationId', async (req, res) => {
  console.log('Received request to update stage status:', req.body);
  try {
    const { applicationId } = req.params;
    const { mainStage, subStage, status, userId, notes } = req.body;
    
    // Validate required fields
    if (!mainStage || !subStage || !status) {
      return res.status(400).json({
        success: false,
        message: 'Main stage, substage, and status are required'
      });
    }
    
    // Update the stage status
    const updatedApplication = await stageStatusService.updateStageStatus(
      applicationId,
      mainStage,
      subStage,
      status,
      userId || 'system',
      notes || ''
    );
    
    res.json({
      success: true,
      message: 'Stage status updated successfully',
      application: {
        id: updatedApplication.id,
        currentMainStage: updatedApplication.currentMainStage,
        currentSubStage: updatedApplication.currentSubStage,
        currentStageStatus: updatedApplication.currentStageStatus
      }
    });
  } catch (error) {
    console.error('Error updating stage status:', error);
    
    // Handle validation errors
    if (error.message.includes('Invalid') || error.message.includes('not belong')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update stage status'
    });
  }
});

/**
 * Perform an administrative override of a stage status
 * 
 * PUT /api/stage-status/:applicationId/override
 * 
 * Request body:
 * {
 *   "mainStage": "ONBOARDING",
 *   "subStage": "BENEFICIARY_REGISTRATION",
 *   "status": "COMPLETED",
 *   "userId": "admin123",
 *   "reason": "Manually completing stage due to system error"
 * }
 */
router.put('/:applicationId/override', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const { mainStage, subStage, status, userId, reason } = req.body;
    
    // Validate required fields
    if (!mainStage || !subStage || !status) {
      return res.status(400).json({
        success: false,
        message: 'Main stage, substage, and status are required'
      });
    }
    
    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'A reason is required for administrative overrides'
      });
    }
    
    // Perform the administrative override
    const updatedApplication = await stageStatusService.adminOverrideStageStatus(
      applicationId,
      mainStage,
      subStage,
      status,
      userId || 'admin',
      reason
    );
    
    res.json({
      success: true,
      message: 'Stage status overridden successfully',
      application: {
        id: updatedApplication.id,
        currentMainStage: updatedApplication.currentMainStage,
        currentSubStage: updatedApplication.currentSubStage,
        currentStageStatus: updatedApplication.currentStageStatus
      }
    });
  } catch (error) {
    console.error('Error overriding stage status:', error);
    
    // Handle validation errors
    if (error.message.includes('Invalid') || error.message.includes('required')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to override stage status'
    });
  }
});

/**
 * Get the audit log for an application
 * 
 * GET /api/stage-status/:applicationId/audit-log
 */
router.get('/:applicationId/audit-log', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const auditLog = await stageStatusService.getStageStatusAuditLog(applicationId);
    
    res.json({
      success: true,
      auditLog
    });
  } catch (error) {
    console.error('Error getting audit log:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get audit log'
    });
  }
});

/**
 * Get available main stages
 * 
 * GET /api/stage-status/main-stages
 */
router.get('/main-stages/list', (req, res) => {
  try {
    const mainStages = Object.values(ApplicationMainStage);
    
    res.json({
      success: true,
      mainStages
    });
  } catch (error) {
    console.error('Error getting main stages:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get main stages'
    });
  }
});

/**
 * Get available substages for a main stage
 * 
 * GET /api/stage-status/main-stages/:mainStage/sub-stages
 */
router.get('/main-stages/:mainStage/sub-stages', (req, res) => {
  try {
    const { mainStage } = req.params;
    
    // Validate main stage
    if (!Object.values(ApplicationMainStage).includes(mainStage)) {
      return res.status(400).json({
        success: false,
        message: `Invalid main stage: ${mainStage}`
      });
    }
    
    const subStages = require('../models/stage-status-enums').MainStageToSubStagesMap[mainStage] || [];
    
    res.json({
      success: true,
      mainStage,
      subStages
    });
  } catch (error) {
    console.error('Error getting substages:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get substages'
    });
  }
});

/**
 * Get available statuses
 * 
 * GET /api/stage-status/statuses
 */
router.get('/statuses/list', (req, res) => {
  try {
    const statuses = Object.values(StageStatus);
    
    res.json({
      success: true,
      statuses
    });
  } catch (error) {
    console.error('Error getting statuses:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get statuses'
    });
  }
});

/**
 * Get valid status transitions
 * 
 * GET /api/stage-status/status-transitions
 */
router.get('/status-transitions/list', (req, res) => {
  try {
    const validStatusTransitions = require('../models/stage-status-enums').ValidStatusTransitions;
    
    res.json({
      success: true,
      validStatusTransitions
    });
  } catch (error) {
    console.error('Error getting status transitions:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get status transitions'
    });
  }
});

module.exports = router;