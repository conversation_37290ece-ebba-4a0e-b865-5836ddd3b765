const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
const PORT = 3002; // Hardcoded port 3002

// Middleware
app.use(cors());
app.use(express.json());

// In-memory database
const inMemoryDb = require('./in-memory-db');

// Mock Application model for routes
const mockApplication = {
  find: () => {
    // Return an object with chainable methods
    const apps = inMemoryDb.findAll();
    return {
      limit: (limit) => ({
        skip: (skip) => ({
          populate: () => ({
            lean: () => apps.then(allApps => {
              const start = skip || 0;
              const end = start + (limit || allApps.length);
              return allApps.slice(start, end);
            })
          })
        }),
        populate: () => ({
          lean: () => apps.then(allApps => allApps.slice(0, limit))
        })
      }),
      populate: () => ({
        lean: () => apps
      }),
      lean: () => apps,
      then: (callback) => apps.then(callback)
    };
  },
  findById: (id) => inMemoryDb.findById(id),
  create: (data) => inMemoryDb.create(data),
  findByIdAndUpdate: (id, data) => inMemoryDb.update(id, data),
  findByIdAndDelete: (id) => inMemoryDb.remove(id),
  countDocuments: () => inMemoryDb.findAll().then(apps => apps.length)
};

// Mock SiteVisit model for routes
const siteVisits = [];
let siteVisitNextId = 1;

const mockSiteVisit = {
  find: (filter = {}) => {
    return Promise.resolve(siteVisits.filter(visit => {
      for (const key in filter) {
        if (visit[key] !== filter[key]) {
          return false;
        }
      }
      return true;
    }));
  },
  findById: (id) => {
    return Promise.resolve(siteVisits.find(visit => visit.id === id));
  },
  findByApplicationId: (applicationId) => {
    return Promise.resolve(siteVisits.filter(visit => visit.applicationId === applicationId));
  },
  create: (data) => {
    const newSiteVisit = {
      ...data,
      id: data.id || `${siteVisitNextId++}`,
      createdDate: new Date(),
      updatedDate: new Date()
    };
    
    // Ensure location is properly structured
    if (!newSiteVisit.location) {
      newSiteVisit.location = {
        address: 'Unknown',
        latitude: null,
        longitude: null
      };
    }
    
    siteVisits.push(newSiteVisit);
    return Promise.resolve(newSiteVisit);
  },
  findByIdAndUpdate: (id, updates) => {
    const index = siteVisits.findIndex(visit => visit.id === id);
    if (index === -1) return Promise.resolve(null);
    
    const updatedSiteVisit = {
      ...siteVisits[index],
      ...updates,
      updatedDate: new Date()
    };
    
    siteVisits[index] = updatedSiteVisit;
    return Promise.resolve(updatedSiteVisit);
  },
  findByIdAndDelete: (id) => {
    const index = siteVisits.findIndex(visit => visit.id === id);
    if (index === -1) return Promise.resolve(null);
    
    const deletedSiteVisit = siteVisits[index];
    siteVisits.splice(index, 1);
    return Promise.resolve(deletedSiteVisit);
  }
};

// Mock the require for models/application.js, models/site-visit.js, and models/interview.js
require.cache[require.resolve('./models/application')] = {
  exports: mockApplication
};

require.cache[require.resolve('./models/site-visit')] = {
  exports: mockSiteVisit
};

// Mock Interview model for routes
const Interview = require('./in-memory-interview-store');
require.cache[require.resolve('./models/interview')] = {
  exports: Interview
};

// Function to seed sample site visits
async function seedSiteVisits() {
  // Create sample site visits for existing applications
  const sampleSiteVisits = [
    {
      applicationId: 'APP-2025-001',
      scheduledDate: new Date('2025-03-20T09:00:00Z'),
      status: 'SCHEDULED',
      location: {
        address: '123 Main Street, Johannesburg',
        latitude: -26.2041,
        longitude: 28.0473
      },
      conductedBy: 'Michael Chen',
      duration: 120,
      checklist: [
        {
          id: 'check-001',
          category: 'Facility',
          description: 'Verify store location and size',
          verified: true,
          notes: 'Store location matches application details',
          createdDate: new Date('2025-03-18T14:30:00Z'),
          updatedDate: new Date('2025-03-18T14:30:00Z')
        },
        {
          id: 'check-002',
          category: 'Inventory',
          description: 'Verify inventory levels match reported figures',
          verified: false,
          notes: 'Need to check inventory records',
          createdDate: new Date('2025-03-18T14:35:00Z'),
          updatedDate: new Date('2025-03-18T14:35:00Z')
        }
      ],
      photos: [],
      documents: [],
      actionItems: [],
      createdDate: new Date('2025-03-18T14:00:00Z'),
      updatedDate: new Date('2025-03-18T14:00:00Z')
    },
    {
      applicationId: 'APP-2025-002',
      scheduledDate: new Date('2025-03-22T10:30:00Z'),
      status: 'SCHEDULED',
      location: {
        address: '456 Tech Avenue, Cape Town',
        latitude: -33.9249,
        longitude: 18.4241
      },
      conductedBy: 'Thabo Molefe',
      duration: 90,
      checklist: [],
      photos: [],
      documents: [],
      actionItems: [],
      createdDate: new Date('2025-03-19T09:15:00Z'),
      updatedDate: new Date('2025-03-19T09:15:00Z')
    },
    {
      applicationId: 'APP-2025-003',
      scheduledDate: new Date('2025-03-15T08:00:00Z'),
      status: 'COMPLETED',
      location: {
        address: 'Farm 789, Stellenbosch',
        latitude: -33.9321,
        longitude: 18.8602
      },
      conductedBy: 'David Moyo',
      findings: 'Farm operations are well-established and align with application details. Sustainable farming practices observed. Recommend approval.',
      duration: 180,
      checklist: [
        {
          id: 'check-003',
          category: 'Land',
          description: 'Verify farm size and boundaries',
          verified: true,
          notes: 'Farm size matches application details',
          createdDate: new Date('2025-03-15T08:30:00Z'),
          updatedDate: new Date('2025-03-15T08:30:00Z')
        },
        {
          id: 'check-004',
          category: 'Crops',
          description: 'Verify crop types and planting areas',
          verified: true,
          notes: 'Crop types match application details',
          createdDate: new Date('2025-03-15T09:00:00Z'),
          updatedDate: new Date('2025-03-15T09:00:00Z')
        },
        {
          id: 'check-005',
          category: 'Equipment',
          description: 'Verify farm equipment and machinery',
          verified: true,
          notes: 'Equipment matches application details',
          createdDate: new Date('2025-03-15T09:30:00Z'),
          updatedDate: new Date('2025-03-15T09:30:00Z')
        }
      ],
      photos: [
        {
          id: 'photo-001',
          url: 'https://example.com/photos/farm1.jpg',
          caption: 'Main farm building',
          category: 'EXTERIOR',
          createdDate: new Date('2025-03-15T08:15:00Z'),
          metadata: {}
        },
        {
          id: 'photo-002',
          url: 'https://example.com/photos/crops.jpg',
          caption: 'Crop fields',
          category: 'EXTERIOR',
          createdDate: new Date('2025-03-15T08:45:00Z'),
          metadata: {}
        }
      ],
      documents: [],
      actionItems: [],
      createdDate: new Date('2025-03-14T15:30:00Z'),
      updatedDate: new Date('2025-03-15T12:00:00Z')
    }
  ];

  // Add sample site visits to the in-memory array
  for (const siteVisit of sampleSiteVisits) {
    await mockSiteVisit.create(siteVisit);
  }
  
  console.log(`Seeded ${sampleSiteVisits.length} sample site visits`);
}

async function initializeServer() {
  try {
    console.log('Using in-memory database');
    
    // Seed the in-memory database with applications
    console.log('Seeding in-memory database with applications...');
    await inMemoryDb.seed();
    console.log('In-memory database seeded with sample applications');
    
    // Seed sample site visits
    await seedSiteVisits();
    
    // Seed in-memory questionnaire data
    const { seedQuestionnaireData } = require('./in-memory-questionnaire-store');
    await seedQuestionnaireData();
    
    // Import routes
    const applicationRoutes = require('./routes/applications');
    const siteVisitRoutes = require('./routes/site-visits');
    const interviewRoutes = require('./routes/interviews');
    const questionnaireRoutes = require('../../questionnaire-routes');
    
    // Initialize routes
    app.use('/api/applications', applicationRoutes);
    app.use('/api/site-visits', siteVisitRoutes);
    app.use('/api/interviews', interviewRoutes);
    app.use('/api/questionnaires', questionnaireRoutes);
    
    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error('API Error:', err);
      res.status(500).json({ 
        error: 'Server error', 
        message: err.message 
      });
    });
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    }).on('error', (err) => {
      console.error('Server error:', err);
    });
  } catch (err) {
    console.error('Server initialization failed:', err);
    process.exit(1);
  }
}

// Start the server
initializeServer();
