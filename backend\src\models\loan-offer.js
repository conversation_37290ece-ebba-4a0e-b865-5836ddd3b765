const mongoose = require('mongoose');

const repaymentScheduleSchema = new mongoose.Schema({
  paymentNumber: Number,
  dueDate: Date,
  principalAmount: Number,
  interestAmount: Number,
  totalAmount: Number,
  balance: Number
});

const loanOfferSchema = new mongoose.Schema({
  offerId: {
    type: String,
    required: true,
    unique: true
  },
  applicationId: {
    type: String,
    required: true,
    ref: 'Application'
  },
  productId: {
    type: String,
    required: true,
    ref: 'LoanProduct'
  },
  pricingRuleId: {
    type: String,
    ref: 'PricingRule'
  },
  offeredAmount: {
    type: Number,
    required: true,
    min: 0
  },
  approvedAmount: {
    type: Number,
    min: 0
  },
  interestRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  term: {
    type: Number,
    required: true,
    min: 1
  },
  termUnit: {
    type: String,
    enum: ['DAYS', 'MONTHS', 'YEARS'],
    default: 'MONTHS'
  },
  repaymentFrequency: {
    type: String,
    enum: ['DAILY', 'WEEKLY', 'BI_WEEKLY', 'MONTHLY', 'QUARTERLY', 'SEMI_ANNUALLY', 'ANNUALLY'],
    default: 'MONTHLY'
  },
  monthlyPayment: {
    type: Number,
    required: true
  },
  totalRepayment: {
    type: Number,
    required: true
  },
  totalInterest: {
    type: Number,
    required: true
  },
  fees: {
    originationFee: {
      type: Number,
      default: 0
    },
    processingFee: {
      type: Number,
      default: 0
    },
    otherFees: [{
      name: String,
      amount: Number
    }]
  },
  collateralRequired: {
    type: Boolean,
    default: false
  },
  collateralDetails: {
    type: String,
    description: String,
    estimatedValue: Number
  },
  specialConditions: [String],
  repaymentSchedule: [repaymentScheduleSchema],
  riskAssessment: {
    creditScore: Number,
    riskRating: {
      type: String,
      enum: ['LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']
    },
    riskFactors: [{
      factor: String,
      score: Number,
      impact: String
    }]
  },
  status: {
    type: String,
    enum: ['DRAFT', 'PENDING', 'SENT', 'ACCEPTED', 'REJECTED', 'EXPIRED', 'WITHDRAWN'],
    default: 'DRAFT'
  },
  validUntil: {
    type: Date,
    required: true
  },
  sentDate: Date,
  acceptedDate: Date,
  rejectedDate: Date,
  rejectionReason: String,
  approvalWorkflow: {
    currentStep: {
      type: String,
      enum: ['INITIAL_REVIEW', 'CREDIT_ANALYSIS', 'RISK_ASSESSMENT', 'PRICING_APPROVAL', 'FINAL_APPROVAL', 'COMPLETED']
    },
    steps: [{
      step: String,
      status: {
        type: String,
        enum: ['PENDING', 'IN_PROGRESS', 'APPROVED', 'REJECTED']
      },
      assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      completedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      startDate: Date,
      completionDate: Date,
      comments: String
    }]
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Generate offer ID if not provided
loanOfferSchema.pre('save', async function(next) {
  if (!this.offerId) {
    const count = await this.constructor.countDocuments();
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    this.offerId = `OFFER-${year}${month}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Calculate repayment schedule before saving
loanOfferSchema.pre('save', function(next) {
  if (this.isModified('offeredAmount') || this.isModified('interestRate') || this.isModified('term')) {
    this.calculateRepaymentSchedule();
  }
  next();
});

// Method to calculate repayment schedule
loanOfferSchema.methods.calculateRepaymentSchedule = function() {
  const principal = this.offeredAmount;
  const monthlyRate = this.interestRate / 100 / 12;
  const numberOfPayments = this.term;
  
  // Calculate monthly payment using amortization formula
  const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
                        (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
  
  this.monthlyPayment = Math.round(monthlyPayment * 100) / 100;
  
  // Generate repayment schedule
  let balance = principal;
  const schedule = [];
  let totalInterest = 0;
  
  for (let i = 1; i <= numberOfPayments; i++) {
    const interestPayment = balance * monthlyRate;
    const principalPayment = monthlyPayment - interestPayment;
    balance -= principalPayment;
    
    schedule.push({
      paymentNumber: i,
      dueDate: new Date(Date.now() + (i * 30 * 24 * 60 * 60 * 1000)), // Approximate monthly
      principalAmount: Math.round(principalPayment * 100) / 100,
      interestAmount: Math.round(interestPayment * 100) / 100,
      totalAmount: Math.round(monthlyPayment * 100) / 100,
      balance: Math.round(balance * 100) / 100
    });
    
    totalInterest += interestPayment;
  }
  
  this.repaymentSchedule = schedule;
  this.totalInterest = Math.round(totalInterest * 100) / 100;
  this.totalRepayment = Math.round((principal + totalInterest) * 100) / 100;
};

const LoanOffer = mongoose.model('LoanOffer', loanOfferSchema);

module.exports = LoanOffer;