const mongoose = require('mongoose');
const { Schema } = mongoose;

// Schema for template criteria
const templateCriteriaSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  weight: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  maxScore: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  scoreType: {
    type: String,
    enum: ['numeric', 'boolean', 'scale', 'text'],
    default: 'numeric'
  },
  options: [{
    type: String,
    trim: true
  }],
  required: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  }
});

// Main scorecard template schema
const scorecardTemplateSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  category: {
    type: String,
    required: true,
    enum: [
      // Main Stages
      'onboarding',
      'business-case-review',
      'due-diligence',
      'assessment-report',
      'application-approval',
      
      // Sub-stages - Onboarding
      'beneficiary-registration',
      'pre-screening',
      
      // Sub-stages - Business Case Review
      'document-collection',
      'desktop-analysis',
      'data-validation',
      
      // Sub-stages - Due Diligence
      'sme-interview',
      'site-visit',
      
      // Sub-stages - Assessment Report
      'report-completion',
      'report-quality-check',
      'report-review',
      
      // Sub-stages - Application Approval
      'committee-review',
      'final-decision',
      'approval-documentation',
      'corporate-approval-1',
      'corporate-approval-2',
      'corporate-approval-3',
      'corporate-approval-4',
      
      // Custom category
      'custom'
    ]
  },
  version: {
    type: Number,
    default: 1,
    min: 1
  },
  isActive: {
    type: Boolean,
    default: true
  },
  criteria: [templateCriteriaSchema],
  
  // Template settings
  allowCustomCriteria: {
    type: Boolean,
    default: true
  },
  requireAllCriteria: {
    type: Boolean,
    default: false
  },
  passingScore: {
    type: Number,
    min: 0,
    max: 100
  },
  
  // Associated stages/substages where this template can be used
  applicableStages: [{
    type: String,
    trim: true
  }],
  applicableSubstages: [{
    type: String,
    trim: true
  }],
  
  // Metadata
  createdBy: {
    type: String,
    trim: true
  },
  updatedBy: {
    type: String,
    trim: true
  },
  
  // Usage tracking
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  lastUsed: {
    type: Date
  },
  
  // NEW: Programme Association Fields
  fundingProgrammes: [{
    type: Schema.Types.ObjectId,
    ref: 'FundingProgramme'
  }],
  
  // NEW: Template Scope
  templateScope: {
    type: String,
    enum: ['programme-specific', 'shared', 'global'],
    default: 'global'
  },
  
  // NEW: Programme-specific customizations
  programmeCustomizations: [{
    programmeId: {
      type: Schema.Types.ObjectId,
      ref: 'FundingProgramme'
    },
    customName: String,
    customDescription: String,
    customCriteria: [{
      criterionId: String,
      customWeight: Number,
      customDescription: String,
      isHidden: { type: Boolean, default: false }
    }],
    customPassingScore: Number
  }],
  
  // NEW: Template hierarchy
  parentTemplateId: {
    type: Schema.Types.ObjectId,
    ref: 'ScorecardTemplate'
  },
  
  // NEW: Usage analytics per programme
  programmeUsageStats: [{
    programmeId: {
      type: Schema.Types.ObjectId,
      ref: 'FundingProgramme'
    },
    usageCount: { type: Number, default: 0 },
    lastUsed: Date,
    averageScore: Number
  }],
  
  // NEW: Programme-specific stage mappings
  programmeStageMapping: [{
    programmeId: {
      type: Schema.Types.ObjectId,
      ref: 'FundingProgramme'
    },
    applicableStages: [String],
    applicableSubstages: [String]
  }]
}, {
  timestamps: true, // Automatically adds createdAt and updatedAt
  collection: 'scorecard_templates'
});

// Indexes for better query performance
scorecardTemplateSchema.index({ category: 1, isActive: 1 });
scorecardTemplateSchema.index({ name: 'text', description: 'text' });
scorecardTemplateSchema.index({ createdAt: -1 });
scorecardTemplateSchema.index({ usageCount: -1 });

// NEW: Compound indexes for programme-based querying
scorecardTemplateSchema.index({ fundingProgrammes: 1, category: 1, isActive: 1 });
scorecardTemplateSchema.index({ templateScope: 1, isActive: 1 });
scorecardTemplateSchema.index({ 'programmeUsageStats.programmeId': 1 });

// Virtual for total criteria weight validation
scorecardTemplateSchema.virtual('totalWeight').get(function() {
  return this.criteria.reduce((total, criterion) => total + criterion.weight, 0);
});

// Pre-save middleware for validation
scorecardTemplateSchema.pre('save', function(next) {
  // Validate total weight equals 100%
  const totalWeight = this.criteria.reduce((total, criterion) => total + criterion.weight, 0);
  if (Math.abs(totalWeight - 100) > 0.01) {
    const error = new Error(`Total criteria weight must equal 100% (current: ${totalWeight}%)`);
    error.name = 'ValidationError';
    return next(error);
  }
  
  // Ensure criteria have unique names within the template
  const criteriaNames = this.criteria.map(c => c.name.toLowerCase());
  const duplicates = criteriaNames.filter((name, index) => criteriaNames.indexOf(name) !== index);
  if (duplicates.length > 0) {
    const error = new Error('Duplicate criterion names are not allowed within a template');
    error.name = 'ValidationError';
    return next(error);
  }
  
  // Set order for criteria if not set
  this.criteria.forEach((criterion, index) => {
    if (!criterion.order) {
      criterion.order = index + 1;
    }
  });
  
  next();
});

// Instance methods
scorecardTemplateSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

scorecardTemplateSchema.methods.validateCriteria = function() {
  const errors = [];
  const warnings = [];
  
  // Check if template has criteria
  if (!this.criteria || this.criteria.length === 0) {
    errors.push('Template must have at least one criterion');
  }
  
  // Check total weight
  const totalWeight = this.criteria.reduce((total, criterion) => total + criterion.weight, 0);
  if (Math.abs(totalWeight - 100) > 0.01) {
    errors.push(`Total criteria weight must equal 100% (current: ${totalWeight}%)`);
  }
  
  // Check individual criteria
  this.criteria.forEach((criterion, index) => {
    if (!criterion.name || criterion.name.trim().length === 0) {
      errors.push(`Criterion ${index + 1}: Name is required`);
    }
    
    if (criterion.weight <= 0 || criterion.weight > 100) {
      errors.push(`Criterion ${index + 1}: Weight must be between 1 and 100`);
    }
    
    if (criterion.maxScore <= 0) {
      errors.push(`Criterion ${index + 1}: Maximum score must be greater than 0`);
    }
  });
  
  // Warnings
  if (this.criteria.length > 10) {
    warnings.push('Templates with more than 10 criteria may be difficult to use');
  }
  
  if (this.passingScore && this.passingScore > 90) {
    warnings.push('Very high passing scores may be difficult to achieve');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Static methods
scorecardTemplateSchema.statics.findByCategory = function(category) {
  return this.find({ category, isActive: true }).sort({ usageCount: -1, name: 1 });
};

scorecardTemplateSchema.statics.searchTemplates = function(query) {
  return this.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { category: { $regex: query, $options: 'i' } }
        ]
      }
    ]
  }).sort({ usageCount: -1, name: 1 });
};

scorecardTemplateSchema.statics.getMostUsed = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ usageCount: -1, name: 1 })
    .limit(limit);
};

scorecardTemplateSchema.statics.getUsageStats = function(templateId) {
  // This would typically aggregate data from actual scorecard usage
  // For now, return basic template info
  return this.findById(templateId).select('name usageCount lastUsed category');
};

// Create and export the model
const ScorecardTemplate = mongoose.model('ScorecardTemplate', scorecardTemplateSchema);

module.exports = ScorecardTemplate;