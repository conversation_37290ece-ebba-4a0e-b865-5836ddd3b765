const express = require('express');
const router = express.Router();
const SME = require('../models/sme');
const CorporateSponsor = require('../models/corporate-sponsor');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Get all SMEs with filtering and pagination
router.get('/', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    let query = {};
    
    // Handle search
    if (req.query.search) {
      query.$or = [
        { businessName: { $regex: req.query.search, $options: 'i' } },
        { registrationNumber: { $regex: req.query.search, $options: 'i' } },
        { businessDescription: { $regex: req.query.search, $options: 'i' } },
        { industry: { $regex: req.query.search, $options: 'i' } },
        { sector: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // Handle filters
    if (req.query.businessType) {
      query.businessType = req.query.businessType;
    }
    
    if (req.query.industry) {
      query.industry = { $regex: req.query.industry, $options: 'i' };
    }
    
    if (req.query.sector) {
      query.sector = { $regex: req.query.sector, $options: 'i' };
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.verificationStatus) {
      query.verificationStatus = req.query.verificationStatus;
    }
    
    if (req.query.corporateSponsorId) {
      query.corporateSponsorId = req.query.corporateSponsorId;
    }
    
    if (req.query.isExxaroSupplier !== undefined) {
      query.isExxaroSupplier = req.query.isExxaroSupplier === 'true';
    }
    
    // Handle employee count range
    if (req.query.minEmployees || req.query.maxEmployees) {
      query.numberOfEmployees = {};
      if (req.query.minEmployees) {
        query.numberOfEmployees.$gte = parseInt(req.query.minEmployees);
      }
      if (req.query.maxEmployees) {
        query.numberOfEmployees.$lte = parseInt(req.query.maxEmployees);
      }
    }
    
    // Handle turnover range
    if (req.query.minTurnover || req.query.maxTurnover) {
      query.annualTurnover = {};
      if (req.query.minTurnover) {
        query.annualTurnover.$gte = parseFloat(req.query.minTurnover);
      }
      if (req.query.maxTurnover) {
        query.annualTurnover.$lte = parseFloat(req.query.maxTurnover);
      }
    }
    
    const smes = await SME.find(query)
      .populate('corporateSponsorId', 'name industry')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);
    
    const total = await SME.countDocuments(query);
    
    res.json({
      smes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error('Error fetching SMEs:', err);
    res.status(500).json({ message: 'Error fetching SMEs', error: err.message });
  }
});

// Get a specific SME by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const sme = await SME.findById(req.params.id)
      .populate('corporateSponsorId', 'name industry contactPerson');
    
    if (!sme) {
      return res.status(404).json({ message: 'SME not found' });
    }
    
    res.json(sme);
  } catch (err) {
    console.error('Error fetching SME:', err);
    res.status(500).json({ message: 'Error fetching SME', error: err.message });
  }
});

// Create a new SME
router.post('/', authenticateToken, async (req, res) => {
  try {
    // Validate required fields
    const { businessName } = req.body;
    
    if (!businessName) {
      return res.status(400).json({ 
        message: 'Business name is required' 
      });
    }
    
    // Check if registration number is unique (if provided)
    if (req.body.registrationNumber) {
      const existingSME = await SME.findOne({ registrationNumber: req.body.registrationNumber });
      if (existingSME) {
        return res.status(400).json({ message: 'Registration number already exists' });
      }
    }
    
    // Validate Corporate Sponsor if provided
    if (req.body.corporateSponsorId) {
      const corporateSponsor = await CorporateSponsor.findById(req.body.corporateSponsorId);
      if (!corporateSponsor) {
        return res.status(400).json({ message: 'Corporate Sponsor not found' });
      }
    }
    
    // Generate unique SME ID
    const count = await SME.countDocuments();
    const smeId = `SME-${new Date().getFullYear()}-${(count + 1).toString().padStart(4, '0')}`;
    
    const sme = new SME({
      ...req.body,
      id: smeId
    });
    
    const savedSME = await sme.save();
    
    // Populate the response
    const populatedSME = await SME.findById(savedSME._id)
      .populate('corporateSponsorId', 'name industry');
    
    res.status(201).json(populatedSME);
  } catch (err) {
    console.error('Error creating SME:', err);
    res.status(400).json({ message: 'Error creating SME', error: err.message });
  }
});

// Update an SME
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const sme = await SME.findById(req.params.id);
    
    if (!sme) {
      return res.status(404).json({ message: 'SME not found' });
    }
    
    // Check if registration number is unique (if being updated)
    if (req.body.registrationNumber && req.body.registrationNumber !== sme.registrationNumber) {
      const existingSME = await SME.findOne({ 
        registrationNumber: req.body.registrationNumber,
        _id: { $ne: req.params.id }
      });
      if (existingSME) {
        return res.status(400).json({ message: 'Registration number already exists' });
      }
    }
    
    // Validate Corporate Sponsor if being updated
    if (req.body.corporateSponsorId && req.body.corporateSponsorId !== sme.corporateSponsorId?.toString()) {
      const corporateSponsor = await CorporateSponsor.findById(req.body.corporateSponsorId);
      if (!corporateSponsor) {
        return res.status(400).json({ message: 'Corporate Sponsor not found' });
      }
    }
    
    // Update SME
    Object.assign(sme, req.body);
    sme.updatedAt = new Date();
    
    const updatedSME = await sme.save();
    
    // Populate the response
    const populatedSME = await SME.findById(updatedSME._id)
      .populate('corporateSponsorId', 'name industry');
    
    res.json(populatedSME);
  } catch (err) {
    console.error('Error updating SME:', err);
    res.status(400).json({ message: 'Error updating SME', error: err.message });
  }
});

// Delete an SME
router.delete('/:id', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const sme = await SME.findById(req.params.id);
    
    if (!sme) {
      return res.status(404).json({ message: 'SME not found' });
    }
    
    await SME.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'SME deleted successfully' });
  } catch (err) {
    console.error('Error deleting SME:', err);
    res.status(500).json({ message: 'Error deleting SME', error: err.message });
  }
});

// Update SME verification status
router.patch('/:id/verification', authenticateToken, authorizeRoles(['admin', 'manager', 'analyst']), async (req, res) => {
  try {
    const { verificationStatus } = req.body;
    
    if (!['pending', 'verified', 'rejected'].includes(verificationStatus)) {
      return res.status(400).json({ message: 'Invalid verification status' });
    }
    
    const sme = await SME.findById(req.params.id);
    
    if (!sme) {
      return res.status(404).json({ message: 'SME not found' });
    }
    
    sme.verificationStatus = verificationStatus;
    sme.updatedAt = new Date();
    
    const updatedSME = await sme.save();
    
    // Populate the response
    const populatedSME = await SME.findById(updatedSME._id)
      .populate('corporateSponsorId', 'name industry');
    
    res.json(populatedSME);
  } catch (err) {
    console.error('Error updating SME verification status:', err);
    res.status(500).json({ message: 'Error updating SME verification status', error: err.message });
  }
});

// Get SME statistics
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    const totalSMEs = await SME.countDocuments();
    const verifiedSMEs = await SME.countDocuments({ verificationStatus: 'verified' });
    const activeSMEs = await SME.countDocuments({ status: 'active' });
    const smesByType = await SME.aggregate([
      { $group: { _id: '$businessType', count: { $sum: 1 } } }
    ]);
    const smesByIndustry = await SME.aggregate([
      { $group: { _id: '$industry', count: { $sum: 1 } } }
    ]);
    const smesBySector = await SME.aggregate([
      { $group: { _id: '$sector', count: { $sum: 1 } } }
    ]);
    
    res.json({
      totalSMEs,
      verifiedSMEs,
      activeSMEs,
      verificationRate: totalSMEs > 0 ? (verifiedSMEs / totalSMEs * 100).toFixed(2) : 0,
      smesByType,
      smesByIndustry,
      smesBySector
    });
  } catch (err) {
    console.error('Error fetching SME statistics:', err);
    res.status(500).json({ message: 'Error fetching SME statistics', error: err.message });
  }
});

// Get business types for dropdown
router.get('/options/business-types', authenticateToken, async (req, res) => {
  try {
    const businessTypes = [
      'Sole Proprietorship',
      'Partnership',
      'Private Company',
      'Public Company',
      'Close Corporation',
      'Trust',
      'NPO',
      'Other'
    ];
    
    res.json(businessTypes);
  } catch (err) {
    console.error('Error fetching business types:', err);
    res.status(500).json({ message: 'Error fetching business types', error: err.message });
  }
});

module.exports = router;
