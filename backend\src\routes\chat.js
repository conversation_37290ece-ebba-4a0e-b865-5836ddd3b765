const express = require('express');
const router = express.Router();
const ChatService = require('../services/chat-service');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/chat/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Create a new chat room
router.post('/rooms', authenticateToken, async (req, res) => {
  try {
    const room = await ChatService.createRoom(req.body, req.user.sub);
    res.status(201).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error creating room:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get user's chat rooms
router.get('/rooms', authenticateToken, async (req, res) => {
  try {
    const { includeArchived, type } = req.query;
    const rooms = await ChatService.getUserRooms(req.user.sub, {
      includeArchived: includeArchived === 'true',
      type
    });
    
    res.json({
      success: true,
      data: rooms
    });
  } catch (error) {
    console.error('Error getting user rooms:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get room details
router.get('/rooms/:roomId', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const rooms = await ChatService.getUserRooms(req.user.sub);
    const room = rooms.find(r => r._id.toString() === roomId);
    
    if (!room) {
      return res.status(404).json({
        success: false,
        error: 'Room not found or access denied'
      });
    }
    
    res.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error getting room details:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update room settings
router.put('/rooms/:roomId', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const room = await ChatService.updateRoomSettings(roomId, req.user.sub, req.body);
    
    res.json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Error updating room:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get room participants
router.get('/rooms/:roomId/participants', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const participants = await ChatService.getRoomParticipants(roomId, req.user.sub);
    
    res.json({
      success: true,
      data: participants
    });
  } catch (error) {
    console.error('Error getting participants:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Add participant to room
router.post('/rooms/:roomId/participants', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { userId, role } = req.body;
    
    const participant = await ChatService.addParticipant(
      roomId,
      userId,
      role,
      req.user.sub
    );
    
    res.status(201).json({
      success: true,
      data: participant
    });
  } catch (error) {
    console.error('Error adding participant:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Remove participant from room
router.delete('/rooms/:roomId/participants/:userId', authenticateToken, async (req, res) => {
  try {
    const { roomId, userId } = req.params;
    
    const participant = await ChatService.removeParticipant(
      roomId,
      userId,
      req.user.sub
    );
    
    res.json({
      success: true,
      data: participant
    });
  } catch (error) {
    console.error('Error removing participant:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get room messages
router.get('/rooms/:roomId/messages', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { limit, before, after, includeDeleted } = req.query;
    
    const messages = await ChatService.getRoomMessages(roomId, req.user.sub, {
      limit: parseInt(limit) || 50,
      before,
      after,
      includeDeleted: includeDeleted === 'true'
    });
    
    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Error getting messages:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Send message
router.post('/rooms/:roomId/messages', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const messageData = {
      roomId,
      ...req.body
    };
    
    const message = await ChatService.sendMessage(messageData, req.user.sub);
    
    res.status(201).json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Send message with file attachment
router.post('/rooms/:roomId/messages/file', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    const { roomId } = req.params;
    const { content, replyTo, mentions } = req.body;
    
    const attachment = {
      fileName: req.file.originalname,
      fileUrl: `/uploads/chat/${req.file.filename}`,
      fileType: req.file.mimetype,
      fileSize: req.file.size
    };
    
    const messageData = {
      roomId,
      content: {
        text: content || '',
        type: 'FILE'
      },
      attachments: [attachment],
      replyTo,
      mentions: mentions ? JSON.parse(mentions) : []
    };
    
    const message = await ChatService.sendMessage(messageData, req.user.sub);
    
    res.status(201).json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error sending file message:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Search messages in room
router.get('/rooms/:roomId/messages/search', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { q, limit, includeDeleted } = req.query;
    
    if (!q) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }
    
    const messages = await ChatService.searchMessages(
      roomId,
      req.user.sub,
      q,
      {
        limit: parseInt(limit) || 50,
        includeDeleted: includeDeleted === 'true'
      }
    );
    
    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Error searching messages:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Edit message
router.put('/messages/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const { content } = req.body;
    
    const ChatMessage = require('../models/chat-message');
    const message = await ChatMessage.findById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }
    
    await message.editMessage(content, req.user.sub);
    
    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error editing message:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Delete message
router.delete('/messages/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    
    const ChatMessage = require('../models/chat-message');
    const message = await ChatMessage.findById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }
    
    await message.deleteMessage(req.user.sub);
    
    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Mark message as read
router.post('/messages/:messageId/read', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    
    const ChatMessage = require('../models/chat-message');
    const message = await ChatMessage.findById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }
    
    await message.markAsReadBy(req.user.sub);
    
    res.json({
      success: true,
      data: { read: true }
    });
  } catch (error) {
    console.error('Error marking message as read:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Add reaction to message
router.post('/messages/:messageId/reactions', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const { emoji } = req.body;
    
    if (!emoji) {
      return res.status(400).json({
        success: false,
        error: 'Emoji is required'
      });
    }
    
    const ChatMessage = require('../models/chat-message');
    const message = await ChatMessage.findById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }
    
    await message.addReaction(req.user.sub, emoji);
    
    res.json({
      success: true,
      data: message.reactions
    });
  } catch (error) {
    console.error('Error adding reaction:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Remove reaction from message
router.delete('/messages/:messageId/reactions/:emoji', authenticateToken, async (req, res) => {
  try {
    const { messageId, emoji } = req.params;
    
    const ChatMessage = require('../models/chat-message');
    const message = await ChatMessage.findById(messageId);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found'
      });
    }
    
    await message.removeReaction(req.user.sub, emoji);
    
    res.json({
      success: true,
      data: message.reactions
    });
  } catch (error) {
    console.error('Error removing reaction:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Update participant preferences
router.put('/rooms/:roomId/preferences', authenticateToken, async (req, res) => {
  try {
    const { roomId } = req.params;
    const { preferences } = req.body;
    
    const ChatParticipant = require('../models/chat-participant');
    const participant = await ChatParticipant.findOne({
      roomId,
      userId: req.user.sub
    });
    
    if (!participant) {
      return res.status(404).json({
        success: false,
        error: 'Participant not found'
      });
    }
    
    Object.assign(participant.preferences, preferences);
    await participant.save();
    
    res.json({
      success: true,
      data: participant.preferences
    });
  } catch (error) {
    console.error('Error updating preferences:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get unread message count
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const ChatParticipant = require('../models/chat-participant');
    const participants = await ChatParticipant.find({
      userId: req.user.sub,
      isActive: true,
      unreadCount: { $gt: 0 }
    });
    
    const totalUnread = participants.reduce((sum, p) => sum + p.unreadCount, 0);
    
    res.json({
      success: true,
      data: {
        totalUnread,
        byRoom: participants.map(p => ({
          roomId: p.roomId,
          unreadCount: p.unreadCount
        }))
      }
    });
  } catch (error) {
    console.error('Error getting unread count:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get available users for creating chats
router.get('/users', authenticateToken, async (req, res) => {
  try {
    const User = require('../models/user');
    const users = await User.find(
      { isActive: true },
      'username firstName lastName email profilePicture'
    ).sort({ firstName: 1, lastName: 1 });
    
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;