const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const emailRecipientSchema = new Schema({
  email: { 
    type: String, 
    required: true,
    trim: true
  },
  name: { 
    type: String,
    trim: true
  },
  type: { 
    type: String, 
    enum: ['to', 'cc', 'bcc'],
    default: 'to'
  }
});

const scheduleConfigSchema = new Schema({
  frequency: { 
    type: String, 
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
    required: true
  },
  dayOfWeek: { 
    type: Number, 
    min: 0, 
    max: 6 
  },
  dayOfMonth: { 
    type: Number, 
    min: 1, 
    max: 31 
  },
  month: { 
    type: Number, 
    min: 0, 
    max: 11 
  },
  hour: { 
    type: Number, 
    required: true,
    min: 0, 
    max: 23 
  },
  minute: { 
    type: Number, 
    required: true,
    min: 0, 
    max: 59 
  },
  timezone: { 
    type: String,
    default: 'UTC'
  }
});

const deliveryConfigSchema = new Schema({
  method: { 
    type: String, 
    enum: ['email', 'download', 'api'],
    required: true
  },
  format: { 
    type: String, 
    enum: ['pdf', 'excel', 'csv'],
    required: true
  },
  recipients: [emailRecipientSchema],
  emailSubject: { 
    type: String,
    trim: true
  },
  emailBody: { 
    type: String,
    trim: true
  },
  apiEndpoint: { 
    type: String,
    trim: true
  },
  apiKey: { 
    type: String,
    trim: true
  }
});

const scheduleExecutionSchema = new Schema({
  executionId: { 
    type: String, 
    required: true
  },
  scheduledTime: { 
    type: Date, 
    required: true
  },
  executionTime: { 
    type: Date
  },
  status: { 
    type: String, 
    enum: ['pending', 'running', 'completed', 'failed'],
    default: 'pending'
  },
  reportUrl: { 
    type: String,
    trim: true
  },
  error: { 
    type: String,
    trim: true
  }
});

const scheduledReportSchema = new Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  reportId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Report',
    required: true
  },
  schedule: { 
    type: scheduleConfigSchema,
    required: true
  },
  delivery: { 
    type: deliveryConfigSchema,
    required: true
  },
  status: { 
    type: String, 
    enum: ['active', 'paused', 'completed', 'error'],
    default: 'active'
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  lastExecutionTime: { 
    type: Date
  },
  nextExecutionTime: { 
    type: Date
  },
  executions: [scheduleExecutionSchema]
}, { timestamps: true });

// Add text index for search functionality
scheduledReportSchema.index({ 
  name: 'text', 
  description: 'text'
});

// Virtual for URL
scheduledReportSchema.virtual('url').get(function() {
  return `/scheduled-reports/${this._id}`;
});

// Pre-save hook to calculate next execution time
scheduledReportSchema.pre('save', function(next) {
  if (this.status === 'active' && !this.nextExecutionTime) {
    this.calculateNextExecutionTime();
  }
  next();
});

// Method to calculate the next execution time based on schedule
scheduledReportSchema.methods.calculateNextExecutionTime = function() {
  const now = new Date();
  let nextExecution = new Date();
  
  // Set the hour and minute
  nextExecution.setHours(this.schedule.hour, this.schedule.minute, 0, 0);
  
  // Adjust based on frequency
  switch (this.schedule.frequency) {
    case 'daily':
      // If the time has already passed today, set it for tomorrow
      if (nextExecution <= now) {
        nextExecution.setDate(nextExecution.getDate() + 1);
      }
      break;
      
    case 'weekly':
      // Set to the specified day of week
      const currentDay = nextExecution.getDay();
      const targetDay = this.schedule.dayOfWeek || 0;
      const daysToAdd = (targetDay - currentDay + 7) % 7;
      
      nextExecution.setDate(nextExecution.getDate() + daysToAdd);
      
      // If the time has already passed this week, add another week
      if (daysToAdd === 0 && nextExecution <= now) {
        nextExecution.setDate(nextExecution.getDate() + 7);
      }
      break;
      
    case 'monthly':
      // Set to the specified day of month
      const targetDayOfMonth = this.schedule.dayOfMonth || 1;
      nextExecution.setDate(targetDayOfMonth);
      
      // If the day has already passed this month, set it for next month
      if (nextExecution <= now) {
        nextExecution.setMonth(nextExecution.getMonth() + 1);
      }
      break;
      
    case 'quarterly':
      // Set to the first day of the next quarter if the current quarter has passed
      const currentMonth = now.getMonth();
      const currentQuarter = Math.floor(currentMonth / 3);
      const nextQuarterStartMonth = (currentQuarter + 1) % 4 * 3;
      
      nextExecution.setMonth(nextQuarterStartMonth, 1);
      
      // If the next quarter date has already passed, add a year
      if (nextExecution <= now) {
        nextExecution.setFullYear(nextExecution.getFullYear() + 1);
      }
      break;
      
    case 'yearly':
      // Set to the specified month and day
      const targetMonth = this.schedule.month || 0;
      nextExecution.setMonth(targetMonth, this.schedule.dayOfMonth || 1);
      
      // If the date has already passed this year, set it for next year
      if (nextExecution <= now) {
        nextExecution.setFullYear(nextExecution.getFullYear() + 1);
      }
      break;
  }
  
  this.nextExecutionTime = nextExecution;
  return nextExecution;
};

module.exports = mongoose.model('ScheduledReport', scheduledReportSchema);
