# Stage & Workflow Management Quick Reference Guide

## Stage Hierarchy at a Glance

### Main Stages → Sub-Stages
```
1. ONBOARDING
   ├── BENEFICIARY_REGISTRATION
   └── PRE_SCREENING

2. BUSINESS_CASE_REVIEW
   ├── DOCUMENT_COLLECTION
   └── DESKTOP_ANALYSIS

3. DUE_DILIGENCE
   ├── DATA_VALIDATION
   ├── SME_INTERVIEW
   └── SITE_VISIT

4. ASSESSMENT_REPORT
   ├── REPORT_COMPLETION
   ├── REPORT_QUALITY_CHECK
   └── REPORT_REVIEW

5. APPLICATION_APPROVAL
   ├── CORPORATE_APPROVAL_1
   ├── CORPORATE_APPROVAL_2
   ├── CORPORATE_APPROVAL_3
   └── CORPORATE_APPROVAL_4
```

## Stage Statuses
- **NOT_STARTED** - Default state, work hasn't begun
- **ACTIVE** - Currently being worked on
- **COMPLETED** - All requirements met
- **SKIPPED** - Bypassed (with justification)
- **NOT_APPLICABLE** - Doesn't apply to this application

## Common Actions

### Update Stage Status
1. Open application → Stage Manager tab
2. Select sub-stage → Choose new status
3. Add notes → Click "Update Stage"

### View Audit Trail
1. Application details → Audit Trail tab
2. Filter by date/user if needed
3. Export for reporting

### Assign Workflow
1. Application details → Workflow tab
2. Click "Assign Workflow"
3. Select from list → Confirm

## Workflow Types
- **Sequential**: Stages completed in order
- **Parallel**: Multiple stages simultaneously
- **Conditional**: Path based on application data
- **Priority**: Expedited processing for urgent cases

## Valid Status Transitions
| From | To | Override Required |
|------|----|--------------------|
| NOT_STARTED | ACTIVE, SKIPPED, NOT_APPLICABLE | No |
| ACTIVE | COMPLETED, SKIPPED, NOT_APPLICABLE | No |
| COMPLETED | ACTIVE | Yes |
| SKIPPED | ACTIVE, NOT_STARTED | No |
| NOT_APPLICABLE | ACTIVE, NOT_STARTED | Yes |

## Key API Endpoints

### Stage Management
```
GET /api/v1/applications/{id}/stage-status
PUT /api/v1/applications/{id}/stage-hierarchy
GET /api/v1/applications/{id}/audit-trail
```

### Workflow Management
```
GET /api/v1/workflows
POST /api/v1/workflows/assign
GET /api/v1/workflows/application/{applicationId}
PUT /api/v1/workflows/application/{applicationId}/stage
```

## Troubleshooting Quick Fixes

### Stage Won't Update
- Check workflow rules for valid transitions
- Verify user permissions
- Look for application locks

### Missing Notifications
- Check automation rule status
- Verify email settings
- Review trigger conditions

### Slow Performance
- Use filters to reduce data load
- Clear browser cache
- Check database connection

## User Permissions Summary
| Role | Stage Updates | Workflow Management | Overrides |
|------|---------------|-------------------|-----------|
| Reviewer | Assigned only | View only | No |
| Senior Reviewer | Any stage | View only | Limited |
| Manager | Full access | Create/Edit | Yes |
| Administrator | Full access | Full access | Yes |

## Emergency Contacts
- **IT Help Desk**: [Contact Info]
- **System Administrator**: [Contact Info]
- **Business Process Owner**: [Contact Info]

---
*Keep this guide handy for quick reference. For detailed procedures, see the full User Manual.*
