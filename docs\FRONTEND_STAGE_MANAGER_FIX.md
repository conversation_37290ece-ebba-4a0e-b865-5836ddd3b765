# Frontend Stage Manager Display Issue Fix

## Problem Description
The application shows correct stage information in the applications list (Onboarding/Beneficiary Registration), but the Stage Manager tab displays "No stage information available for this application."

This indicates:
- ✅ Database has correct stage data
- ✅ Basic API endpoints are working
- ❌ Stage Manager component is not loading the stage hierarchy properly

## Root Cause Analysis

The issue is likely in the frontend component that loads the detailed stage hierarchy for the Stage Manager interface. The component may be:

1. **Making the wrong API call** for stage hierarchy data
2. **Not handling the response properly** from the stage hierarchy endpoint
3. **Missing permissions** to access detailed stage information
4. **Frontend caching issue** preventing updated data from loading
5. **Component state management issue**

## Diagnostic Steps

### Step 1: Check Browser Network Tab
1. Open browser Developer Tools (F12)
2. Go to Network tab
3. Navigate to APP-2025-001 → Application Stages tab
4. Look for API calls being made
5. Check if any calls are failing (red status codes)

**Expected API calls:**
- `GET /api/v1/applications/APP-2025-001` (basic application data)
- `GET /api/v1/applications/APP-2025-001/stage-hierarchy` (detailed stage data)

### Step 2: Check API Response
Test the stage hierarchy endpoint directly:

```bash
# Test if the detailed stage endpoint works
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**Look for in the response:**
```json
{
  "id": "APP-2025-001",
  "currentMainStage": "ONBOARDING",
  "currentSubStage": "BENEFICIARY_REGISTRATION",
  "currentStageStatus": "NOT_STARTED",
  "stageHierarchy": [
    {
      "mainStage": "ONBOARDING",
      "status": "NOT_STARTED",
      "subStages": [...]
    }
  ]
}
```

### Step 3: Check Browser Console
1. Open browser console
2. Navigate to Stage Manager tab
3. Look for JavaScript errors
4. Check if stage data is being loaded

```javascript
// Run this in browser console to check stage data
console.log('Current application data:', window.applicationData);
console.log('Stage hierarchy:', window.applicationData?.stageHierarchy);
```

## Fixes

### Fix 1: Clear Browser Cache
```bash
# Clear browser cache completely
# In Chrome: Ctrl+Shift+Delete → Clear all data
# Or try incognito/private browsing mode
```

### Fix 2: Check API Endpoint Implementation
The issue might be that the Stage Manager component is calling a different endpoint. Check if this endpoint exists:

```bash
# Test stage hierarchy specific endpoint
curl -X GET "http://localhost:3000/api/v1/applications/APP-2025-001/stage-hierarchy" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

If this endpoint doesn't exist, we need to add it to the backend:

```javascript
// Add to backend/src/routes/applications.js
router.get('/:id/stage-hierarchy', authenticateToken, async (req, res) => {
  try {
    const application = await Application.findOne({ id: req.params.id })
      .select('id stageHierarchy currentMainStage currentSubStage currentStageStatus')
      .lean();
    
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }
    
    if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
      return res.status(404).json({ message: 'No stage hierarchy found' });
    }
    
    res.json({
      applicationId: application.id,
      currentStage: {
        mainStage: application.currentMainStage,
        subStage: application.currentSubStage,
        status: application.currentStageStatus
      },
      stageHierarchy: application.stageHierarchy
    });
  } catch (error) {
    console.error('Error fetching stage hierarchy:', error);
    res.status(500).json({ message: 'Failed to load stage hierarchy' });
  }
});
```

### Fix 3: Frontend Component Fix
The Stage Manager component might need to be updated to handle the stage hierarchy data properly. Check if the component is looking for the data in the right place:

```javascript
// Frontend component should check for stageHierarchy in the application data
const StageManager = ({ applicationId }) => {
  const [stageData, setStageData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStageData = async () => {
      try {
        setLoading(true);
        
        // Try the main application endpoint first
        const response = await fetch(`/api/v1/applications/${applicationId}`);
        const data = await response.json();
        
        if (data.stageHierarchy && data.stageHierarchy.length > 0) {
          setStageData(data);
          setError(null);
        } else {
          setError('No stage information available for this application');
        }
      } catch (err) {
        console.error('Error loading stage data:', err);
        setError('Failed to load stage information');
      } finally {
        setLoading(false);
      }
    };

    fetchStageData();
  }, [applicationId]);

  if (loading) return <div>Loading stage information...</div>;
  if (error) return <div>{error}</div>;
  if (!stageData?.stageHierarchy) return <div>No stage information available</div>;

  return (
    <div className="stage-manager">
      {/* Render stage hierarchy */}
      {stageData.stageHierarchy.map(mainStage => (
        <div key={mainStage.mainStage}>
          <h3>{mainStage.mainStage}</h3>
          {mainStage.subStages.map(subStage => (
            <div key={subStage.subStage}>
              {subStage.subStage}: {subStage.status.status}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};
```

### Fix 4: Restart Backend Services
```bash
# Restart the backend to ensure all changes are loaded
cd backend
npm restart

# Or if using PM2
pm2 restart screening-portal-backend

# Or if using Docker
docker-compose restart backend
```

### Fix 5: Check User Permissions
The user might not have permissions to view detailed stage information:

```javascript
// Check user permissions in browser console
console.log('User permissions:', window.userPermissions);
console.log('User role:', window.userRole);
```

Make sure the user has the appropriate permissions to view stage details.

## Verification Steps

After applying fixes:

1. **Clear browser cache** completely
2. **Refresh the page**
3. **Navigate to APP-2025-001 → Application Stages tab**
4. **Check if Stage Manager now shows the hierarchy**

Expected result: You should see a detailed stage hierarchy interface with:
- 5 main stages (ONBOARDING, BUSINESS_CASE_REVIEW, etc.)
- Sub-stages under each main stage
- Status indicators for each stage
- Ability to update stage statuses

## Additional Debugging

### Enable Debug Logging
Add this to the backend to see what's happening:

```javascript
// In applications.js route handler
router.get('/:id', async (req, res) => {
  console.log(`Fetching application: ${req.params.id}`);
  
  const application = await Application.findOne({ id: req.params.id });
  
  console.log('Application found:', !!application);
  console.log('Has stageHierarchy:', !!application?.stageHierarchy);
  console.log('StageHierarchy length:', application?.stageHierarchy?.length || 0);
  
  // ... rest of the handler
});
```

### Frontend Debug Logging
Add this to the Stage Manager component:

```javascript
useEffect(() => {
  console.log('Stage Manager component mounted for:', applicationId);
  
  fetch(`/api/v1/applications/${applicationId}`)
    .then(response => {
      console.log('API response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('Application data received:', data);
      console.log('Stage hierarchy present:', !!data.stageHierarchy);
      console.log('Stage hierarchy length:', data.stageHierarchy?.length || 0);
    })
    .catch(error => {
      console.error('API call failed:', error);
    });
}, [applicationId]);
```

## Contact Support

If the issue persists after trying these fixes:

1. **Provide browser console logs** showing any errors
2. **Provide network tab screenshots** showing API calls
3. **Confirm which fixes were attempted**
4. **Include user role and permissions information**

The issue is likely a frontend component or API endpoint problem rather than a database issue, since the basic stage information is displaying correctly in the applications list.
