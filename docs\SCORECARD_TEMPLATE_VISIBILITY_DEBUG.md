# Scorecard Template Visibility Debug Guide

## Issue Description
User created a scorecard template but can't see it anymore in the scorecard management interface.

## Debug Analysis

### Potential Sources Identified:
1. **Frontend State Management Issue** - Template list not refreshing after creation
2. **API Response Handling** - Backend not returning the created template properly
3. **Caching/Mock Data Issue** - Service returning cached/mock data instead of fresh data
4. **Database Persistence Issue** - Template not saving correctly to database
5. **Component Communication Issue** - Template form not properly emitting saved template
6. **Timing/Race Condition** - List loading before creation is complete
7. **Error Handling Silently Failing** - Errors occurring but not displayed

### Most Likely Causes:
Based on code analysis, the two most likely causes are:

1. **Frontend State Management Issue**: The `onTemplateSaved` method updates the local array but doesn't refresh from the server, which could lead to inconsistencies.

2. **Mock Data Fallback**: The service has a fallback to mock data when API calls fail, which might be masking the real issue and showing stale data.

## Debug Logging Added

I've added comprehensive debug logging to help diagnose the issue:

### Frontend Components:

1. **scorecard-management.component.ts**:
   - Logs when templates are loaded
   - Logs when a template is saved
   - Logs the template list before and after save
   - Now triggers a refresh from server after save

2. **scorecard-template.service.ts**:
   - Logs API calls for getting templates
   - Logs API responses
   - Logs when falling back to mock data
   - Logs template creation requests and responses

3. **template-form.component.ts**:
   - Logs form submission
   - Logs validation results
   - Logs successful creation/update events

### Backend API:

1. **scorecard-templates.js**:
   - Logs GET requests with query parameters
   - Logs number of templates found
   - Logs POST requests with full request body
   - Logs validation results
   - Logs successful saves with template ID

## How to Test and Confirm Diagnosis

Please follow these steps to help diagnose the issue:

### 1. Open Browser Developer Console
- Press F12 or right-click and select "Inspect"
- Go to the "Console" tab
- Clear the console

### 2. Create a New Template
1. Navigate to Scorecard Management
2. Fill out the template form with:
   - Name: "Test Debug Template"
   - Stage: Select any main stage
   - Sub-Stage: Select any sub-stage
   - Add at least one criterion with weight totaling 100%
3. Click "Create Template"

### 3. Check Console Logs
Look for the following log patterns:

```
[DEBUG] TemplateForm.onSubmit called
[DEBUG] Form valid: true Total weight: 100
[DEBUG] Creating new template
[DEBUG] ScorecardTemplateService.createTemplate called with: {...}
[DEBUG] Template created successfully: {...}
[DEBUG] onTemplateSaved called with template: {...}
[DEBUG] Templates after save: [number] [...]
[DEBUG] Triggering refresh from server...
[DEBUG] ScorecardTemplateService.getTemplates called
[DEBUG] API returned templates: [number] [...]
```

### 4. Check Network Tab
1. Go to the "Network" tab in Developer Tools
2. Look for:
   - POST request to `/api/v1/scorecard-templates`
   - GET request to `/api/v1/scorecard-templates` (after save)
3. Check the response status and data

### 5. Check for Errors
Look for any of these error patterns:
- Red error messages in console
- Failed network requests (4xx or 5xx status codes)
- "[DEBUG] API call failed" messages
- "[DEBUG] Falling back to mock templates" messages

## Questions to Answer:

1. **Do you see the template in the console logs after creation?**
   - Check the `[DEBUG] Template created successfully` log
   - Note the template ID

2. **Does the GET request after save include your new template?**
   - Check the `[DEBUG] API returned templates` log
   - Count if the number increased

3. **Are there any error messages?**
   - Any red console errors?
   - Any failed network requests?

4. **Is the service falling back to mock data?**
   - Look for `[DEBUG] Falling back to mock templates` message

5. **What's the backend response?**
   - Check the backend logs/terminal for:
     - `[DEBUG] POST /api/v1/scorecard-templates - Request received`
     - `[DEBUG] Template saved successfully: [ID]`

## Temporary Fix

As an immediate workaround, I've modified the component to automatically refresh the template list from the server after saving. This should help ensure the new template appears.

## Next Steps

Based on your findings:

1. **If API is returning mock data**: Check backend connectivity and database connection
2. **If template saves but doesn't appear in GET**: Check database query filters
3. **If no errors but template missing**: Check for filtering logic hiding the template
4. **If network errors occur**: Check backend server status and error logs

Please run through these tests and share:
1. The console log output
2. Any error messages
3. Network request/response details
4. Whether the template appears after page refresh

This will help us pinpoint the exact cause and implement a permanent fix.