const mongoose = require('mongoose');

// Define schemas for nested objects
const locationSchema = new mongoose.Schema({
  address: { type: String, required: true },
  latitude: { type: Number },
  longitude: { type: Number }
}, { _id: false });

const actionItemSchema = new mongoose.Schema({
  id: { type: String, required: true },
  description: { type: String, required: true },
  priority: { 
    type: String, 
    enum: ['HIGH', 'MEDIUM', 'LOW'],
    default: 'MEDIUM'
  },
  assignedTo: { type: String },
  dueDate: { type: Date },
  status: { 
    type: String, 
    enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
    default: 'PENDING'
  },
  createdDate: { type: Date, default: Date.now },
  updatedDate: { type: Date, default: Date.now },
  notes: { type: String }
}, { _id: false });

const checklistItemSchema = new mongoose.Schema({
  id: { type: String, required: true },
  category: { type: String },
  description: { type: String, required: true },
  verified: { type: Boolean, default: false },
  notes: { type: String },
  createdDate: { type: Date, default: Date.now },
  updatedDate: { type: Date, default: Date.now }
}, { _id: false });

const photoSchema = new mongoose.Schema({
  id: { type: String, required: true },
  url: { type: String, required: true },
  caption: { type: String },
  category: { 
    type: String, 
    enum: ['EXTERIOR', 'INTERIOR', 'EQUIPMENT', 'DOCUMENT', 'OTHER'],
    default: 'OTHER'
  },
  createdDate: { type: Date, default: Date.now },
  metadata: { type: Object }
}, { _id: false });

const documentSchema = new mongoose.Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  url: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['REPORT', 'CERTIFICATE', 'LICENSE', 'PERMIT', 'OTHER'],
    default: 'OTHER'
  },
  notes: { type: String },
  createdDate: { type: Date, default: Date.now },
  updatedDate: { type: Date, default: Date.now }
}, { _id: false });

// Main SiteVisit schema
const siteVisitSchema = new mongoose.Schema({
  id: { type: String, required: true },
  applicationId: { type: String, required: true },
  scheduledDate: { type: Date, required: true },
  status: { 
    type: String, 
    enum: ['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
    default: 'SCHEDULED'
  },
  location: { type: locationSchema, required: true },
  conductedBy: { type: String, required: true },
  findings: { type: String, default: '' },
  checklist: [checklistItemSchema],
  photos: [photoSchema],
  documents: [documentSchema],
  duration: { type: Number }, // in minutes
  actionItems: [actionItemSchema],
  createdDate: { type: Date, default: Date.now },
  updatedDate: { type: Date, default: Date.now }
});

// Pre-save middleware to generate site visit ID
siteVisitSchema.pre('save', function(next) {
  if (!this.id) {
    this.id = `VISIT-${Date.now()}`;
  }
  next();
});

// Create the Mongoose model
const SiteVisitModel = mongoose.model('SiteVisit', siteVisitSchema);

// Export the model
module.exports = SiteVisitModel;
