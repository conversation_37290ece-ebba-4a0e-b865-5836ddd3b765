const mongoose = require('mongoose');
const NotificationTemplate = require('./models/notification-template');
const User = require('./models/user');

/**
 * Seed Notification Data
 * Populates the database with initial notification templates and sample data
 */

const notificationTemplates = [
  {
    name: 'Application Status Change',
    description: 'Notification sent when an application status changes',
    type: 'APPLICATION_STATUS_CHANGE',
    category: 'APPLICATION',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'oldStatus', type: 'string', required: true, description: 'Previous status' },
      { name: 'newStatus', type: 'string', required: true, description: 'New status' },
      { name: 'programmeName', type: 'string', required: false, description: 'Funding programme name' },
      { name: 'sponsorName', type: 'string', required: false, description: 'Corporate sponsor name' },
      { name: 'statusChangeDate', type: 'string', required: false, description: 'Date of status change' },
      { name: 'dashboardUrl', type: 'string', required: false, description: 'Link to application dashboard' }
    ],
    content: {
      inApp: {
        title: 'Application Status Updated - {{applicationId}}',
        message: 'Your application for {{businessName}} has been updated from {{oldStatus}} to {{newStatus}}. {{#if dashboardUrl}}View details: {{dashboardUrl}}{{/if}}',
        actions: [
          {
            label: 'View Application',
            action: 'NAVIGATE',
            url: '{{dashboardUrl}}',
            style: 'PRIMARY'
          }
        ]
      },
      email: {
        subject: 'Application Status Update - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2c3e50;">Application Status Update</h2>
            <p>Dear {{applicantName}},</p>
            <p>We wanted to inform you that the status of your application <strong>{{applicationId}}</strong> for <strong>{{businessName}}</strong> has been updated.</p>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>Previous Status:</strong> {{oldStatus}}</p>
              <p><strong>New Status:</strong> {{newStatus}}</p>
              <p><strong>Programme:</strong> {{programmeName}}</p>
              <p><strong>Date:</strong> {{statusChangeDate}}</p>
            </div>
            {{#if dashboardUrl}}
            <p><a href="{{dashboardUrl}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Application Details</a></p>
            {{/if}}
            <p>If you have any questions, please don't hesitate to contact us.</p>
            <p>Best regards,<br>The Funding Team</p>
          </div>
        `,
        textBody: 'Dear {{applicantName}}, Your application {{applicationId}} for {{businessName}} has been updated from {{oldStatus}} to {{newStatus}}. Programme: {{programmeName}}. Date: {{statusChangeDate}}. {{#if dashboardUrl}}View details: {{dashboardUrl}}{{/if}}'
      },
      sms: {
        message: 'Application {{applicationId}} status updated from {{oldStatus}} to {{newStatus}}. {{#if dashboardUrl}}Details: {{dashboardUrl}}{{/if}}'
      },
      push: {
        title: 'Application Status Updated',
        body: '{{businessName}} application is now {{newStatus}}',
        data: {
          applicationId: '{{applicationId}}',
          status: '{{newStatus}}',
          url: '{{dashboardUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'MEDIUM',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: false,
        push: true
      },
      targetAudience: 'SPECIFIC_USERS'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'Document Required',
    description: 'Notification sent when additional documents are required',
    type: 'DOCUMENT_REQUIRED',
    category: 'DOCUMENT',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'documentType', type: 'string', required: true, description: 'Type of document required' },
      { name: 'deadline', type: 'string', required: false, description: 'Submission deadline' },
      { name: 'programmeName', type: 'string', required: false, description: 'Funding programme name' },
      { name: 'uploadUrl', type: 'string', required: false, description: 'Document upload URL' }
    ],
    content: {
      inApp: {
        title: 'Document Required - {{documentType}}',
        message: 'Please submit {{documentType}} for your application {{applicationId}} ({{businessName}}).{{#if deadline}} Deadline: {{deadline}}.{{/if}}',
        actions: [
          {
            label: 'Upload Document',
            action: 'NAVIGATE',
            url: '{{uploadUrl}}',
            style: 'PRIMARY'
          }
        ]
      },
      email: {
        subject: 'Document Required - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #e74c3c;">Document Required</h2>
            <p>Dear {{applicantName}},</p>
            <p>We need additional documentation for your application <strong>{{applicationId}}</strong> for <strong>{{businessName}}</strong>.</p>
            <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
              <p><strong>Required Document:</strong> {{documentType}}</p>
              {{#if deadline}}<p><strong>Submission Deadline:</strong> {{deadline}}</p>{{/if}}
              <p><strong>Programme:</strong> {{programmeName}}</p>
            </div>
            {{#if uploadUrl}}
            <p><a href="{{uploadUrl}}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Upload Document Now</a></p>
            {{/if}}
            <p>Please submit the required document as soon as possible to avoid delays in processing your application.</p>
            <p>Best regards,<br>The Funding Team</p>
          </div>
        `,
        textBody: 'Dear {{applicantName}}, Document required for application {{applicationId}} ({{businessName}}): {{documentType}}.{{#if deadline}} Deadline: {{deadline}}.{{/if}} {{#if uploadUrl}}Upload: {{uploadUrl}}{{/if}}'
      },
      sms: {
        message: 'Document required for application {{applicationId}}: {{documentType}}.{{#if deadline}} Deadline: {{deadline}}.{{/if}} {{#if uploadUrl}}Upload: {{uploadUrl}}{{/if}}'
      },
      push: {
        title: 'Document Required',
        body: '{{documentType}} needed for {{businessName}} application',
        data: {
          applicationId: '{{applicationId}}',
          documentType: '{{documentType}}',
          url: '{{uploadUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'HIGH',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: true,
        push: true
      },
      targetAudience: 'SPECIFIC_USERS'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'Deadline Reminder',
    description: 'Notification sent when a deadline is approaching',
    type: 'DEADLINE_REMINDER',
    category: 'APPLICATION',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'deadlineType', type: 'string', required: true, description: 'Type of deadline' },
      { name: 'deadline', type: 'string', required: true, description: 'Deadline date' },
      { name: 'daysRemaining', type: 'number', required: true, description: 'Days remaining until deadline' },
      { name: 'urgencyLevel', type: 'string', required: false, description: 'Urgency level' },
      { name: 'actionUrl', type: 'string', required: false, description: 'Action URL' }
    ],
    content: {
      inApp: {
        title: '{{urgencyLevel}} Deadline Reminder',
        message: '{{deadlineType}} deadline for {{businessName}} ({{applicationId}}) is {{deadline}} - {{daysRemaining}} days remaining.',
        actions: [
          {
            label: 'Take Action',
            action: 'NAVIGATE',
            url: '{{actionUrl}}',
            style: 'WARNING'
          }
        ]
      },
      email: {
        subject: 'Deadline Reminder - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #f39c12;">Deadline Reminder</h2>
            <p>Dear {{applicantName}},</p>
            <p>This is a reminder that you have an upcoming deadline for your application <strong>{{applicationId}}</strong> for <strong>{{businessName}}</strong>.</p>
            <div style="background-color: #fcf8e3; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #f39c12;">
              <p><strong>Deadline Type:</strong> {{deadlineType}}</p>
              <p><strong>Deadline Date:</strong> {{deadline}}</p>
              <p><strong>Days Remaining:</strong> {{daysRemaining}}</p>
              <p><strong>Urgency:</strong> {{urgencyLevel}}</p>
            </div>
            {{#if actionUrl}}
            <p><a href="{{actionUrl}}" style="background-color: #f39c12; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Take Action Now</a></p>
            {{/if}}
            <p>Please ensure you complete the required actions before the deadline to avoid any delays.</p>
            <p>Best regards,<br>The Funding Team</p>
          </div>
        `,
        textBody: 'Deadline reminder: {{deadlineType}} for {{businessName}} ({{applicationId}}) is {{deadline}} - {{daysRemaining}} days remaining. {{#if actionUrl}}Action: {{actionUrl}}{{/if}}'
      },
      sms: {
        message: 'REMINDER: {{deadlineType}} deadline {{deadline}} - {{daysRemaining}} days left for {{applicationId}}. {{#if actionUrl}}{{actionUrl}}{{/if}}'
      },
      push: {
        title: 'Deadline Reminder',
        body: '{{daysRemaining}} days left for {{deadlineType}}',
        data: {
          applicationId: '{{applicationId}}',
          deadline: '{{deadline}}',
          daysRemaining: '{{daysRemaining}}',
          url: '{{actionUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'URGENT',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: true,
        push: true
      },
      targetAudience: 'SPECIFIC_USERS'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'Interview Scheduled',
    description: 'Notification sent when an interview is scheduled',
    type: 'INTERVIEW_SCHEDULED',
    category: 'INTERVIEW',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'interviewType', type: 'string', required: true, description: 'Type of interview' },
      { name: 'scheduledDate', type: 'string', required: true, description: 'Interview date' },
      { name: 'scheduledTime', type: 'string', required: true, description: 'Interview time' },
      { name: 'programmeName', type: 'string', required: false, description: 'Funding programme name' },
      { name: 'interviewUrl', type: 'string', required: false, description: 'Interview details URL' }
    ],
    content: {
      inApp: {
        title: 'Interview Scheduled - {{interviewType}}',
        message: 'Your {{interviewType}} for {{businessName}} ({{applicationId}}) is scheduled for {{scheduledDate}} at {{scheduledTime}}.',
        actions: [
          {
            label: 'View Details',
            action: 'NAVIGATE',
            url: '{{interviewUrl}}',
            style: 'PRIMARY'
          }
        ]
      },
      email: {
        subject: 'Interview Scheduled - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #17a2b8;">Interview Scheduled</h2>
            <p>Dear {{applicantName}},</p>
            <p>We are pleased to inform you that an interview has been scheduled for your application <strong>{{applicationId}}</strong> for <strong>{{businessName}}</strong>.</p>
            <div style="background-color: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #17a2b8;">
              <p><strong>Interview Type:</strong> {{interviewType}}</p>
              <p><strong>Date:</strong> {{scheduledDate}}</p>
              <p><strong>Time:</strong> {{scheduledTime}}</p>
              <p><strong>Programme:</strong> {{programmeName}}</p>
            </div>
            {{#if interviewUrl}}
            <p><a href="{{interviewUrl}}" style="background-color: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Interview Details</a></p>
            {{/if}}
            <p>Please ensure you are available at the scheduled time. We look forward to speaking with you.</p>
            <p>Best regards,<br>The Funding Team</p>
          </div>
        `,
        textBody: 'Interview scheduled for {{businessName}} ({{applicationId}}): {{interviewType}} on {{scheduledDate}} at {{scheduledTime}}. Programme: {{programmeName}}. {{#if interviewUrl}}Details: {{interviewUrl}}{{/if}}'
      },
      sms: {
        message: 'Interview scheduled: {{interviewType}} for {{applicationId}} on {{scheduledDate}} at {{scheduledTime}}. {{#if interviewUrl}}{{interviewUrl}}{{/if}}'
      },
      push: {
        title: 'Interview Scheduled',
        body: '{{interviewType}} on {{scheduledDate}} at {{scheduledTime}}',
        data: {
          applicationId: '{{applicationId}}',
          interviewType: '{{interviewType}}',
          scheduledDate: '{{scheduledDate}}',
          url: '{{interviewUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'HIGH',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: true,
        push: true
      },
      targetAudience: 'SPECIFIC_USERS'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'Site Visit Scheduled',
    description: 'Notification sent when a site visit is scheduled',
    type: 'SITE_VISIT_SCHEDULED',
    category: 'SITE_VISIT',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'visitType', type: 'string', required: true, description: 'Type of site visit' },
      { name: 'scheduledDate', type: 'string', required: true, description: 'Visit date' },
      { name: 'scheduledTime', type: 'string', required: true, description: 'Visit time' },
      { name: 'businessAddress', type: 'string', required: false, description: 'Business address' },
      { name: 'programmeName', type: 'string', required: false, description: 'Funding programme name' }
    ],
    content: {
      inApp: {
        title: 'Site Visit Scheduled - {{visitType}}',
        message: 'A {{visitType}} for {{businessName}} ({{applicationId}}) is scheduled for {{scheduledDate}} at {{scheduledTime}}.',
        actions: []
      },
      email: {
        subject: 'Site Visit Scheduled - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #6f42c1;">Site Visit Scheduled</h2>
            <p>Dear {{applicantName}},</p>
            <p>We have scheduled a site visit for your application <strong>{{applicationId}}</strong> for <strong>{{businessName}}</strong>.</p>
            <div style="background-color: #e2e3f0; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #6f42c1;">
              <p><strong>Visit Type:</strong> {{visitType}}</p>
              <p><strong>Date:</strong> {{scheduledDate}}</p>
              <p><strong>Time:</strong> {{scheduledTime}}</p>
              {{#if businessAddress}}<p><strong>Location:</strong> {{businessAddress}}</p>{{/if}}
              <p><strong>Programme:</strong> {{programmeName}}</p>
            </div>
            <p>Please ensure that you or a representative will be available at the business premises during the scheduled time.</p>
            <p>Best regards,<br>The Funding Team</p>
          </div>
        `,
        textBody: 'Site visit scheduled for {{businessName}} ({{applicationId}}): {{visitType}} on {{scheduledDate}} at {{scheduledTime}}.{{#if businessAddress}} Location: {{businessAddress}}.{{/if}} Programme: {{programmeName}}.'
      },
      sms: {
        message: 'Site visit scheduled: {{visitType}} for {{applicationId}} on {{scheduledDate}} at {{scheduledTime}}.{{#if businessAddress}} At: {{businessAddress}}{{/if}}'
      },
      push: {
        title: 'Site Visit Scheduled',
        body: '{{visitType}} on {{scheduledDate}} at {{scheduledTime}}',
        data: {
          applicationId: '{{applicationId}}',
          visitType: '{{visitType}}',
          scheduledDate: '{{scheduledDate}}'
        }
      }
    },
    defaultSettings: {
      priority: 'HIGH',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: true,
        push: true
      },
      targetAudience: 'SPECIFIC_USERS'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'Approval Required',
    description: 'Notification sent when approval is required from managers/approvers',
    type: 'APPROVAL_REQUIRED',
    category: 'APPROVAL',
    variables: [
      { name: 'applicationId', type: 'string', required: true, description: 'Application ID' },
      { name: 'applicantName', type: 'string', required: true, description: 'Name of the applicant' },
      { name: 'businessName', type: 'string', required: true, description: 'Business name' },
      { name: 'approvalType', type: 'string', required: true, description: 'Type of approval required' },
      { name: 'programmeName', type: 'string', required: false, description: 'Funding programme name' },
      { name: 'requestedAmount', type: 'string', required: false, description: 'Requested funding amount' },
      { name: 'reviewUrl', type: 'string', required: false, description: 'Review URL' }
    ],
    content: {
      inApp: {
        title: 'Approval Required - {{approvalType}}',
        message: '{{approvalType}} required for {{businessName}} ({{applicationId}}).{{#if requestedAmount}} Amount: {{requestedAmount}}.{{/if}}',
        actions: [
          {
            label: 'Review Application',
            action: 'NAVIGATE',
            url: '{{reviewUrl}}',
            style: 'PRIMARY'
          }
        ]
      },
      email: {
        subject: 'Approval Required - {{applicationId}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">Approval Required</h2>
            <p>Dear Approver,</p>
            <p>Your approval is required for the following application:</p>
            <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
              <p><strong>Application ID:</strong> {{applicationId}}</p>
              <p><strong>Business Name:</strong> {{businessName}}</p>
              <p><strong>Applicant:</strong> {{applicantName}}</p>
              <p><strong>Approval Type:</strong> {{approvalType}}</p>
              {{#if requestedAmount}}<p><strong>Requested Amount:</strong> {{requestedAmount}}</p>{{/if}}
              <p><strong>Programme:</strong> {{programmeName}}</p>
            </div>
            {{#if reviewUrl}}
            <p><a href="{{reviewUrl}}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Application</a></p>
            {{/if}}
            <p>Please review and provide your approval decision as soon as possible.</p>
            <p>Best regards,<br>The System</p>
          </div>
        `,
        textBody: 'Approval required: {{approvalType}} for {{businessName}} ({{applicationId}}). Applicant: {{applicantName}}.{{#if requestedAmount}} Amount: {{requestedAmount}}.{{/if}} Programme: {{programmeName}}. {{#if reviewUrl}}Review: {{reviewUrl}}{{/if}}'
      },
      sms: {
        message: 'Approval required: {{approvalType}} for {{applicationId}} ({{businessName}}).{{#if reviewUrl}} Review: {{reviewUrl}}{{/if}}'
      },
      push: {
        title: 'Approval Required',
        body: '{{approvalType}} needed for {{businessName}}',
        data: {
          applicationId: '{{applicationId}}',
          approvalType: '{{approvalType}}',
          url: '{{reviewUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'HIGH',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: false,
        push: true
      },
      targetAudience: 'ROLE_BASED'
    },
    isActive: true,
    isSystem: true
  },
  {
    name: 'System Announcement',
    description: 'General system announcements and updates',
    type: 'SYSTEM_ANNOUNCEMENT',
    category: 'SYSTEM',
    variables: [
      { name: 'title', type: 'string', required: true, description: 'Announcement title' },
      { name: 'message', type: 'string', required: true, description: 'Announcement message' },
      { name: 'priority', type: 'string', required: false, description: 'Priority level' },
      { name: 'actionUrl', type: 'string', required: false, description: 'Action URL' },
      { name: 'actionLabel', type: 'string', required: false, description: 'Action button label' }
    ],
    content: {
      inApp: {
        title: '{{title}}',
        message: '{{message}}',
        actions: [
          {
            label: '{{actionLabel}}',
            action: 'NAVIGATE',
            url: '{{actionUrl}}',
            style: 'PRIMARY'
          }
        ]
      },
      email: {
        subject: 'System Announcement: {{title}}',
        htmlBody: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #28a745;">{{title}}</h2>
            <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
              <p>{{message}}</p>
            </div>
            {{#if actionUrl}}
            <p><a href="{{actionUrl}}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">{{actionLabel}}</a></p>
            {{/if}}
            <p>Best regards,<br>The System Team</p>
          </div>
        `,
        textBody: '{{title}}: {{message}} {{#if actionUrl}}{{actionLabel}}: {{actionUrl}}{{/if}}'
      },
      sms: {
        message: '{{title}}: {{message}} {{#if actionUrl}}{{actionUrl}}{{/if}}'
      },
      push: {
        title: '{{title}}',
        body: '{{message}}',
        data: {
          url: '{{actionUrl}}'
        }
      }
    },
    defaultSettings: {
      priority: 'MEDIUM',
      deliveryMethods: {
        inApp: true,
        email: true,
        sms: false,
        push: true
      },
      targetAudience: 'ALL_USERS'
    },
    isActive: true,
    isSystem: true
  }
];

/**
 * Seed notification templates
 */
async function seedNotificationTemplates() {
  console.log('🌱 Seeding notification templates...');

  try {
    // Get system user for created by field
    let systemUser = await User.findOne({ username: 'system' });
    if (!systemUser) {
      // Create system user if it doesn't exist
      systemUser = new User({
        username: 'system',
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'User',
        role: 'admin',
        organizationType: 'system',
        isActive: true,
        isSystem: true
      });
      await systemUser.save();
      console.log('Created system user for templates');
    }

    let createdCount = 0;
    let updatedCount = 0;

    for (const templateData of notificationTemplates) {
      const existingTemplate = await NotificationTemplate.findOne({
        type: templateData.type,
        isSystem: true
      });

      if (existingTemplate) {
        // Update existing template
        Object.assign(existingTemplate, {
          ...templateData,
          updatedBy: systemUser._id,
          updatedAt: new Date()
        });
        await existingTemplate.save();
        updatedCount++;
        console.log(`✅ Updated template: ${templateData.name}`);
      } else {
        // Create new template
        const template = new NotificationTemplate({
          ...templateData,
          createdBy: systemUser._id,
          updatedBy: systemUser._id
        });
        await template.save();
        createdCount++;
        console.log(`✅ Created template: ${templateData.name}`);
      }
    }

    console.log(`🎉 Notification templates seeded successfully!`);
    console.log(`📊 Created: ${createdCount}, Updated: ${updatedCount}`);

    return {
      success: true,
      created: createdCount,
      updated: updatedCount,
      total: notificationTemplates.length
    };

  } catch (error) {
    console.error('❌ Error seeding notification templates:', error);
    throw error;
  }
}

/**
 * Seed sample notification preferences for existing users
 */
async function seedNotificationPreferences() {
  console.log('🌱 Seeding notification preferences...');

  try {
    const users = await User.find({ isActive: true }).limit(50); // Limit to avoid too many operations
    let createdCount = 0;

    for (const user of users) {
      const existingPrefs = await mongoose.connection.db.collection('notificationpreferences')
        .findOne({ userId: user._id });

      if (!existingPrefs) {
        const preferences = {
          userId: user._id,
          inAppNotifications: true,
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: true,
          categories: {
            APPLICATION: true,
            INTERVIEW: true,
            SITE_VISIT: true,
            DOCUMENT: true,
            APPROVAL: user.role === 'admin' || user.role === 'manager',
            SYSTEM: true,
            MEETING: true,
            REPORT: user.role === 'admin' || user.role === 'manager',
            SCORECARD: true,
            GENERAL: true
          },
          priorities: {
            LOW: true,
            MEDIUM: true,
            HIGH: true,
            URGENT: true
          },
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '06:00'
          },
          digestEmail: {
            enabled: true,
            frequency: 'daily'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        await mongoose.connection.db.collection('notificationpreferences')
          .insertOne(preferences);
        createdCount++;
      }
    }

    console.log(`🎉 Notification preferences seeded successfully!`);
    console.log(`📊 Created preferences for ${createdCount} users`);

    return {
      success: true,
      created: createdCount
    };

  } catch (error) {
    console.error('❌ Error seeding notification preferences:', error);
    throw error;
  }
}

/**
 * Seed sample notifications for testing
 */
async function seedSampleNotifications() {
  console.log('🌱 Seeding sample notifications...');

  try {
    const systemUser = await User.findOne({ username: 'system' });
    if (!systemUser) {
      console.warn('System user not found, skipping sample notifications');
      return { success: false, message: 'System user not found' };
    }

    const users = await User.find({ isActive: true }).limit(10);
    if (users.length === 0) {
      console.warn('No active users found, skipping sample notifications');
      return { success: false, message: 'No active users found' };
    }

    const sampleNotifications = [
      {
        title: 'Welcome to the Funding System',
        message: 'Welcome to our funding application system. You can now submit and track your applications online.',
        type: 'SYSTEM_ANNOUNCEMENT',
        category: 'SYSTEM',
        priority: 'MEDIUM',
        targetAudience: 'ALL_USERS',
        deliveryMethods: {
          inApp: true,
          email: false,
          sms: false,
          push: false
        },
        status: 'SENT',
        sentAt: new Date()
      },
      {
        title: 'System Maintenance Scheduled',
        message: 'System maintenance is scheduled for this weekend. The system will be unavailable from 2 AM to 6 AM on Saturday.',
        type: 'SYSTEM_ANNOUNCEMENT',
        category: 'SYSTEM',
        priority: 'HIGH',
        targetAudience: 'ALL_USERS',
        deliveryMethods: {
          inApp: true,
          email: true,
          sms: false,
          push: true
        },
        status: 'SENT',
        sentAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
      },
      {
        title: 'New Features Available',
        message: 'We have added new features to improve your experience. Check out the updated dashboard and notification center.',
        type: 'SYSTEM_ANNOUNCEMENT',
        category: 'SYSTEM',
        priority: 'LOW',
        targetAudience: 'ALL_USERS',
        deliveryMethods: {
          inApp: true,
          email: false,
          sms: false,
          push: false
        },
        status: 'SENT',
        sentAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 1 week ago
      }
    ];

    let createdCount = 0;

    for (const notificationData of sampleNotifications) {
      const notification = new (require('./models/notification'))({
        ...notificationData,
        createdBy: systemUser._id,
        analytics: {
          totalRecipients: users.length,
          deliveredCount: users.length,
          readCount: Math.floor(users.length * 0.7), // 70% read rate
          clickCount: Math.floor(users.length * 0.3), // 30% click rate
          lastAnalyticsUpdate: new Date()
        }
      });

      await notification.save();
      createdCount++;

      // Create user notifications for each user
      for (const user of users) {
        const userNotification = new (require('./models/user-notification'))({
          userId: user._id,
          notificationId: notification._id,
          status: 'DELIVERED',
          isRead: Math.random() > 0.3, // 70% chance of being read
          isClicked: Math.random() > 0.7, // 30% chance of being clicked
          deliveredAt: notification.sentAt,
          readAt: Math.random() > 0.3 ? new Date(notification.sentAt.getTime() + Math.random() * 24 * 60 * 60 * 1000) : null
        });

        await userNotification.save();
      }
    }

    console.log(`🎉 Sample notifications seeded successfully!`);
    console.log(`📊 Created ${createdCount} notifications with user notifications`);

    return {
      success: true,
      created: createdCount
    };

  } catch (error) {
    console.error('❌ Error seeding sample notifications:', error);
    throw error;
  }
}

/**
 * Main seeding function
 */
async function seedNotificationData() {
  console.log('🚀 Starting notification data seeding...');

  try {
    const results = {
      templates: await seedNotificationTemplates(),
      preferences: await seedNotificationPreferences(),
      sampleNotifications: await seedSampleNotifications()
    };

    console.log('🎉 All notification data seeded successfully!');
    console.log('📊 Summary:', results);

    return results;

  } catch (error) {
    console.error('❌ Error during notification data seeding:', error);
    throw error;
  }
}

/**
 * Run seeding if called directly
 */
if (require.main === module) {
  const connectDB = require('./config/database');
  
  connectDB().then(async () => {
    try {
      await seedNotificationData();
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    } catch (error) {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  });
}

module.exports = {
  seedNotificationData,
  seedNotificationTemplates,
  seedNotificationPreferences,
  seedSampleNotifications
};