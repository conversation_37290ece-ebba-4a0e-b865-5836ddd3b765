const express = require('express');
const router = express.Router();
const CorporateSponsor = require('../models/corporate-sponsor');

// Get all corporate sponsors with pagination
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build filter object from query parameters
    const filter = {};
    if (req.query.status) filter.status = req.query.status;
    if (req.query.industry) filter.industry = req.query.industry;
    
    // Text search if search parameter is provided
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }
    
    const corporateSponsors = await CorporateSponsor.find(filter)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);
    
    const total = await CorporateSponsor.countDocuments(filter);
    
    res.json({
      success: true,
      data: corporateSponsors,
      total: total,
      page: page,
      limit: limit
    });
  } catch (error) {
    console.error('Error fetching corporate sponsors:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch corporate sponsors',
      error: error.message
    });
  }
});

// Get active corporate sponsors for filter dropdown
router.get('/active', async (req, res) => {
  try {
    console.log('Fetching active corporate sponsors for filter dropdown');
    
    // Get all active corporate sponsors
    const activeSponsors = await CorporateSponsor.find({ status: 'active' })
      .select('_id name industry totalFunding description')
      .sort({ name: 1 });
    
    console.log(`Found ${activeSponsors.length} active corporate sponsors`);
    
    // Transform the response to include both _id and id fields for compatibility
    const transformedSponsors = activeSponsors.map(sponsor => ({
      _id: sponsor._id,
      id: sponsor._id.toString(), // Add string version for frontend compatibility
      name: sponsor.name,
      industry: sponsor.industry,
      totalFunding: sponsor.totalFunding,
      description: sponsor.description
    }));
    
    res.json(transformedSponsors);
  } catch (error) {
    console.error('Error fetching active corporate sponsors:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch active corporate sponsors',
      error: error.message
    });
  }
});

// Get a specific corporate sponsor by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate if the ID is a valid MongoDB ObjectId
    if (!id || id === 'NaN' || id === 'null' || id === 'undefined') {
      return res.status(400).json({
        success: false,
        message: 'Invalid sponsor ID provided'
      });
    }
    
    // Check if it's a valid ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid sponsor ID format'
      });
    }
    
    const corporateSponsor = await CorporateSponsor.findById(id);
    
    if (!corporateSponsor) {
      return res.status(404).json({
        success: false,
        message: 'Corporate sponsor not found'
      });
    }
    
    res.json({
      success: true,
      data: corporateSponsor
    });
  } catch (error) {
    console.error(`Error fetching corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch corporate sponsor',
      error: error.message
    });
  }
});

// Create a new corporate sponsor
router.post('/', async (req, res) => {
  try {
    const corporateSponsor = new CorporateSponsor(req.body);
    const savedCorporateSponsor = await corporateSponsor.save();
    
    res.status(201).json({
      success: true,
      data: savedCorporateSponsor
    });
  } catch (error) {
    console.error('Error creating corporate sponsor:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create corporate sponsor',
      error: error.message
    });
  }
});

// Update a corporate sponsor
router.put('/:id', async (req, res) => {
  try {
    // Update the updatedAt field
    req.body.updatedAt = new Date();
    
    const corporateSponsor = await CorporateSponsor.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!corporateSponsor) {
      return res.status(404).json({
        success: false,
        message: 'Corporate sponsor not found'
      });
    }
    
    res.json({
      success: true,
      data: corporateSponsor
    });
  } catch (error) {
    console.error(`Error updating corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to update corporate sponsor',
      error: error.message
    });
  }
});

// Delete a corporate sponsor
router.delete('/:id', async (req, res) => {
  try {
    const corporateSponsor = await CorporateSponsor.findByIdAndDelete(req.params.id);
    
    if (!corporateSponsor) {
      return res.status(404).json({
        success: false,
        message: 'Corporate sponsor not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Corporate sponsor deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete corporate sponsor',
      error: error.message
    });
  }
});

// Get programmes for a corporate sponsor
router.get('/:id/programmes', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate if the ID is a valid MongoDB ObjectId
    if (!id || id === 'NaN' || id === 'null' || id === 'undefined') {
      return res.status(400).json({
        success: false,
        message: 'Invalid sponsor ID provided'
      });
    }
    
    // Check if it's a valid ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid sponsor ID format'
      });
    }
    
    console.log(`Fetching programmes for corporate sponsor: ${id}`);
    
    // Get funding programmes for this corporate sponsor
    const FundingProgramme = require('../models/funding-programme');
    const programmes = await FundingProgramme.find({
      corporateSponsorId: id,
      status: 'active' // Only return active programmes
    })
    .select('_id name description status budget timeline')
    .sort({ name: 1 });
    
    console.log(`Found ${programmes.length} programmes for sponsor ${id}`);
    
    // Transform the response to include both _id and id fields for compatibility
    const transformedProgrammes = programmes.map(programme => ({
      _id: programme._id,
      id: programme._id.toString(), // Add string version for frontend compatibility
      name: programme.name,
      description: programme.description,
      status: programme.status,
      budget: programme.budget,
      timeline: programme.timeline
    }));
    
    res.json(transformedProgrammes);
  } catch (error) {
    console.error(`Error fetching programmes for corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch programmes for corporate sponsor',
      error: error.message
    });
  }
});

// Get users for a corporate sponsor
router.get('/:id/users', async (req, res) => {
  try {
    // This would typically involve a lookup to a users collection
    // For now, we'll return a placeholder response
    res.json({
      success: true,
      data: [],
      message: 'User lookup functionality to be implemented'
    });
  } catch (error) {
    console.error(`Error fetching users for corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users for corporate sponsor',
      error: error.message
    });
  }
});

// Create a user for a corporate sponsor
router.post('/:id/users', async (req, res) => {
  try {
    // This would typically involve creating a user in a users collection
    // For now, we'll return a placeholder response
    res.status(201).json({
      success: true,
      data: { ...req.body, id: 'placeholder-user-id' },
      message: 'User creation functionality to be implemented'
    });
  } catch (error) {
    console.error(`Error creating user for corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user for corporate sponsor',
      error: error.message
    });
  }
});

// Get applications for a corporate sponsor
router.get('/:id/applications', async (req, res) => {
  try {
    // This would typically involve a lookup to an applications collection
    // For now, we'll return a placeholder response
    res.json({
      success: true,
      data: [],
      message: 'Application lookup functionality to be implemented'
    });
  } catch (error) {
    console.error(`Error fetching applications for corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch applications for corporate sponsor',
      error: error.message
    });
  }
});

// Get statistics for a corporate sponsor
router.get('/:id/statistics', async (req, res) => {
  try {
    // This would typically involve aggregation queries
    // For now, we'll return a placeholder response
    res.json({
      success: true,
      data: {
        totalApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        pendingApplications: 0,
        totalFunding: 0
      },
      message: 'Statistics functionality to be implemented'
    });
  } catch (error) {
    console.error(`Error fetching statistics for corporate sponsor ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics for corporate sponsor',
      error: error.message
    });
  }
});

module.exports = router;
