const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Reuse address schema from corporate sponsor model
const addressSchema = new Schema({
  street: { type: String },
  city: { type: String },
  province: { type: String },
  postalCode: { type: String },
  country: { type: String, default: 'South Africa' }
});

const attendeeSchema = new Schema({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  role: { type: String },
  confirmed: { 
    type: Boolean,
    default: false
  }
});

const meetingApplicationSchema = new Schema({
  applicationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Application',
    required: true
  },
  status: { 
    type: String, 
    enum: ['scheduled', 'presented', 'approved', 'rejected', 'deferred'],
    default: 'scheduled'
  },
  presentationTime: { type: String },
  notes: { type: String }
});

const committeeMeetingSchema = new Schema({
  programmeId: { 
    type: Schema.Types.ObjectId, 
    ref: 'FundingProgramme',
    required: true
  },
  title: { 
    type: String,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  date: { 
    type: Date,
    required: true
  },
  startTime: { type: String },
  endTime: { type: String },
  location: { 
    type: String, 
    enum: ['physical', 'virtual'],
    required: true
  },
  meetingLink: { 
    type: String,
    trim: true
  },
  physicalAddress: addressSchema,
  attendees: [attendeeSchema],
  applications: [meetingApplicationSchema],
  agenda: { 
    type: String,
    trim: true
  },
  minutes: { 
    type: String,
    trim: true
  },
  status: { 
    type: String, 
    enum: ['scheduled', 'in-progress', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User'
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, { timestamps: true });

// Add text index for search functionality
committeeMeetingSchema.index({ 
  title: 'text', 
  description: 'text', 
  agenda: 'text',
  minutes: 'text'
});

// Virtual for URL
committeeMeetingSchema.virtual('url').get(function() {
  return `/committee-meetings/${this._id}`;
});

module.exports = mongoose.model('CommitteeMeeting', committeeMeetingSchema);
