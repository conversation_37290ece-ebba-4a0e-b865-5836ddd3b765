const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const ApprovalWorkflow = require('../models/approval-workflow');
const Application = require('../models/application');
const FundingProgramme = require('../models/funding-programme');
const CommitteeMeeting = require('../models/committee-meeting');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Get all approval workflows
router.get('/', authenticateToken, async (req, res) => {
  try {
    const query = {};
    
    // Handle application filter
    if (req.query.applicationId) {
      query.applicationId = req.query.applicationId;
    }
    
    // Handle programme filter
    if (req.query.programmeId) {
      query.programmeId = req.query.programmeId;
    }
    
    // Handle current step filter
    if (req.query.currentStep) {
      query.currentStep = req.query.currentStep;
    }
    
    const workflows = await ApprovalWorkflow.find(query)
      .populate('applicationId', 'applicationNumber applicantName businessName')
      .populate('programmeId', 'name')
      .sort({ 'createdAt': -1 })
      .limit(parseInt(req.query.limit) || 100)
      .skip(parseInt(req.query.skip) || 0);
    
    const total = await ApprovalWorkflow.countDocuments(query);
    
    res.json({
      workflows,
      total,
      limit: parseInt(req.query.limit) || 100,
      skip: parseInt(req.query.skip) || 0
    });
  } catch (err) {
    console.error('Error fetching approval workflows:', err);
    res.status(500).json({ message: 'Error fetching approval workflows', error: err.message });
  }
});

// Get a specific approval workflow by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const workflow = await ApprovalWorkflow.findById(req.params.id)
      .populate('applicationId', 'applicationNumber applicantName businessName requestedAmount')
      .populate('programmeId', 'name corporateSponsorId')
      .populate('steps.assignedTo', 'firstName lastName email')
      .populate('committeeMeetings.meetingId');
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    res.json(workflow);
  } catch (err) {
    console.error('Error fetching approval workflow:', err);
    res.status(500).json({ message: 'Error fetching approval workflow', error: err.message });
  }
});

// Create a new approval workflow
router.post('/', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { applicationId, programmeId } = req.body;
    
    if (!applicationId || !programmeId) {
      return res.status(400).json({ message: 'Application ID and Programme ID are required' });
    }
    
    // Verify application exists
    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(400).json({ message: 'Application not found' });
    }
    
    // Verify programme exists
    const programme = await FundingProgramme.findById(programmeId);
    if (!programme) {
      return res.status(400).json({ message: 'Funding programme not found' });
    }
    
    // Check if workflow already exists for this application
    const existingWorkflow = await ApprovalWorkflow.findOne({ applicationId });
    if (existingWorkflow) {
      return res.status(400).json({ 
        message: 'Approval workflow already exists for this application',
        workflowId: existingWorkflow._id
      });
    }
    
    // Create initial workflow with first step
    const newWorkflow = new ApprovalWorkflow({
      applicationId,
      programmeId,
      currentStep: 'ANALYST_REPORT',
      steps: [{
        step: 'ANALYST_REPORT',
        status: 'pending'
      }],
      committeeMeetings: []
    });
    
    const savedWorkflow = await newWorkflow.save();
    
    // Update application with workflow reference
    await Application.findByIdAndUpdate(applicationId, {
      approvalWorkflow: savedWorkflow._id
    });
    
    res.status(201).json(savedWorkflow);
  } catch (err) {
    console.error('Error creating approval workflow:', err);
    res.status(400).json({ message: 'Error creating approval workflow', error: err.message });
  }
});

// Update a step in the approval workflow
router.put('/:id/steps/:step', authenticateToken, authorizeRoles(['admin', 'manager', 'analyst']), async (req, res) => {
  try {
    const { status, notes, assignedTo, attachments } = req.body;
    
    if (!status || !['pending', 'in-progress', 'completed', 'rejected'].includes(status)) {
      return res.status(400).json({ message: 'Valid status is required' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Find the step in the workflow
    const stepIndex = workflow.steps.findIndex(s => s.step === req.params.step);
    
    if (stepIndex === -1) {
      return res.status(404).json({ message: 'Step not found in this workflow' });
    }
    
    // Update step status
    workflow.steps[stepIndex].status = status;
    
    // Update other fields if provided
    if (notes) {
      workflow.steps[stepIndex].notes = notes;
    }
    
    if (assignedTo) {
      workflow.steps[stepIndex].assignedTo = assignedTo;
    }
    
    if (attachments) {
      workflow.steps[stepIndex].attachments = attachments;
    }
    
    // Set dates based on status
    if (status === 'in-progress' && !workflow.steps[stepIndex].startDate) {
      workflow.steps[stepIndex].startDate = new Date();
    }
    
    if (['completed', 'rejected'].includes(status)) {
      workflow.steps[stepIndex].completionDate = new Date();
    }
    
    // If step is completed, advance to next step
    if (status === 'completed') {
      workflow.advanceToNextStep();
    }
    
    await workflow.save();
    
    // If workflow is rejected, update application status
    if (status === 'rejected') {
      await Application.findByIdAndUpdate(workflow.applicationId, {
        status: 'rejected',
        decisionDate: new Date()
      });
    }
    
    // If final step is completed, update application status
    if (workflow.currentStep === 'RECEIPT_CONFIRMATION' && status === 'completed') {
      await Application.findByIdAndUpdate(workflow.applicationId, {
        status: 'funded',
        decisionDate: new Date()
      });
    }
    
    res.json({
      message: 'Step updated successfully',
      workflow
    });
  } catch (err) {
    console.error('Error updating approval step:', err);
    res.status(500).json({ message: 'Error updating approval step', error: err.message });
  }
});

// Add committee meeting reference to workflow
router.post('/:id/committee-meetings', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { meetingId } = req.body;
    
    if (!meetingId) {
      return res.status(400).json({ message: 'Meeting ID is required' });
    }
    
    // Verify meeting exists
    const meetingExists = await CommitteeMeeting.exists({ _id: meetingId });
    if (!meetingExists) {
      return res.status(400).json({ message: 'Committee meeting not found' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Check if meeting is already in the workflow
    const meetingAlreadyExists = workflow.committeeMeetings.some(m => 
      m.meetingId.toString() === meetingId
    );
    
    if (meetingAlreadyExists) {
      return res.status(400).json({ message: 'Meeting is already added to this workflow' });
    }
    
    // Add meeting to workflow
    workflow.committeeMeetings.push({
      meetingId,
      status: 'scheduled'
    });
    
    await workflow.save();
    
    res.status(201).json({
      message: 'Committee meeting added to workflow',
      workflow
    });
  } catch (err) {
    console.error('Error adding committee meeting to workflow:', err);
    res.status(500).json({ message: 'Error adding committee meeting to workflow', error: err.message });
  }
});

// Update committee meeting status in workflow
router.put('/:id/committee-meetings/:meetingId', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { status, notes } = req.body;
    
    if (!status || !['scheduled', 'presented', 'approved', 'rejected', 'deferred'].includes(status)) {
      return res.status(400).json({ message: 'Valid status is required' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Find the meeting in the workflow
    const meetingIndex = workflow.committeeMeetings.findIndex(m => 
      m.meetingId.toString() === req.params.meetingId
    );
    
    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Meeting not found in this workflow' });
    }
    
    // Update meeting status
    workflow.committeeMeetings[meetingIndex].status = status;
    
    if (notes) {
      workflow.committeeMeetings[meetingIndex].notes = notes;
    }
    
    if (['presented', 'approved', 'rejected', 'deferred'].includes(status)) {
      workflow.committeeMeetings[meetingIndex].presentationDate = new Date();
    }
    
    await workflow.save();
    
    // If meeting is approved or rejected, update application status
    if (['approved', 'rejected'].includes(status)) {
      await Application.findByIdAndUpdate(workflow.applicationId, {
        status: status === 'approved' ? 'approved' : 'rejected',
        decisionDate: new Date()
      });
      
      // If approved, advance to next step
      if (status === 'approved' && workflow.currentStep === 'COMMITTEE_MEETING') {
        // Find the committee meeting step
        const stepIndex = workflow.steps.findIndex(s => s.step === 'COMMITTEE_MEETING');
        if (stepIndex !== -1) {
          workflow.steps[stepIndex].status = 'completed';
          workflow.steps[stepIndex].completionDate = new Date();
          workflow.advanceToNextStep();
          await workflow.save();
        }
      }
    }
    
    res.json({
      message: 'Committee meeting status updated',
      workflow
    });
  } catch (err) {
    console.error('Error updating committee meeting status:', err);
    res.status(500).json({ message: 'Error updating committee meeting status', error: err.message });
  }
});

// Generate award letter
router.post('/:id/award-letter', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { content, attachments } = req.body;
    
    if (!content) {
      return res.status(400).json({ message: 'Award letter content is required' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Find the award letter step
    const stepIndex = workflow.steps.findIndex(s => s.step === 'AWARD_LETTER');
    
    if (stepIndex === -1) {
      // If step doesn't exist, create it
      workflow.steps.push({
        step: 'AWARD_LETTER',
        status: 'in-progress',
        notes: content,
        attachments: attachments || [],
        startDate: new Date()
      });
    } else {
      // Update existing step
      workflow.steps[stepIndex].status = 'in-progress';
      workflow.steps[stepIndex].notes = content;
      
      if (attachments) {
        workflow.steps[stepIndex].attachments = attachments;
      }
      
      if (!workflow.steps[stepIndex].startDate) {
        workflow.steps[stepIndex].startDate = new Date();
      }
    }
    
    // Ensure current step is at least at AWARD_LETTER
    const stepOrder = [
      'ANALYST_REPORT',
      'INSIGHT_MANAGER_REVIEW',
      'CORPORATE_MANAGER_REVIEW',
      'COMMITTEE_SCHEDULING',
      'COMMITTEE_SUBMISSION',
      'COMMITTEE_MEETING',
      'RECOMMENDATION',
      'FINAL_APPROVAL',
      'AWARD_LETTER',
      'CONDITIONS_FULFILLMENT',
      'AGREEMENT_SIGNING',
      'DISBURSEMENT',
      'RECEIPT_CONFIRMATION'
    ];
    
    const currentStepIndex = stepOrder.indexOf(workflow.currentStep);
    const awardLetterIndex = stepOrder.indexOf('AWARD_LETTER');
    
    if (currentStepIndex < awardLetterIndex) {
      workflow.currentStep = 'AWARD_LETTER';
    }
    
    await workflow.save();
    
    res.json({
      message: 'Award letter generated successfully',
      workflow
    });
  } catch (err) {
    console.error('Error generating award letter:', err);
    res.status(500).json({ message: 'Error generating award letter', error: err.message });
  }
});

// Generate funding agreement
router.post('/:id/funding-agreement', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { content, attachments } = req.body;
    
    if (!content) {
      return res.status(400).json({ message: 'Funding agreement content is required' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Find the agreement signing step
    const stepIndex = workflow.steps.findIndex(s => s.step === 'AGREEMENT_SIGNING');
    
    if (stepIndex === -1) {
      // If step doesn't exist, create it
      workflow.steps.push({
        step: 'AGREEMENT_SIGNING',
        status: 'in-progress',
        notes: content,
        attachments: attachments || [],
        startDate: new Date()
      });
    } else {
      // Update existing step
      workflow.steps[stepIndex].status = 'in-progress';
      workflow.steps[stepIndex].notes = content;
      
      if (attachments) {
        workflow.steps[stepIndex].attachments = attachments;
      }
      
      if (!workflow.steps[stepIndex].startDate) {
        workflow.steps[stepIndex].startDate = new Date();
      }
    }
    
    // Ensure current step is at least at AGREEMENT_SIGNING
    const stepOrder = [
      'ANALYST_REPORT',
      'INSIGHT_MANAGER_REVIEW',
      'CORPORATE_MANAGER_REVIEW',
      'COMMITTEE_SCHEDULING',
      'COMMITTEE_SUBMISSION',
      'COMMITTEE_MEETING',
      'RECOMMENDATION',
      'FINAL_APPROVAL',
      'AWARD_LETTER',
      'CONDITIONS_FULFILLMENT',
      'AGREEMENT_SIGNING',
      'DISBURSEMENT',
      'RECEIPT_CONFIRMATION'
    ];
    
    const currentStepIndex = stepOrder.indexOf(workflow.currentStep);
    const agreementIndex = stepOrder.indexOf('AGREEMENT_SIGNING');
    
    if (currentStepIndex < agreementIndex) {
      workflow.currentStep = 'AGREEMENT_SIGNING';
    }
    
    await workflow.save();
    
    res.json({
      message: 'Funding agreement generated successfully',
      workflow
    });
  } catch (err) {
    console.error('Error generating funding agreement:', err);
    res.status(500).json({ message: 'Error generating funding agreement', error: err.message });
  }
});

// Record disbursement
router.post('/:id/disbursement', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const { amount, date, notes, attachments } = req.body;
    
    if (!amount || !date) {
      return res.status(400).json({ message: 'Disbursement amount and date are required' });
    }
    
    const workflow = await ApprovalWorkflow.findById(req.params.id);
    
    if (!workflow) {
      return res.status(404).json({ message: 'Approval workflow not found' });
    }
    
    // Find the disbursement step
    const stepIndex = workflow.steps.findIndex(s => s.step === 'DISBURSEMENT');
    
    if (stepIndex === -1) {
      // If step doesn't exist, create it
      workflow.steps.push({
        step: 'DISBURSEMENT',
        status: 'in-progress',
        notes: notes || `Disbursement of ${amount} on ${date}`,
        attachments: attachments || [],
        startDate: new Date()
      });
    } else {
      // Update existing step
      workflow.steps[stepIndex].status = 'in-progress';
      workflow.steps[stepIndex].notes = notes || `Disbursement of ${amount} on ${date}`;
      
      if (attachments) {
        workflow.steps[stepIndex].attachments = attachments;
      }
      
      if (!workflow.steps[stepIndex].startDate) {
        workflow.steps[stepIndex].startDate = new Date();
      }
    }
    
    // Ensure current step is at least at DISBURSEMENT
    const stepOrder = [
      'ANALYST_REPORT',
      'INSIGHT_MANAGER_REVIEW',
      'CORPORATE_MANAGER_REVIEW',
      'COMMITTEE_SCHEDULING',
      'COMMITTEE_SUBMISSION',
      'COMMITTEE_MEETING',
      'RECOMMENDATION',
      'FINAL_APPROVAL',
      'AWARD_LETTER',
      'CONDITIONS_FULFILLMENT',
      'AGREEMENT_SIGNING',
      'DISBURSEMENT',
      'RECEIPT_CONFIRMATION'
    ];
    
    const currentStepIndex = stepOrder.indexOf(workflow.currentStep);
    const disbursementIndex = stepOrder.indexOf('DISBURSEMENT');
    
    if (currentStepIndex < disbursementIndex) {
      workflow.currentStep = 'DISBURSEMENT';
    }
    
    await workflow.save();
    
    // Update application with funding amount
    await Application.findByIdAndUpdate(workflow.applicationId, {
      fundingAmount: amount,
      status: 'disbursed'
    });
    
    // Update programme budget
    const application = await Application.findById(workflow.applicationId);
    if (application) {
      await FundingProgramme.findByIdAndUpdate(workflow.programmeId, {
        $inc: { 'budget.allocated': amount }
      });
    }
    
    res.json({
      message: 'Disbursement recorded successfully',
      workflow
    });
  } catch (err) {
    console.error('Error recording disbursement:', err);
    res.status(500).json({ message: 'Error recording disbursement', error: err.message });
  }
});

module.exports = router;
