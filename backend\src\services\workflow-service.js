const Workflow = require('../models/workflow');
const WorkflowAssignment = require('../models/workflow-assignment');
const Application = require('../models/application');
const { ApplicationMainStage, ApplicationSubStage, MainStageToSubStagesMap, StageStatus } = require('../models/stage-status-enums');

class WorkflowService {
  /**
   * Create a new workflow
   */
  async createWorkflow(workflowData, userId) {
    try {
      const workflow = new Workflow({
        ...workflowData,
        createdBy: userId,
        updatedBy: userId
      });
      
      return await workflow.save();
    } catch (error) {
      throw new Error(`Failed to create workflow: ${error.message}`);
    }
  }

  /**
   * Update an existing workflow
   */
  async updateWorkflow(workflowId, updates, userId) {
    try {
      const workflow = await Workflow.findByIdAndUpdate(
        workflowId,
        {
          ...updates,
          updatedBy: userId,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      );
      
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      
      return workflow;
    } catch (error) {
      throw new Error(`Failed to update workflow: ${error.message}`);
    }
  }

  /**
   * Get all workflows
   */
  async getAllWorkflows(filters = {}) {
    try {
      const query = {};
      
      if (filters.status) {
        query.status = filters.status;
      }
      
      if (filters.type) {
        query.type = filters.type;
      }
      
      return await Workflow.find(query)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email')
        .populate('applicablePrograms', 'name')
        .sort({ priority: -1, createdAt: -1 });
    } catch (error) {
      throw new Error(`Failed to fetch workflows: ${error.message}`);
    }
  }

  /**
   * Get a single workflow by ID
   */
  async getWorkflowById(workflowId) {
    try {
      const workflow = await Workflow.findById(workflowId)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email')
        .populate('applicablePrograms', 'name');
      
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      
      return workflow;
    } catch (error) {
      throw new Error(`Failed to fetch workflow: ${error.message}`);
    }
  }

  /**
   * Delete a workflow
   */
  async deleteWorkflow(workflowId) {
    try {
      // Check if workflow is assigned to any applications
      const assignmentCount = await WorkflowAssignment.countDocuments({ workflowId });
      
      if (assignmentCount > 0) {
        throw new Error(`Cannot delete workflow. It is assigned to ${assignmentCount} applications.`);
      }
      
      const workflow = await Workflow.findByIdAndDelete(workflowId);
      
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      
      return { success: true, message: 'Workflow deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete workflow: ${error.message}`);
    }
  }

  /**
   * Assign a workflow to an application
   */
  async assignWorkflowToApplication(applicationId, workflowId, userId) {
    try {
      // Check if application exists
      const application = await Application.findOne({ id: applicationId });
      if (!application) {
        throw new Error('Application not found');
      }
      
      // Check if workflow exists and is active
      const workflow = await Workflow.findById(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      
      if (workflow.status !== 'Active') {
        throw new Error('Workflow is not active');
      }
      
      // Check if application already has a workflow assigned
      const existingAssignment = await WorkflowAssignment.findOne({ applicationId });
      if (existingAssignment) {
        throw new Error('Application already has a workflow assigned');
      }
      
      // Create workflow assignment
      const assignment = new WorkflowAssignment({
        applicationId,
        workflowId,
        currentStage: {
          mainStage: workflow.stages[0].mainStage,
          subStage: workflow.stages[0].subStages[0].name
        },
        stageHistory: [{
          mainStage: workflow.stages[0].mainStage,
          subStage: workflow.stages[0].subStages[0].name,
          status: StageStatus.NOT_STARTED,
          startedAt: new Date()
        }],
        assignedBy: userId
      });
      
      return await assignment.save();
    } catch (error) {
      throw new Error(`Failed to assign workflow: ${error.message}`);
    }
  }

  /**
   * Get workflow assignment for an application
   */
  async getApplicationWorkflow(applicationId) {
    try {
      const assignment = await WorkflowAssignment.findOne({ applicationId })
        .populate('workflowId')
        .populate('assignedBy', 'name email')
        .populate('stageHistory.completedBy', 'name email')
        .populate('overrides.overriddenBy', 'name email');
      
      return assignment;
    } catch (error) {
      throw new Error(`Failed to fetch application workflow: ${error.message}`);
    }
  }

  /**
   * Update application stage in workflow
   */
  async updateApplicationStage(applicationId, stageUpdate, userId) {
    try {
      const assignment = await WorkflowAssignment.findOne({ applicationId })
        .populate('workflowId');
      
      if (!assignment) {
        throw new Error('No workflow assigned to this application');
      }
      
      const workflow = assignment.workflowId;
      
      // Validate stage transition based on workflow type
      if (workflow.type === 'Sequential') {
        // Validate sequential progression
        const currentStageIndex = this.getStageIndex(workflow, assignment.currentStage);
        const newStageIndex = this.getStageIndex(workflow, stageUpdate);
        
        if (newStageIndex !== currentStageIndex + 1 && stageUpdate.status !== StageStatus.SKIPPED) {
          throw new Error('Invalid stage progression for sequential workflow');
        }
      }
      
      // Update stage history
      const historyEntry = {
        mainStage: stageUpdate.mainStage,
        subStage: stageUpdate.subStage,
        status: stageUpdate.status,
        startedAt: new Date(),
        completedAt: stageUpdate.status === StageStatus.COMPLETED ? new Date() : null,
        completedBy: stageUpdate.status === StageStatus.COMPLETED ? userId : null,
        notes: stageUpdate.notes
      };
      
      assignment.stageHistory.push(historyEntry);
      assignment.currentStage = {
        mainStage: stageUpdate.mainStage,
        subStage: stageUpdate.subStage
      };
      
      // Execute automation rules
      await this.executeAutomationRules(workflow, stageUpdate, applicationId);
      
      return await assignment.save();
    } catch (error) {
      throw new Error(`Failed to update application stage: ${error.message}`);
    }
  }

  /**
   * Override workflow stage
   */
  async overrideWorkflowStage(applicationId, override, userId) {
    try {
      const assignment = await WorkflowAssignment.findOne({ applicationId });
      
      if (!assignment) {
        throw new Error('No workflow assigned to this application');
      }
      
      // Add override record
      assignment.overrides.push({
        fromStage: assignment.currentStage,
        toStage: override.toStage,
        reason: override.reason,
        overriddenBy: userId,
        overriddenAt: new Date()
      });
      
      // Update current stage
      assignment.currentStage = override.toStage;
      
      // Add to stage history
      assignment.stageHistory.push({
        mainStage: override.toStage.mainStage,
        subStage: override.toStage.subStage,
        status: StageStatus.ACTIVE,
        startedAt: new Date(),
        notes: `Override: ${override.reason}`
      });
      
      return await assignment.save();
    } catch (error) {
      throw new Error(`Failed to override workflow stage: ${error.message}`);
    }
  }

  /**
   * Get workflow statistics
   */
  async getWorkflowStatistics(workflowId) {
    try {
      const assignments = await WorkflowAssignment.find({ workflowId });
      
      const statistics = {
        totalApplications: assignments.length,
        stageDistribution: {},
        averageTimePerStage: {},
        completionRate: 0
      };
      
      // Calculate stage distribution
      assignments.forEach(assignment => {
        const stageKey = `${assignment.currentStage.mainStage}-${assignment.currentStage.subStage}`;
        statistics.stageDistribution[stageKey] = (statistics.stageDistribution[stageKey] || 0) + 1;
      });
      
      // Calculate average time per stage
      const stageTimes = {};
      assignments.forEach(assignment => {
        assignment.stageHistory.forEach(stage => {
          if (stage.completedAt) {
            const stageKey = `${stage.mainStage}-${stage.subStage}`;
            const duration = stage.completedAt - stage.startedAt;
            
            if (!stageTimes[stageKey]) {
              stageTimes[stageKey] = [];
            }
            stageTimes[stageKey].push(duration);
          }
        });
      });
      
      Object.keys(stageTimes).forEach(stageKey => {
        const times = stageTimes[stageKey];
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        statistics.averageTimePerStage[stageKey] = Math.round(avgTime / (1000 * 60 * 60)); // Convert to hours
      });
      
      // Calculate completion rate
      const completedCount = assignments.filter(a => 
        a.stageHistory.some(h => h.status === StageStatus.COMPLETED && h.mainStage === 'Application Approval')
      ).length;
      
      statistics.completionRate = assignments.length > 0 
        ? Math.round((completedCount / assignments.length) * 100) 
        : 0;
      
      return statistics;
    } catch (error) {
      throw new Error(`Failed to get workflow statistics: ${error.message}`);
    }
  }

  /**
   * Helper method to get stage index in workflow
   */
  getStageIndex(workflow, stage) {
    for (let i = 0; i < workflow.stages.length; i++) {
      const workflowStage = workflow.stages[i];
      if (workflowStage.mainStage === stage.mainStage) {
        const subStageIndex = workflowStage.subStages.findIndex(s => s.name === stage.subStage);
        if (subStageIndex !== -1) {
          return { mainIndex: i, subIndex: subStageIndex };
        }
      }
    }
    return null;
  }

  /**
   * Execute automation rules for a stage update
   */
  async executeAutomationRules(workflow, stageUpdate, applicationId) {
    try {
      const applicableRules = workflow.automationRules.filter(rule => 
        rule.enabled && rule.trigger === 'StageComplete'
      );
      
      for (const rule of applicableRules) {
        if (rule.triggerDetails.stage === `${stageUpdate.mainStage}-${stageUpdate.subStage}`) {
          for (const action of rule.actions) {
            await this.executeAction(action, applicationId);
          }
        }
      }
    } catch (error) {
      console.error('Error executing automation rules:', error);
      // Don't throw - automation failures shouldn't block stage updates
    }
  }

  /**
   * Execute a single automation action
   */
  async executeAction(action, applicationId) {
    switch (action.type) {
      case 'SendNotification':
        // Integrate with notification service
        console.log(`Sending notification for application ${applicationId}:`, action.details);
        break;
        
      case 'AssignReviewer':
        // Integrate with user assignment logic
        console.log(`Assigning reviewer for application ${applicationId}:`, action.details);
        break;
        
      case 'UpdateStatus':
        // Update application status
        console.log(`Updating status for application ${applicationId}:`, action.details);
        break;
        
      case 'CreateTask':
        // Create a task in task management system
        console.log(`Creating task for application ${applicationId}:`, action.details);
        break;
        
      default:
        console.warn(`Unknown action type: ${action.type}`);
    }
  }
}

module.exports = new WorkflowService();