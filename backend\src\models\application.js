const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
  street: String,
  city: String,
  province: String,
  postalCode: String,
  country: String
});

const personalInfoSchema = new mongoose.Schema({
  firstName: String,
  lastName: String,
  email: String,
  phone: String,
  idNumber: String,
  address: addressSchema,
  applicationObjective: String
});

const businessInfoSchema = new mongoose.Schema({
  legalName: { type: String, required: true },
  tradingName: { type: String, required: true },
  registrationNumber: { type: String, required: false },
  cipcRegistrationNumber: { type: String, required: true },
  entityType: { 
    type: String,
    enum: ['Pty Ltd', 'Close Corporation', 'Non-Profit', 'Partnership', 'Sole Proprietor', 'Other'],
    required: true 
  },
  startTradingDate: { type: Date, required: true },
  cipcRegistrationDocument: { type: String }, // File path
  vatNumber: String,
  industry: String,
  businessType: String,
  yearEstablished: Number,
  employeeCount: Number,
  address: addressSchema,
  website: String
});

const contactDetailsSchema = new mongoose.Schema({
  name: { type: String, required: true },
  surname: { type: String, required: true },
  mainOfficeNumber: { type: String, required: true },
  cellphoneNumber: { type: String, required: true },
  email: { type: String, required: true, match: /.+\@.+\..+/ },
  applicationObjective: { type: String }
});

const financialInfoSchema = new mongoose.Schema({
  annualTurnover: Number,
  netProfit: Number,
  currentAssets: Number,
  currentLiabilities: Number,
  totalDebt: Number,
  fundingPurpose: String,
  fundingAmount: Number,
  collateral: String,
  timeframe: String,
  taxNumber: Number,
  taxClearanceExpiryDate: Date,
  taxClearanceCertificateDocument: String, // File path
  vatRegistered: { type: Boolean, default: false },
  vatRegistrationNumber: String,
  vatCertificateDocument: String // File path
});

// Import stage status enums
const { ApplicationMainStage, ApplicationSubStage, StageStatus } = require('./stage-status-enums');

// Schema for individual stage status entries
const stageStatusSchema = new mongoose.Schema({
  status: {
    type: String,
    enum: Object.values(StageStatus),
    default: StageStatus.NOT_STARTED
  },
  startedAt: Date,
  completedAt: Date,
  assignedTo: String,
  notes: String,
  overrideReason: String,
  overrideBy: String,
  overrideDate: Date
});

// Schema for substages
const subStageSchema = new mongoose.Schema({
  subStage: {
    type: String,
    enum: Object.values(ApplicationSubStage),
    required: true
  },
  status: stageStatusSchema,
  history: [stageStatusSchema]
});

// Schema for main stages
const mainStageSchema = new mongoose.Schema({
  mainStage: {
    type: String,
    enum: Object.values(ApplicationMainStage),
    required: true
  },
  status: {
    type: String,
    enum: Object.values(StageStatus),
    default: StageStatus.NOT_STARTED
  },
  subStages: [subStageSchema]
});

// Stage notes schema
const stageNotesSchema = new mongoose.Schema({
  strengths: [String],
  weaknesses: [String],
  opportunities: [String],
  threats: [String],
  generalNotes: [String]
});

const bbbeeProfileSchema = new mongoose.Schema({
  bbbeeLevel: { 
    type: String, 
    enum: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5', 'Level 6', 'Level 7', 'Level 8', 'Non-Compliant'] 
  },
  bbbeeCertificateExpiryDate: Date,
  blackOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackWomenOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackYouthOwnershipPercentage: { type: Number, min: 0, max: 100 },
  blackPeopleWithDisabilityOwnershipPercentage: { type: Number, min: 0, max: 100 },
  bbbeeCategory: String,
  isExxaroSupplier: { type: Boolean, default: false },
  sdEd: String,
  hasLatestInvoice: { type: Boolean, default: false },
  financialYearEnd: Date,
  lastAnnualTurnover: Number,
  hasAnnualFinancialStatements: { type: Boolean, default: false }
});

const applicationSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  programmeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FundingProgramme',
    required: true
  },
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor',
    required: true
  },
  registrationNumber: String,
  fundingAmount: Number,
  submissionDate: {
    type: Date,
    default: Date.now
  },
  // Three-level hierarchy fields
  currentMainStage: {
    type: String,
    enum: Object.values(ApplicationMainStage),
    default: ApplicationMainStage.ONBOARDING
  },
  currentSubStage: {
    type: String,
    enum: Object.values(ApplicationSubStage),
    default: ApplicationSubStage.BENEFICIARY_REGISTRATION
  },
  currentStageStatus: {
    type: String,
    enum: Object.values(StageStatus),
    default: StageStatus.NOT_STARTED
  },
  stageHierarchy: [mainStageSchema],
  status: {
    type: String,
    enum: ['pending', 'in-review', 'approved', 'rejected', 'withdrawn-by-applicant', 'on-hold', 'sent-back'],
    default: 'pending'
  },
  owner: String,
  personalInfo: personalInfoSchema,
  businessInfo: businessInfoSchema,
  contactDetails: contactDetailsSchema,
  financialInfo: financialInfoSchema,
  bbbeeProfile: bbbeeProfileSchema,
  smeInterview: {
    interviewDate: Date,
    interviewer: String,
    participants: [String],
    notes: String,
    recommendations: [String],
    followUpActions: [String],
    attachedDocuments: [String],
    status: {
      type: String,
      enum: ['SCHEDULED', 'COMPLETED', 'PENDING_REVIEW', 'FOLLOW_UP_REQUIRED'],
      default: 'SCHEDULED'
    }
  },
  documents: [String],
  notes: [String],
  tags: [String],
  // Audit log for stage status changes
  stageStatusAuditLog: [{
    timestamp: {
      type: Date,
      default: Date.now
    },
    mainStage: String,
    subStage: String,
    fromStatus: String,
    toStatus: String,
    changedBy: String,
    reason: String,
    isOverride: {
      type: Boolean,
      default: false
    }
  }],
  score: Number,
  assignedTo: String,
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  approvalWorkflow: {
    currentStep: {
      type: String,
      enum: [
        'ANALYST_REPORT',
        'INSIGHT_MANAGER_REVIEW',
        'CORPORATE_MANAGER_REVIEW',
        'COMMITTEE_SCHEDULING',
        'COMMITTEE_SUBMISSION',
        'COMMITTEE_MEETING',
        'RECOMMENDATION',
        'FINAL_APPROVAL',
        'AWARD_LETTER',
        'CONDITIONS_FULFILLMENT',
        'AGREEMENT_SIGNING',
        'DISBURSEMENT',
        'RECEIPT_CONFIRMATION'
      ],
      default: 'ANALYST_REPORT'
    },
    steps: [{
      step: String,
      status: {
        type: String,
        enum: ['pending', 'in-progress', 'completed', 'rejected'],
        default: 'pending'
      },
      assignedTo: String,
      startDate: Date,
      completionDate: Date,
      notes: String,
      attachments: [String]
    }],
    committeeMeetings: [{
      meetingId: {
        type: String,
        ref: 'CommitteeMeeting'
      },
      status: {
        type: String,
        enum: ['scheduled', 'presented', 'approved', 'rejected', 'deferred'],
        default: 'scheduled'
      },
      presentationDate: Date,
      notes: String
    }]
  }
});

// Pre-save middleware to generate application ID
applicationSchema.pre('save', async function(next) {
  if (!this.id) {
    const count = await this.constructor.countDocuments();
    const nextNumber = (count + 1).toString().padStart(3, '0');
    this.id = `APP-2025-${nextNumber}`;
  }
  next();
});

const Application = mongoose.model('Application', applicationSchema);

module.exports = Application;
