# Scorecard Template 400 Error - Comprehensive Debug Guide

## Debug Logging Added

I've added extensive debug logging to help identify the exact cause of the persistent 400 Bad Request error. The logging has been added to:

### 1. Frontend - Template Form Component
**File:** `frontend/src/app/Components/scorecard-management/template-form/template-form.component.ts`

- Enhanced `onSubmit()` method with detailed logging
- Enhanced `buildTemplateData()` method to log:
  - Raw form values
  - Field-by-field analysis
  - Transformation process
  - Final payload structure

### 2. Frontend - Template Service
**File:** `frontend/src/app/Components/scorecard-management/services/scorecard-template.service.ts`

- Enhanced `createTemplate()` method to log:
  - Incoming template data
  - Field breakdown
  - Final API payload
  - Error details

### 3. Backend - Scorecard Templates Route
**File:** `backend/src/routes/scorecard-templates.js`

- Enhanced POST route handler with:
  - Request headers logging
  - Request body analysis
  - Field-by-field validation
  - Detailed error reporting

## Testing Instructions

### Step 1: Start Both Servers
1. Start the backend server:
   ```bash
   cd backend
   npm start
   ```

2. Start the frontend server with proxy:
   ```bash
   cd frontend
   ng serve --proxy-config proxy.conf.json
   ```

### Step 2: Open Browser Developer Tools
1. Open Chrome/Firefox Developer Tools (F12)
2. Go to the Console tab
3. Clear the console
4. Go to the Network tab
5. Clear any existing requests

### Step 3: Create a Test Template
1. Navigate to Scorecard Management
2. Click "Create New Template"
3. Fill in the form:
   - Template Name: "Debug Test Template"
   - Stage: Select "Onboarding"
   - Sub-Stage: Select "Pre-Screening"
   - Template Scope: "Programme Specific"
   - Select at least one programme
   - Add one criterion:
     - Name: "Test Criterion"
     - Weight: 100
     - Max Score: 10

### Step 4: Submit and Capture Logs
1. Click "Create Template"
2. Immediately check:
   - **Console Tab**: Look for all [DEBUG] messages
   - **Network Tab**: Find the POST request to `/api/v1/scorecard-templates`
     - Click on it
     - Check Headers tab for request URL
     - Check Payload/Request tab for the actual data sent
     - Check Response tab for the error details

### Step 5: Check Backend Logs
Look in the terminal where the backend is running for:
- `[DEBUG] ========== POST /api/v1/scorecard-templates ==========`
- Field analysis logs
- Any validation errors

## What to Look For

### In Frontend Console:
1. **Form Validation**:
   - `[DEBUG] Form valid: true/false`
   - Any form field errors

2. **Data Building**:
   - `[DEBUG] buildTemplateData - Raw form value:`
   - Check if `mainStage` and `subStage` have values
   - Check if `category` is being set
   - Check if `applicableStages` and `applicableSubstages` arrays are created

3. **API Call**:
   - `[DEBUG] Final template data being sent to API:`
   - Verify all required fields are present

### In Network Tab:
1. **Request URL**: Should be `http://localhost:3002/api/v1/scorecard-templates`
   - If it shows `http://localhost:4200/...`, the proxy isn't working

2. **Request Payload**: Should contain:
   ```json
   {
     "name": "Debug Test Template",
     "category": "pre-screening",
     "applicableStages": ["ONBOARDING"],
     "applicableSubstages": ["pre-screening"],
     "criteria": [...],
     // ... other fields
   }
   ```

3. **Response**: Check the exact error message and details

### In Backend Console:
1. **Request Reception**:
   - `[DEBUG] Request body type:` should be "object"
   - `[DEBUG] Request body keys:` should list all fields

2. **Field Analysis**:
   - Check each field's value and type
   - Look for any `undefined` or missing values

3. **Validation**:
   - Check which validation is failing
   - Look for Mongoose validation errors

## Common Issues to Check

1. **Proxy Not Working**:
   - Ensure frontend is started with `--proxy-config proxy.conf.json`
   - Check if backend is running on port 3002

2. **Missing Fields**:
   - `name` and `category` are required by backend
   - Check if `category` is being set from `subStage`

3. **Data Type Issues**:
   - `applicableStages` and `applicableSubstages` should be arrays
   - `criteria` should be an array with at least one item

4. **Mongoose Validation**:
   - Total criteria weight must equal 100%
   - Each criterion must have required fields

## Next Steps

After running the test:
1. Copy all [DEBUG] logs from both frontend and backend
2. Note the exact error message and where it occurs
3. Check if the data transformation is working correctly
4. Identify which specific field or validation is causing the 400 error

With this detailed logging, we should be able to pinpoint the exact cause of the persistent 400 error.