const mongoose = require('mongoose');
const { Schema } = mongoose;

// Schema for scorecard version history
const scorecardVersionSchema = new Schema({
  scorecardId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Scorecard', 
    required: true 
  },
  versionNumber: { 
    type: Number, 
    required: true 
  },
  data: {
    name: String,
    description: String,
    criteria: [{
      id: String,
      name: String,
      description: String,
      weight: Number,
      maxScore: Number,
      score: Number,
      comments: String
    }],
    totalScore: Number,
    maxPossibleScore: Number,
    status: String
  },
  metadata: {
    isAutoSave: { 
      type: Boolean, 
      default: false 
    },
    isFinalSave: { 
      type: Boolean, 
      default: false 
    },
    saveType: { 
      type: String, 
      enum: ['auto', 'manual', 'final'], 
      default: 'manual' 
    },
    conflictResolved: { 
      type: Boolean, 
      default: false 
    },
    changesSummary: String,
    conflictDetails: {
      hadConflicts: { type: Boolean, default: false },
      resolvedConflicts: [{
        field: String,
        yourValue: Schema.Types.Mixed,
        currentValue: Schema.Types.Mixed,
        resolution: String
      }]
    }
  },
  createdBy: { 
    type: String, 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Indexes for performance
scorecardVersionSchema.index({ scorecardId: 1, versionNumber: -1 });
scorecardVersionSchema.index({ scorecardId: 1, createdAt: -1 });
scorecardVersionSchema.index({ scorecardId: 1, 'metadata.saveType': 1 });

// Static methods
scorecardVersionSchema.statics.getLatestVersion = function(scorecardId) {
  return this.findOne({ scorecardId })
    .sort({ versionNumber: -1 })
    .exec();
};

scorecardVersionSchema.statics.getVersionHistory = function(scorecardId, limit = 10, offset = 0) {
  return this.find({ scorecardId })
    .sort({ versionNumber: -1 })
    .limit(limit)
    .skip(offset)
    .select('-data.criteria') // Exclude detailed criteria for list view
    .exec();
};

scorecardVersionSchema.statics.createVersion = async function(scorecard, saveType, userId, changesSummary) {
  const latestVersion = await this.getLatestVersion(scorecard._id);
  const versionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;
  
  const versionData = {
    scorecardId: scorecard._id,
    versionNumber,
    data: {
      name: scorecard.name,
      description: scorecard.description,
      criteria: scorecard.criteria,
      totalScore: scorecard.totalScore,
      maxPossibleScore: scorecard.maxPossibleScore,
      status: scorecard.status
    },
    metadata: {
      isAutoSave: saveType === 'auto',
      isFinalSave: saveType === 'final',
      saveType,
      changesSummary
    },
    createdBy: userId
  };
  
  return this.create(versionData);
};

// Instance methods
scorecardVersionSchema.methods.restore = async function() {
  const Scorecard = mongoose.model('Scorecard');
  const scorecard = await Scorecard.findById(this.scorecardId);
  
  if (!scorecard) {
    throw new Error('Scorecard not found');
  }
  
  // Update scorecard with version data
  scorecard.name = this.data.name;
  scorecard.description = this.data.description;
  scorecard.criteria = this.data.criteria;
  scorecard.totalScore = this.data.totalScore;
  scorecard.maxPossibleScore = this.data.maxPossibleScore;
  scorecard.currentVersion = this.versionNumber;
  
  return scorecard.save();
};

// Virtual for display
scorecardVersionSchema.virtual('displayName').get(function() {
  const typeLabel = this.metadata.isAutoSave ? 'Auto-save' : 
                   this.metadata.isFinalSave ? 'Final' : 'Manual save';
  return `Version ${this.versionNumber} - ${typeLabel}`;
});

// Create model
const ScorecardVersion = mongoose.model('ScorecardVersion', scorecardVersionSchema);

module.exports = ScorecardVersion;