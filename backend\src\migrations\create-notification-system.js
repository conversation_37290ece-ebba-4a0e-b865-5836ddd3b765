const mongoose = require('mongoose');

/**
 * Migration: Create Notification System
 * Sets up indexes and initial configuration for the notification system
 */
async function up() {
  console.log('Starting notification system migration...');

  try {
    const db = mongoose.connection.db;

    // Create indexes for Notification collection
    console.log('Creating indexes for notifications collection...');
    await db.collection('notifications').createIndexes([
      { key: { status: 1, scheduledFor: 1 }, name: 'status_scheduledFor_idx' },
      { key: { type: 1, category: 1 }, name: 'type_category_idx' },
      { key: { createdBy: 1, createdAt: -1 }, name: 'createdBy_createdAt_idx' },
      { key: { targetUsers: 1 }, name: 'targetUsers_idx' },
      { key: { targetRoles: 1 }, name: 'targetRoles_idx' },
      { key: { 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 }, name: 'relatedEntity_idx' },
      { key: { templateId: 1 }, name: 'templateId_idx' },
      { key: { id: 1 }, name: 'notification_id_idx', unique: true, sparse: true },
      { key: { createdAt: -1 }, name: 'createdAt_desc_idx' },
      { key: { sentAt: -1 }, name: 'sentAt_desc_idx', sparse: true }
    ]);

    // Create indexes for UserNotification collection
    console.log('Creating indexes for usernotifications collection...');
    await db.collection('usernotifications').createIndexes([
      { key: { userId: 1, createdAt: -1 }, name: 'userId_createdAt_idx' },
      { key: { userId: 1, isRead: 1 }, name: 'userId_isRead_idx' },
      { key: { notificationId: 1 }, name: 'notificationId_idx' },
      { key: { status: 1 }, name: 'status_idx' },
      { key: { scheduledFor: 1 }, name: 'scheduledFor_idx' },
      { key: { userId: 1, notificationId: 1 }, name: 'userId_notificationId_unique_idx', unique: true },
      { key: { createdAt: -1 }, name: 'usernotif_createdAt_desc_idx' },
      { key: { readAt: -1 }, name: 'readAt_desc_idx', sparse: true }
    ]);

    // Create indexes for NotificationTemplate collection
    console.log('Creating indexes for notificationtemplates collection...');
    await db.collection('notificationtemplates').createIndexes([
      { key: { type: 1, isActive: 1 }, name: 'type_isActive_idx' },
      { key: { category: 1, isActive: 1 }, name: 'category_isActive_idx' },
      { key: { name: 1 }, name: 'name_idx' },
      { key: { createdBy: 1 }, name: 'template_createdBy_idx' },
      { key: { createdAt: -1 }, name: 'template_createdAt_desc_idx' },
      { key: { lastUsed: -1 }, name: 'lastUsed_desc_idx', sparse: true },
      { key: { usageCount: -1 }, name: 'usageCount_desc_idx' }
    ]);

    // Create compound indexes for better query performance
    console.log('Creating compound indexes...');
    await db.collection('notifications').createIndexes([
      { 
        key: { targetAudience: 1, status: 1, scheduledFor: 1 }, 
        name: 'targetAudience_status_scheduledFor_idx' 
      },
      { 
        key: { type: 1, priority: 1, createdAt: -1 }, 
        name: 'type_priority_createdAt_idx' 
      }
    ]);

    await db.collection('usernotifications').createIndexes([
      { 
        key: { userId: 1, status: 1, scheduledFor: 1 }, 
        name: 'userId_status_scheduledFor_idx' 
      },
      { 
        key: { userId: 1, isRead: 1, createdAt: -1 }, 
        name: 'userId_isRead_createdAt_idx' 
      }
    ]);

    // Create text indexes for search functionality
    console.log('Creating text indexes for search...');
    await db.collection('notifications').createIndex(
      { 
        title: 'text', 
        message: 'text',
        'relatedEntity.entityId': 'text'
      },
      { 
        name: 'notification_text_search_idx',
        weights: {
          title: 10,
          message: 5,
          'relatedEntity.entityId': 3
        }
      }
    );

    await db.collection('notificationtemplates').createIndex(
      { 
        name: 'text', 
        description: 'text',
        'content.inApp.title': 'text',
        'content.inApp.message': 'text'
      },
      { 
        name: 'template_text_search_idx',
        weights: {
          name: 10,
          description: 5,
          'content.inApp.title': 8,
          'content.inApp.message': 3
        }
      }
    );

    // Create TTL indexes for cleanup
    console.log('Creating TTL indexes for automatic cleanup...');
    
    // Auto-delete old notifications after 1 year (365 days)
    await db.collection('notifications').createIndex(
      { createdAt: 1 },
      { 
        name: 'notification_ttl_idx',
        expireAfterSeconds: 365 * 24 * 60 * 60 // 1 year in seconds
      }
    );

    // Auto-delete old user notifications after 6 months (180 days)
    await db.collection('usernotifications').createIndex(
      { createdAt: 1 },
      { 
        name: 'usernotification_ttl_idx',
        expireAfterSeconds: 180 * 24 * 60 * 60 // 6 months in seconds
      }
    );

    // Create partial indexes for performance optimization
    console.log('Creating partial indexes...');
    
    // Index only unread notifications
    await db.collection('usernotifications').createIndex(
      { userId: 1, createdAt: -1 },
      { 
        name: 'userId_createdAt_unread_idx',
        partialFilterExpression: { isRead: false }
      }
    );

    // Index only active notifications
    await db.collection('notifications').createIndex(
      { scheduledFor: 1, createdAt: -1 },
      { 
        name: 'scheduledFor_createdAt_active_idx',
        partialFilterExpression: { 
          status: { $in: ['DRAFT', 'SCHEDULED', 'SENDING'] }
        }
      }
    );

    // Index only active templates
    await db.collection('notificationtemplates').createIndex(
      { type: 1, category: 1, lastUsed: -1 },
      { 
        name: 'type_category_lastUsed_active_idx',
        partialFilterExpression: { isActive: true }
      }
    );

    // Create application event tracking collection and indexes
    console.log('Creating application events collection and indexes...');
    await db.createCollection('applicationevents');
    
    await db.collection('applicationevents').createIndexes([
      { key: { applicationId: 1, eventType: 1, timestamp: -1 }, name: 'app_event_type_time_idx' },
      { key: { eventType: 1, timestamp: -1 }, name: 'event_type_time_idx' },
      { key: { triggeredBy: 1, timestamp: -1 }, name: 'triggeredBy_time_idx' },
      { key: { timestamp: -1 }, name: 'timestamp_desc_idx' },
      { key: { applicationId: 1, timestamp: -1 }, name: 'app_time_idx' }
    ]);

    // TTL index for application events (keep for 2 years)
    await db.collection('applicationevents').createIndex(
      { timestamp: 1 },
      { 
        name: 'app_events_ttl_idx',
        expireAfterSeconds: 2 * 365 * 24 * 60 * 60 // 2 years in seconds
      }
    );

    // Create notification analytics collection for performance metrics
    console.log('Creating notification analytics collection...');
    await db.createCollection('notificationanalytics');
    
    await db.collection('notificationanalytics').createIndexes([
      { key: { date: 1, type: 1 }, name: 'date_type_idx' },
      { key: { notificationId: 1 }, name: 'analytics_notificationId_idx' },
      { key: { date: -1 }, name: 'analytics_date_desc_idx' }
    ]);

    // Create notification preferences collection for user settings
    console.log('Creating notification preferences collection...');
    await db.createCollection('notificationpreferences');
    
    await db.collection('notificationpreferences').createIndexes([
      { key: { userId: 1 }, name: 'prefs_userId_idx', unique: true },
      { key: { updatedAt: -1 }, name: 'prefs_updatedAt_idx' }
    ]);

    // Create notification delivery log collection for tracking
    console.log('Creating notification delivery log collection...');
    await db.createCollection('notificationdeliverylogs');
    
    await db.collection('notificationdeliverylogs').createIndexes([
      { key: { notificationId: 1, deliveryMethod: 1 }, name: 'notif_delivery_method_idx' },
      { key: { userId: 1, timestamp: -1 }, name: 'user_delivery_time_idx' },
      { key: { status: 1, timestamp: -1 }, name: 'delivery_status_time_idx' },
      { key: { timestamp: -1 }, name: 'delivery_timestamp_desc_idx' }
    ]);

    // TTL index for delivery logs (keep for 1 year)
    await db.collection('notificationdeliverylogs').createIndex(
      { timestamp: 1 },
      { 
        name: 'delivery_logs_ttl_idx',
        expireAfterSeconds: 365 * 24 * 60 * 60 // 1 year in seconds
      }
    );

    // Create notification queues collection for processing
    console.log('Creating notification queues collection...');
    await db.createCollection('notificationqueues');
    
    await db.collection('notificationqueues').createIndexes([
      { key: { status: 1, priority: -1, scheduledFor: 1 }, name: 'queue_processing_idx' },
      { key: { notificationId: 1 }, name: 'queue_notificationId_idx' },
      { key: { createdAt: -1 }, name: 'queue_createdAt_desc_idx' }
    ]);

    // TTL index for processed queue items (keep for 7 days)
    await db.collection('notificationqueues').createIndex(
      { createdAt: 1 },
      { 
        name: 'queue_ttl_idx',
        expireAfterSeconds: 7 * 24 * 60 * 60, // 7 days in seconds
        partialFilterExpression: { 
          status: { $in: ['COMPLETED', 'FAILED'] }
        }
      }
    );

    // Create system configuration document
    console.log('Creating system configuration...');
    const systemConfig = {
      _id: 'notification_system_config',
      version: '1.0.0',
      features: {
        realTimeNotifications: true,
        emailNotifications: true,
        smsNotifications: true,
        pushNotifications: true,
        scheduledNotifications: true,
        recurringNotifications: true,
        templateSystem: true,
        analyticsTracking: true,
        deliveryTracking: true
      },
      limits: {
        maxNotificationsPerUser: 1000,
        maxTemplatesPerUser: 50,
        maxBulkNotifications: 10000,
        rateLimitPerMinute: 100,
        maxRetryAttempts: 3
      },
      retention: {
        notifications: 365, // days
        userNotifications: 180, // days
        deliveryLogs: 365, // days
        applicationEvents: 730, // days
        queueItems: 7 // days
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('systemconfigurations').replaceOne(
      { _id: 'notification_system_config' },
      systemConfig,
      { upsert: true }
    );

    // Create notification system statistics document
    console.log('Initializing system statistics...');
    const systemStats = {
      _id: 'notification_system_stats',
      totalNotifications: 0,
      totalUserNotifications: 0,
      totalTemplates: 0,
      totalEvents: 0,
      deliveryStats: {
        inApp: { sent: 0, delivered: 0, failed: 0 },
        email: { sent: 0, delivered: 0, failed: 0 },
        sms: { sent: 0, delivered: 0, failed: 0 },
        push: { sent: 0, delivered: 0, failed: 0 }
      },
      lastUpdated: new Date(),
      createdAt: new Date()
    };

    await db.collection('systemstatistics').replaceOne(
      { _id: 'notification_system_stats' },
      systemStats,
      { upsert: true }
    );

    console.log('✅ Notification system migration completed successfully!');
    console.log('📊 Created indexes for optimal query performance');
    console.log('🗑️  Set up TTL indexes for automatic cleanup');
    console.log('⚙️  Initialized system configuration');
    console.log('📈 Set up analytics and tracking collections');

    return {
      success: true,
      message: 'Notification system migration completed successfully',
      indexesCreated: 25,
      collectionsCreated: 6,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Error during notification system migration:', error);
    throw error;
  }
}

/**
 * Rollback migration
 */
async function down() {
  console.log('Rolling back notification system migration...');

  try {
    const db = mongoose.connection.db;

    // Drop indexes from main collections
    console.log('Dropping notification system indexes...');
    
    const collections = [
      'notifications',
      'usernotifications', 
      'notificationtemplates',
      'applicationevents',
      'notificationanalytics',
      'notificationpreferences',
      'notificationdeliverylogs',
      'notificationqueues'
    ];

    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const indexes = await collection.listIndexes().toArray();
        
        for (const index of indexes) {
          if (index.name !== '_id_') { // Don't drop the default _id index
            await collection.dropIndex(index.name);
            console.log(`Dropped index: ${index.name} from ${collectionName}`);
          }
        }
      } catch (error) {
        console.warn(`Could not drop indexes from ${collectionName}:`, error.message);
      }
    }

    // Remove system configuration documents
    console.log('Removing system configuration documents...');
    await db.collection('systemconfigurations').deleteOne({ _id: 'notification_system_config' });
    await db.collection('systemstatistics').deleteOne({ _id: 'notification_system_stats' });

    // Optionally drop the additional collections (commented out for safety)
    /*
    console.log('Dropping additional collections...');
    const additionalCollections = [
      'applicationevents',
      'notificationanalytics', 
      'notificationpreferences',
      'notificationdeliverylogs',
      'notificationqueues'
    ];

    for (const collectionName of additionalCollections) {
      try {
        await db.collection(collectionName).drop();
        console.log(`Dropped collection: ${collectionName}`);
      } catch (error) {
        console.warn(`Could not drop collection ${collectionName}:`, error.message);
      }
    }
    */

    console.log('✅ Notification system migration rollback completed');

    return {
      success: true,
      message: 'Notification system migration rolled back successfully',
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Error during notification system migration rollback:', error);
    throw error;
  }
}

module.exports = {
  up,
  down,
  description: 'Create notification system with indexes and configuration'
};