const mongoose = require('mongoose');

const profileSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true // One-to-one relationship with User
  },
  profileType: {
    type: String,
    enum: ['individual', 'business', 'organization'],
    required: true
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  middleName: {
    type: String,
    trim: true
  },
  idNumber: {
    type: String,
    trim: true
  },
  dateOfBirth: {
    type: Date
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer_not_to_say']
  },
  nationality: {
    type: String,
    trim: true
  },
  contactDetails: {
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    phone: {
      type: String,
      trim: true
    },
    alternativePhone: {
      type: String,
      trim: true
    },
    address: {
      street: String,
      city: String,
      province: String,
      postalCode: String,
      country: String
    }
  },
  emergencyContact: {
    name: String,
    relationship: String,
    phone: String,
    email: String
  },
  professionalDetails: {
    occupation: String,
    employer: String,
    workAddress: {
      street: String,
      city: String,
      province: String,
      postalCode: String,
      country: String
    },
    yearsOfExperience: Number,
    qualifications: [String],
    skills: [String]
  },
  bankingDetails: {
    bankName: String,
    accountNumber: String,
    branchCode: String,
    accountType: {
      type: String,
      enum: ['savings', 'current', 'transmission']
    }
  },
  documents: [{
    type: {
      type: String,
      enum: ['id_document', 'proof_of_address', 'bank_statement', 'cv', 'qualification', 'other']
    },
    filename: String,
    originalName: String,
    path: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    verified: {
      type: Boolean,
      default: false
    }
  }],
  preferences: {
    language: {
      type: String,
      default: 'en'
    },
    timezone: {
      type: String,
      default: 'Africa/Johannesburg'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: true
      }
    }
  },
  // Relationships
  smeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SME'
  },
  corporateSponsorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CorporateSponsor'
  },
  // Status and verification
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending'],
    default: 'pending'
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDate: {
    type: Date
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verificationNotes: {
    type: String
  },
  // Audit fields
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastLoginDate: {
    type: Date
  },
  profileCompleteness: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
profileSchema.index({ userId: 1 });
profileSchema.index({ id: 1 });
profileSchema.index({ 'contactDetails.email': 1 });
profileSchema.index({ idNumber: 1 });
profileSchema.index({ status: 1 });
profileSchema.index({ isVerified: 1 });
profileSchema.index({ smeId: 1 });
profileSchema.index({ corporateSponsorId: 1 });
profileSchema.index({ createdAt: -1 });

// Virtual for full name
profileSchema.virtual('fullName').get(function() {
  if (this.middleName) {
    return `${this.firstName} ${this.middleName} ${this.lastName}`;
  }
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for profile completeness calculation
profileSchema.virtual('calculatedCompleteness').get(function() {
  let completeness = 0;
  const totalFields = 15; // Adjust based on required fields
  
  if (this.firstName) completeness++;
  if (this.lastName) completeness++;
  if (this.idNumber) completeness++;
  if (this.dateOfBirth) completeness++;
  if (this.contactDetails?.email) completeness++;
  if (this.contactDetails?.phone) completeness++;
  if (this.contactDetails?.address?.street) completeness++;
  if (this.contactDetails?.address?.city) completeness++;
  if (this.contactDetails?.address?.province) completeness++;
  if (this.contactDetails?.address?.country) completeness++;
  if (this.professionalDetails?.occupation) completeness++;
  if (this.bankingDetails?.bankName) completeness++;
  if (this.bankingDetails?.accountNumber) completeness++;
  if (this.documents?.length > 0) completeness++;
  if (this.isVerified) completeness++;
  
  return Math.round((completeness / totalFields) * 100);
});

// Pre-save middleware to update profile completeness
profileSchema.pre('save', function(next) {
  this.profileCompleteness = this.calculatedCompleteness;
  this.updatedAt = new Date();
  next();
});

// Validation to ensure profile is associated with either SME or Corporate Sponsor, not both
profileSchema.pre('save', function(next) {
  if (this.smeId && this.corporateSponsorId) {
    const error = new Error('Profile cannot be associated with both SME and Corporate Sponsor');
    return next(error);
  }
  next();
});

// Static method to find profiles by user
profileSchema.statics.findByUser = function(userId) {
  return this.findOne({ userId });
};

// Static method to find verified profiles
profileSchema.statics.findVerified = function() {
  return this.find({ isVerified: true });
};

// Instance method to verify profile
profileSchema.methods.verify = function(verifiedBy, notes) {
  this.isVerified = true;
  this.verificationDate = new Date();
  this.verifiedBy = verifiedBy;
  if (notes) {
    this.verificationNotes = notes;
  }
  return this.save();
};

module.exports = mongoose.model('Profile', profileSchema);
