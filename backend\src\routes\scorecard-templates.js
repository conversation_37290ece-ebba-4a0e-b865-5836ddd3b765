const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const ScorecardTemplate = require('../models/scorecard-template');

// Get all templates with programme filtering support
router.get('/', async (req, res) => {
  console.log('[DEBUG] GET /api/v1/scorecard-templates - Request received');
  console.log('[DEBUG] Query params:', req.query);
  
  try {
    const {
      category,
      search,
      active,
      limit,
      sort,
      programmeId,
      templateScope,
      includeShared = 'true',
      hierarchical = 'false'
    } = req.query;
    
    // Build query
    let query = {};
    
    if (category) {
      query.category = category;
    }
    
    if (active !== undefined) {
      query.isActive = active === 'true';
    } else {
      // Default to active templates only
      query.isActive = true;
    }
    
    // Programme-specific filtering
    if (programmeId) {
      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(programmeId)) {
        return res.status(400).json({
          error: 'Invalid programme ID format',
          message: 'Programme ID must be a valid ObjectId'
        });
      }
      
      const programmeFilter = [
        { fundingProgrammes: new mongoose.Types.ObjectId(programmeId) }
      ];
      
      if (includeShared === 'true') {
        programmeFilter.push(
          { templateScope: 'shared' },
          { templateScope: 'global' }
        );
      }
      
      query.$or = programmeFilter;
    }
    
    if (templateScope) {
      query.templateScope = templateScope;
    }
    
    let dbQuery = ScorecardTemplate.find(query)
      .populate('fundingProgrammes', 'name description status')
      .populate('parentTemplateId', 'name');
    
    // Apply search if provided
    if (search) {
      query.$text = { $search: search };
      dbQuery = ScorecardTemplate.find(query)
        .populate('fundingProgrammes', 'name description status')
        .populate('parentTemplateId', 'name');
    }
    
    // Apply sorting
    if (sort === 'usage') {
      dbQuery = dbQuery.sort({ usageCount: -1, name: 1 });
    } else if (sort === 'name') {
      dbQuery = dbQuery.sort({ name: 1 });
    } else if (sort === 'created') {
      dbQuery = dbQuery.sort({ createdAt: -1 });
    } else {
      // Default sort: scope priority, then usage, then name
      dbQuery = dbQuery.sort({ templateScope: 1, usageCount: -1, name: 1 });
    }
    
    // Apply limit if provided
    if (limit) {
      dbQuery = dbQuery.limit(parseInt(limit));
    }
    
    const templates = await dbQuery;
    
    console.log('[DEBUG] Found templates:', templates.length);
    console.log('[DEBUG] Template IDs:', templates.map(t => ({ id: t._id, name: t.name })));
    
    // Return hierarchical structure if requested
    if (hierarchical === 'true') {
      const hierarchicalData = await buildHierarchicalResponse(templates, programmeId);
      return res.json(hierarchicalData);
    }
    
    res.json(templates);
  } catch (err) {
    console.error('[DEBUG] Error getting templates:', err);
    res.status(500).json({
      error: 'Server error',
      message: err.message
    });
  }
});

// NEW: Get stage and sub-stage options for dropdown
router.get('/stage-options', async (req, res) => {
  try {
    const stageOptions = {
      mainStages: [
        { value: 'ONBOARDING', label: 'Onboarding' },
        { value: 'BUSINESS_CASE_REVIEW', label: 'Business Case Review' },
        { value: 'DUE_DILIGENCE', label: 'Due Diligence' },
        { value: 'ASSESSMENT_REPORT', label: 'Assessment Report' },
        { value: 'APPLICATION_APPROVAL', label: 'Application Approval' }
      ],
      subStages: {
        'ONBOARDING': [
          { value: 'beneficiary-registration', label: 'Beneficiary Registration' },
          { value: 'pre-screening', label: 'Pre-Screening' }
        ],
        'BUSINESS_CASE_REVIEW': [
          { value: 'document-collection', label: 'Document Collection' },
          { value: 'desktop-analysis', label: 'Desktop Analysis' },
          { value: 'data-validation', label: 'Data Validation' }
        ],
        'DUE_DILIGENCE': [
          { value: 'sme-interview', label: 'SME Interview' },
          { value: 'site-visit', label: 'Site Visit' }
        ],
        'ASSESSMENT_REPORT': [
          { value: 'report-completion', label: 'Report Completion' },
          { value: 'report-quality-check', label: 'Report Quality Check' },
          { value: 'report-review', label: 'Report Review' }
        ],
        'APPLICATION_APPROVAL': [
          { value: 'committee-review', label: 'Committee Review' },
          { value: 'final-decision', label: 'Final Decision' },
          { value: 'approval-documentation', label: 'Approval Documentation' },
          { value: 'corporate-approval-1', label: 'Corporate Approval Level 1' },
          { value: 'corporate-approval-2', label: 'Corporate Approval Level 2' },
          { value: 'corporate-approval-3', label: 'Corporate Approval Level 3' },
          { value: 'corporate-approval-4', label: 'Corporate Approval Level 4' }
        ]
      }
    };
    
    res.json(stageOptions);
  } catch (error) {
    console.error('Error fetching stage options:', error);
    res.status(500).json({ message: 'Failed to fetch stage options' });
  }
});

// NEW: Get hierarchical template structure (must come before /:id route)
router.get('/hierarchy', async (req, res) => {
  try {
    const { programmeId } = req.query;
    
    const hierarchy = {
      programmes: [],
      stages: {},
      templates: {}
    };
    
    // Get programmes (mock data for now - would come from FundingProgramme model)
    hierarchy.programmes = [
      { id: 'skills-dev', name: 'Skills Development' },
      { id: 'tech-innovation', name: 'Tech Innovation' },
      { id: 'green-energy', name: 'Green Energy' }
    ];
    
    // Get templates organized by stage
    const templates = await ScorecardTemplate.find({
      isActive: true,
      ...(programmeId && {
        $or: [
          { fundingProgrammes: new mongoose.Types.ObjectId(programmeId) },
          { templateScope: { $in: ['shared', 'global'] } }
        ]
      })
    }).populate('fundingProgrammes', 'name');
    
    // Organize templates by stage and substage
    templates.forEach(template => {
      template.applicableStages.forEach(stage => {
        if (!hierarchy.stages[stage]) {
          hierarchy.stages[stage] = {};
        }
        
        template.applicableSubstages.forEach(substage => {
          if (!hierarchy.stages[stage][substage]) {
            hierarchy.stages[stage][substage] = [];
          }
          hierarchy.stages[stage][substage].push(template);
        });
      });
    });
    
    res.json(hierarchy);
  } catch (err) {
    console.error('Error getting template hierarchy:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Get template by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const template = await ScorecardTemplate.findById(id);
    
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    res.json(template);
  } catch (err) {
    console.error('Error getting template:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Create new template
router.post('/', async (req, res) => {
  console.log('[DEBUG] ========== POST /api/v1/scorecard-templates ==========');
  console.log('[DEBUG] Request headers:', req.headers);
  console.log('[DEBUG] Request body type:', typeof req.body);
  console.log('[DEBUG] Request body keys:', Object.keys(req.body));
  console.log('[DEBUG] Full request body:', JSON.stringify(req.body, null, 2));
  
  // Enhanced field-by-field debugging
  console.log('[DEBUG] Field analysis:');
  console.log('[DEBUG] - name:', req.body.name, '(type:', typeof req.body.name, ')');
  console.log('[DEBUG] - category:', req.body.category, '(type:', typeof req.body.category, ')');
  console.log('[DEBUG] - applicableStages:', req.body.applicableStages, '(type:', typeof req.body.applicableStages, ', isArray:', Array.isArray(req.body.applicableStages), ')');
  console.log('[DEBUG] - applicableSubstages:', req.body.applicableSubstages, '(type:', typeof req.body.applicableSubstages, ', isArray:', Array.isArray(req.body.applicableSubstages), ')');
  console.log('[DEBUG] - mainStage:', req.body.mainStage, '(type:', typeof req.body.mainStage, ')');
  console.log('[DEBUG] - subStage:', req.body.subStage, '(type:', typeof req.body.subStage, ')');
  console.log('[DEBUG] - criteria:', req.body.criteria?.length || 0, 'items');
  console.log('[DEBUG] - templateScope:', req.body.templateScope);
  console.log('[DEBUG] - fundingProgrammes:', req.body.fundingProgrammes);
  
  // Check for any undefined or null values
  const requiredFields = ['name', 'category'];
  const missingFields = requiredFields.filter(field => !req.body[field]);
  if (missingFields.length > 0) {
    console.log('[DEBUG] Missing required fields:', missingFields);
  }
  
  try {
  const templateData = req.body;
  
  // Remove empty id field for new templates
  if (templateData.id === '' || templateData.id === null || templateData.id === undefined) {
    delete templateData.id;
    console.log('[DEBUG] Removed empty/null id field');
  }
  
  // Validate required fields
  if (!templateData.name || !templateData.category) {
    console.log('[DEBUG] Validation failed: missing name or category');
    console.log('[DEBUG] - name value:', templateData.name);
    console.log('[DEBUG] - category value:', templateData.category);
    return res.status(400).json({
      error: 'Validation error',
      message: 'Name and category are required',
      details: {
        name: templateData.name ? 'provided' : 'missing',
        category: templateData.category ? 'provided' : 'missing',
        receivedData: {
          name: templateData.name,
          category: templateData.category
        }
      }
    });
  }
  
  // Validate fundingProgrammes are valid ObjectIds
  if (templateData.fundingProgrammes && Array.isArray(templateData.fundingProgrammes)) {
    const invalidIds = templateData.fundingProgrammes.filter(id => {
      return id && !mongoose.Types.ObjectId.isValid(id);
    });
    
    if (invalidIds.length > 0) {
      console.log('[DEBUG] Invalid funding programme IDs:', invalidIds);
      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid funding programme IDs',
        details: {
          invalidIds: invalidIds,
          hint: 'Funding programme IDs must be valid 24-character MongoDB ObjectIds'
        }
      });
    }
    
    // Convert string IDs to ObjectIds
    templateData.fundingProgrammes = templateData.fundingProgrammes
      .filter(id => id) // Remove empty values
      .map(id => new mongoose.Types.ObjectId(id));
  }
    
    // Log the data that will be saved
    console.log('[DEBUG] Creating template with data:', {
      name: templateData.name,
      category: templateData.category,
      applicableStages: templateData.applicableStages,
      applicableSubstages: templateData.applicableSubstages,
      criteriaCount: templateData.criteria?.length
    });
    
    // Create new template
    const template = new ScorecardTemplate(templateData);
    console.log('[DEBUG] Created template instance');
    console.log('[DEBUG] Template object keys:', Object.keys(template.toObject()));
    
    // Validate criteria before saving
    const validation = template.validateCriteria();
    console.log('[DEBUG] Criteria validation result:', validation);
    
    if (!validation.isValid) {
      console.log('[DEBUG] Template validation failed:', validation.errors);
      return res.status(400).json({
        error: 'Validation error',
        message: 'Template validation failed',
        details: validation.errors
      });
    }
    
    console.log('[DEBUG] Attempting to save template...');
    const savedTemplate = await template.save();
    console.log('[DEBUG] Template saved successfully:', savedTemplate._id);
    console.log('[DEBUG] Saved template summary:', {
      id: savedTemplate._id,
      name: savedTemplate.name,
      category: savedTemplate.category,
      applicableStages: savedTemplate.applicableStages,
      applicableSubstages: savedTemplate.applicableSubstages
    });
    
    res.status(201).json(savedTemplate);
  } catch (err) {
    console.error('[DEBUG] ========== ERROR creating template ==========');
    console.error('[DEBUG] Error name:', err.name);
    console.error('[DEBUG] Error message:', err.message);
    console.error('[DEBUG] Error stack:', err.stack);
    
    if (err.name === 'ValidationError') {
      console.error('[DEBUG] Mongoose validation error details:', err.errors);
      
      // Extract specific field errors
      const fieldErrors = {};
      if (err.errors) {
        Object.keys(err.errors).forEach(field => {
          fieldErrors[field] = {
            message: err.errors[field].message,
            value: err.errors[field].value,
            kind: err.errors[field].kind
          };
          console.error(`[DEBUG] Field '${field}' error:`, fieldErrors[field]);
        });
      }
      
      return res.status(400).json({
        error: 'Validation error',
        message: err.message,
        fieldErrors: fieldErrors,
        hint: 'Check the fieldErrors object for specific validation issues'
      });
    }
    
    // Check for specific MongoDB errors
    if (err.name === 'MongoError' || err.name === 'MongoServerError') {
      console.error('[DEBUG] MongoDB error code:', err.code);
      console.error('[DEBUG] MongoDB error codeName:', err.codeName);
      
      return res.status(400).json({
        error: 'Database error',
        message: err.message,
        code: err.code,
        codeName: err.codeName
      });
    }
    
    res.status(500).json({
      error: 'Server error',
      message: err.message
    });
  }
});

// Update template
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove fields that shouldn't be updated directly
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.usageCount;
    
    // Set updatedBy if provided in headers or body
    if (req.headers['x-user-id']) {
      updateData.updatedBy = req.headers['x-user-id'];
    }
    
    const template = await ScorecardTemplate.findById(id);
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Update template fields
    Object.assign(template, updateData);
    
    // Validate criteria before saving
    const validation = template.validateCriteria();
    if (!validation.isValid) {
      return res.status(400).json({ 
        error: 'Validation error', 
        message: 'Template validation failed',
        details: validation.errors 
      });
    }
    
    const updatedTemplate = await template.save();
    res.json(updatedTemplate);
  } catch (err) {
    console.error('Error updating template:', err);
    
    if (err.name === 'ValidationError') {
      return res.status(400).json({ 
        error: 'Validation error', 
        message: err.message 
      });
    }
    
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Delete template (soft delete by setting isActive to false)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { permanent } = req.query;
    
    if (permanent === 'true') {
      // Permanent deletion
      const deletedTemplate = await ScorecardTemplate.findByIdAndDelete(id);
      if (!deletedTemplate) {
        return res.status(404).json({ error: 'Template not found' });
      }
      res.json({ message: 'Template permanently deleted' });
    } else {
      // Soft delete
      const template = await ScorecardTemplate.findByIdAndUpdate(
        id,
        { isActive: false, updatedBy: req.headers['x-user-id'] || 'system' },
        { new: true }
      );
      
      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }
      
      res.json({ message: 'Template deactivated', template });
    }
  } catch (err) {
    console.error('Error deleting template:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Get templates by category
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const templates = await ScorecardTemplate.findByCategory(category);
    res.json(templates);
  } catch (err) {
    console.error('Error getting templates by category:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Search templates
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const templates = await ScorecardTemplate.searchTemplates(query);
    res.json(templates);
  } catch (err) {
    console.error('Error searching templates:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Get most used templates
router.get('/stats/most-used', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const templates = await ScorecardTemplate.getMostUsed(parseInt(limit));
    res.json(templates);
  } catch (err) {
    console.error('Error getting most used templates:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Get template usage statistics
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;
    const stats = await ScorecardTemplate.getUsageStats(id);
    
    if (!stats) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // In a real implementation, you would aggregate actual usage data
    // For now, return basic template statistics
    const response = {
      templateId: stats._id,
      templateName: stats.name,
      category: stats.category,
      totalUsage: stats.usageCount || 0,
      lastUsed: stats.lastUsed,
      // Mock additional statistics
      averageScore: 75.5, // This would come from actual scorecard data
      usageByStage: {
        'pre-screening': 15,
        'financial-analysis': 8,
        'interview': 12
      },
      usageByMonth: {
        '2024-01': 5,
        '2024-02': 8,
        '2024-03': 12
      }
    };
    
    res.json(response);
  } catch (err) {
    console.error('Error getting template stats:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Validate template
router.post('/:id/validate', async (req, res) => {
  try {
    const { id } = req.params;
    const template = await ScorecardTemplate.findById(id);
    
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    const validation = template.validateCriteria();
    res.json(validation);
  } catch (err) {
    console.error('Error validating template:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Duplicate template
router.post('/:id/duplicate', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    
    const originalTemplate = await ScorecardTemplate.findById(id);
    if (!originalTemplate) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Create duplicate
    const duplicateData = originalTemplate.toObject();
    delete duplicateData._id;
    delete duplicateData.createdAt;
    delete duplicateData.updatedAt;
    
    duplicateData.name = name || `${originalTemplate.name} (Copy)`;
    duplicateData.usageCount = 0;
    duplicateData.lastUsed = undefined;
    duplicateData.createdBy = req.headers['x-user-id'] || 'system';
    
    const duplicateTemplate = new ScorecardTemplate(duplicateData);
    const savedDuplicate = await duplicateTemplate.save();
    
    res.status(201).json(savedDuplicate);
  } catch (err) {
    console.error('Error duplicating template:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// Increment template usage
router.post('/:id/use', async (req, res) => {
  try {
    const { id } = req.params;
    const template = await ScorecardTemplate.findById(id);
    
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    await template.incrementUsage();
    res.json({ message: 'Usage count incremented', usageCount: template.usageCount });
  } catch (err) {
    console.error('Error incrementing template usage:', err);
    res.status(500).json({ 
      error: 'Server error', 
      message: err.message 
    });
  }
});

// NEW: Assign template to programmes
router.post('/:id/assign-programmes', async (req, res) => {
  try {
    const { id } = req.params;
    const { programmeIds, customizations } = req.body;
    
    const template = await ScorecardTemplate.findById(id);
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Update programme assignments
    template.fundingProgrammes = [...new Set([...template.fundingProgrammes, ...programmeIds])];
    
    // Update customizations
    if (customizations) {
      customizations.forEach(customization => {
        const existingIndex = template.programmeCustomizations
          .findIndex(c => c.programmeId.toString() === customization.programmeId);
        
        if (existingIndex >= 0) {
          template.programmeCustomizations[existingIndex] = customization;
        } else {
          template.programmeCustomizations.push(customization);
        }
      });
    }
    
    // Update template scope
    if (template.fundingProgrammes.length > 1) {
      template.templateScope = 'shared';
    } else if (template.fundingProgrammes.length === 1) {
      template.templateScope = 'programme-specific';
    }
    
    await template.save();
    
    // Create assignment records
    await createAssignmentRecords(id, programmeIds, req.headers['x-user-id'] || 'system');
    
    res.json(template);
  } catch (err) {
    console.error('Error assigning template to programmes:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Remove template from programme
router.delete('/:id/programmes/:programmeId', async (req, res) => {
  try {
    const { id, programmeId } = req.params;
    
    const template = await ScorecardTemplate.findById(id);
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Remove programme from assignments
    template.fundingProgrammes = template.fundingProgrammes.filter(
      pid => pid.toString() !== programmeId
    );
    
    // Remove customizations for this programme
    template.programmeCustomizations = template.programmeCustomizations.filter(
      c => c.programmeId.toString() !== programmeId
    );
    
    // Update scope
    if (template.fundingProgrammes.length === 0) {
      template.templateScope = 'global';
    } else if (template.fundingProgrammes.length === 1) {
      template.templateScope = 'programme-specific';
    }
    
    await template.save();
    
    res.json({ message: 'Template removed from programme', template });
  } catch (err) {
    console.error('Error removing template from programme:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Get templates for specific programme
router.get('/programmes/:programmeId/templates', async (req, res) => {
  try {
    const { programmeId } = req.params;
    const { includeShared = 'true', stage, substage } = req.query;
    
    let query = {
      isActive: true,
      $or: [
        { fundingProgrammes: new mongoose.Types.ObjectId(programmeId) }
      ]
    };
    
    if (includeShared === 'true') {
      query.$or.push(
        { templateScope: 'shared' },
        { templateScope: 'global' }
      );
    }
    
    if (stage) {
      query.applicableStages = stage;
    }
    
    if (substage) {
      query.applicableSubstages = substage;
    }
    
    const templates = await ScorecardTemplate.find(query)
      .populate('fundingProgrammes', 'name description')
      .sort({ templateScope: 1, usageCount: -1, name: 1 });
    
    res.json(templates);
  } catch (err) {
    console.error('Error getting programme templates:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Get programme-specific template analytics
router.get('/analytics/programme/:programmeId', async (req, res) => {
  try {
    const { programmeId } = req.params;
    
    const analytics = await ScorecardTemplate.aggregate([
      {
        $match: {
          $or: [
            { fundingProgrammes: new mongoose.Types.ObjectId(programmeId) },
            { templateScope: { $in: ['shared', 'global'] } }
          ],
          isActive: true
        }
      },
      {
        $lookup: {
          from: 'funding_programmes',
          localField: 'fundingProgrammes',
          foreignField: '_id',
          as: 'programmes'
        }
      },
      {
        $project: {
          name: 1,
          category: 1,
          templateScope: 1,
          totalUsage: { $sum: '$programmeUsageStats.usageCount' },
          programmeUsage: {
            $filter: {
              input: '$programmeUsageStats',
              cond: { $eq: ['$$this.programmeId', new mongoose.Types.ObjectId(programmeId)] }
            }
          }
        }
      }
    ]);
    
    res.json(analytics);
  } catch (err) {
    console.error('Error getting programme analytics:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// NEW: Get hierarchical template structure
router.get('/hierarchy', async (req, res) => {
  try {
    const { programmeId } = req.query;
    
    const hierarchy = {
      programmes: [],
      stages: {},
      templates: {}
    };
    
    // Get programmes (mock data for now - would come from FundingProgramme model)
    hierarchy.programmes = [
      { id: 'skills-dev', name: 'Skills Development' },
      { id: 'tech-innovation', name: 'Tech Innovation' },
      { id: 'green-energy', name: 'Green Energy' }
    ];
    
    // Get templates organized by stage
    const templates = await ScorecardTemplate.find({
      isActive: true,
      ...(programmeId && {
        $or: [
          { fundingProgrammes: new mongoose.Types.ObjectId(programmeId) },
          { templateScope: { $in: ['shared', 'global'] } }
        ]
      })
    }).populate('fundingProgrammes', 'name');
    
    // Organize templates by stage and substage
    templates.forEach(template => {
      template.applicableStages.forEach(stage => {
        if (!hierarchy.stages[stage]) {
          hierarchy.stages[stage] = {};
        }
        
        template.applicableSubstages.forEach(substage => {
          if (!hierarchy.stages[stage][substage]) {
            hierarchy.stages[stage][substage] = [];
          }
          hierarchy.stages[stage][substage].push(template);
        });
      });
    });
    
    res.json(hierarchy);
  } catch (err) {
    console.error('Error getting template hierarchy:', err);
    res.status(500).json({ error: 'Server error', message: err.message });
  }
});

// Helper function to build hierarchical response
async function buildHierarchicalResponse(templates, programmeId) {
  const hierarchy = {
    global: [],
    shared: [],
    programmeSpecific: [],
    byStage: {}
  };
  
  templates.forEach(template => {
    // Categorize by scope
    switch (template.templateScope) {
      case 'global':
        hierarchy.global.push(template);
        break;
      case 'shared':
        hierarchy.shared.push(template);
        break;
      case 'programme-specific':
        hierarchy.programmeSpecific.push(template);
        break;
    }
    
    // Organize by stage
    template.applicableStages.forEach(stage => {
      if (!hierarchy.byStage[stage]) {
        hierarchy.byStage[stage] = {};
      }
      
      template.applicableSubstages.forEach(substage => {
        if (!hierarchy.byStage[stage][substage]) {
          hierarchy.byStage[stage][substage] = [];
        }
        hierarchy.byStage[stage][substage].push(template);
      });
    });
  });
  
  return hierarchy;
}

// Helper function to create assignment records
async function createAssignmentRecords(templateId, programmeIds, userId) {
  const db = mongoose.connection.db;
  
  const assignments = programmeIds.map(programmeId => {
    const assignment = {
      templateId: new mongoose.Types.ObjectId(templateId),
      programmeId: new mongoose.Types.ObjectId(programmeId),
      assignedAt: new Date(),
      isActive: true,
      customizations: {}
    };
    
    // Only add assignedBy if userId is provided and valid
    if (userId && userId !== 'system') {
      try {
        assignment.assignedBy = new mongoose.Types.ObjectId(userId);
      } catch (e) {
        // If userId is not a valid ObjectId, skip it
        console.log('Invalid userId for assignment record, skipping assignedBy field');
      }
    }
    
    return assignment;
  });
  
  try {
    await db.collection('template_programme_assignments').insertMany(assignments, { ordered: false });
  } catch (error) {
    // Ignore duplicate key errors (assignments already exist)
    if (error.code !== 11000) {
      console.error('Error creating assignment records:', error);
      // Don't throw error to prevent breaking the main functionality
    }
  }
}

module.exports = router;
