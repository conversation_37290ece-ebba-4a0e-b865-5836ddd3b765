const mongoose = require('mongoose');
const { Schema } = mongoose;

// Schema for scorecard criteria
const scorecardCriteriaSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  description: { type: String },
  weight: { type: Number, required: true, min: 1, max: 100 },
  maxScore: { type: Number, required: true, min: 1, max: 10 },
  score: { type: Number, required: true, min: 0, max: 10 },
  comments: { type: String }
});

// Main scorecard schema with auto-save support
const scorecardSchema = new Schema({
  applicationId: { type: String, required: true },
  stageId: { type: String, required: true },
  substageId: { type: String, required: true },
  name: { type: String, required: true },
  description: { type: String },
  criteria: [scorecardCriteriaSchema],
  totalScore: { type: Number },
  maxPossibleScore: { type: Number },
  status: { 
    type: String, 
    enum: ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'APPROVED', 'REJECTED'],
    default: 'NOT_STARTED'
  },
  
  // New fields for auto-save and versioning
  isDraft: { type: Boolean, default: true },
  currentVersion: { type: Number, default: 1 },
  lastModifiedBy: { type: String },
  lastModifiedAt: { type: Date, default: Date.now },
  lockedBy: { type: String, default: null },
  lockedAt: { type: Date, default: null },
  finalizedAt: { type: Date, default: null },
  finalizedBy: { type: String, default: null },
  
  // Original fields
  completedBy: { type: String },
  completedAt: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for performance
scorecardSchema.index({ applicationId: 1, stageId: 1, substageId: 1 });
scorecardSchema.index({ applicationId: 1, isDraft: 1 });
scorecardSchema.index({ lockedBy: 1, lockedAt: 1 });

// Pre-save middleware to update timestamps
scorecardSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  this.lastModifiedAt = new Date();
  next();
});

// Instance methods
scorecardSchema.methods.lock = function(userId) {
  this.lockedBy = userId;
  this.lockedAt = new Date();
  return this.save();
};

scorecardSchema.methods.unlock = function() {
  this.lockedBy = null;
  this.lockedAt = null;
  return this.save();
};

scorecardSchema.methods.finalize = function(userId) {
  this.isDraft = false;
  this.status = 'COMPLETED';
  this.finalizedAt = new Date();
  this.finalizedBy = userId;
  this.completedAt = new Date();
  this.completedBy = userId;
  return this.save();
};

// Static methods
scorecardSchema.statics.findDrafts = function(applicationId) {
  return this.find({ applicationId, isDraft: true });
};

scorecardSchema.statics.findByApplicationAndStage = function(applicationId, stageId, substageId) {
  return this.findOne({ applicationId, stageId, substageId });
};

// Check if model already exists before creating
let Scorecard;
try {
  Scorecard = mongoose.model('Scorecard');
} catch (e) {
  Scorecard = mongoose.model('Scorecard', scorecardSchema);
}

module.exports = Scorecard;