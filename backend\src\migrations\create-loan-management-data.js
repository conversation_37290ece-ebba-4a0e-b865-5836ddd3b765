const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

const LoanProduct = require('../models/loan-product');
const PricingRule = require('../models/pricing-rule');

async function createLoanManagementData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-screening-app', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('Connected to MongoDB');

    // Check if loan products already exist
    const existingProducts = await LoanProduct.countDocuments();
    if (existingProducts > 0) {
      console.log('Loan products already exist. Skipping seed data.');
      return;
    }

    // Create loan products
    const loanProducts = [
      {
        productId: 'PROD-0001',
        productName: 'Small Business Term Loan',
        productType: 'TERM_LOAN',
        description: 'Traditional term loan for small businesses with fixed monthly payments',
        minAmount: 50000,
        maxAmount: 1000000,
        minTerm: 6,
        maxTerm: 60,
        termUnit: 'MONTHS',
        baseInterestRate: 12,
        features: [
          'Fixed interest rate',
          'Flexible repayment terms',
          'No prepayment penalties',
          'Quick approval process'
        ],
        eligibilityCriteria: {
          minCreditScore: 600,
          minRevenue: 500000,
          minTimeInBusiness: 12,
          requiredDocuments: [
            'Business registration',
            'Financial statements',
            'Bank statements',
            'Tax returns'
          ],
          industryRestrictions: [],
          geographicRestrictions: []
        },
        fees: {
          originationFee: 2,
          processingFee: 500,
          lateFee: 5,
          prepaymentPenalty: 0
        },
        collateralRequired: false,
        active: true
      },
      {
        productId: 'PROD-0002',
        productName: 'Equipment Financing',
        productType: 'EQUIPMENT_FINANCING',
        description: 'Specialized financing for business equipment purchases',
        minAmount: 25000,
        maxAmount: 500000,
        minTerm: 12,
        maxTerm: 84,
        termUnit: 'MONTHS',
        baseInterestRate: 10,
        features: [
          'Equipment serves as collateral',
          'Up to 100% financing',
          'Tax benefits',
          'Preserve working capital'
        ],
        eligibilityCriteria: {
          minCreditScore: 650,
          minRevenue: 300000,
          minTimeInBusiness: 24,
          requiredDocuments: [
            'Equipment quote',
            'Business registration',
            'Financial statements',
            'Equipment specifications'
          ],
          industryRestrictions: [],
          geographicRestrictions: []
        },
        fees: {
          originationFee: 1.5,
          processingFee: 750,
          lateFee: 5,
          prepaymentPenalty: 2
        },
        collateralRequired: true,
        collateralTypes: ['Equipment'],
        active: true
      },
      {
        productId: 'PROD-0003',
        productName: 'Working Capital Line of Credit',
        productType: 'LINE_OF_CREDIT',
        description: 'Flexible credit line for managing cash flow and working capital needs',
        minAmount: 10000,
        maxAmount: 250000,
        minTerm: 12,
        maxTerm: 36,
        termUnit: 'MONTHS',
        baseInterestRate: 15,
        features: [
          'Draw funds as needed',
          'Pay interest only on used amount',
          'Revolving credit',
          'Online access'
        ],
        eligibilityCriteria: {
          minCreditScore: 680,
          minRevenue: 750000,
          minTimeInBusiness: 36,
          requiredDocuments: [
            'Business registration',
            'Financial statements',
            'Bank statements',
            'Accounts receivable aging'
          ],
          industryRestrictions: [],
          geographicRestrictions: []
        },
        fees: {
          originationFee: 2.5,
          processingFee: 1000,
          lateFee: 5,
          prepaymentPenalty: 0
        },
        collateralRequired: false,
        active: true
      },
      {
        productId: 'PROD-0004',
        productName: 'Invoice Financing',
        productType: 'INVOICE_FINANCING',
        description: 'Get immediate cash by financing your outstanding invoices',
        minAmount: 5000,
        maxAmount: 100000,
        minTerm: 1,
        maxTerm: 3,
        termUnit: 'MONTHS',
        baseInterestRate: 18,
        features: [
          'Fast access to cash',
          'No long-term commitment',
          'Improve cash flow',
          'Flexible terms'
        ],
        eligibilityCriteria: {
          minCreditScore: 600,
          minRevenue: 1000000,
          minTimeInBusiness: 12,
          requiredDocuments: [
            'Outstanding invoices',
            'Customer contracts',
            'Business registration',
            'Bank statements'
          ],
          industryRestrictions: [],
          geographicRestrictions: []
        },
        fees: {
          originationFee: 3,
          processingFee: 250,
          lateFee: 10,
          prepaymentPenalty: 0
        },
        collateralRequired: true,
        collateralTypes: ['Invoices', 'Accounts Receivable'],
        active: true
      }
    ];

    // Insert loan products
    const createdProducts = await LoanProduct.insertMany(loanProducts);
    console.log(`Created ${createdProducts.length} loan products`);

    // Create pricing rules for each product
    const pricingRules = [
      {
        ruleId: 'RULE-0001',
        ruleName: 'Small Business Term Loan - Standard Pricing',
        productId: 'PROD-0001',
        riskFactors: [
          {
            factor: 'CREDIT_SCORE',
            weight: 0.3,
            ranges: [
              { min: 300, max: 599, adjustment: 5 },
              { min: 600, max: 699, adjustment: 2 },
              { min: 700, max: 799, adjustment: 0 },
              { min: 800, max: 850, adjustment: -2 }
            ]
          },
          {
            factor: 'TIME_IN_BUSINESS',
            weight: 0.2,
            ranges: [
              { min: 0, max: 2, adjustment: 3 },
              { min: 2, max: 5, adjustment: 1 },
              { min: 5, max: 10, adjustment: 0 },
              { min: 10, max: 100, adjustment: -1 }
            ]
          },
          {
            factor: 'DEBT_TO_INCOME',
            weight: 0.25,
            ranges: [
              { min: 0, max: 0.3, adjustment: -1 },
              { min: 0.3, max: 0.5, adjustment: 0 },
              { min: 0.5, max: 0.7, adjustment: 2 },
              { min: 0.7, max: 1, adjustment: 4 }
            ]
          },
          {
            factor: 'REVENUE_STABILITY',
            weight: 0.25,
            ranges: [
              { min: 0, max: 0.5, adjustment: 3 },
              { min: 0.5, max: 0.7, adjustment: 1 },
              { min: 0.7, max: 0.9, adjustment: 0 },
              { min: 0.9, max: 1, adjustment: -1 }
            ]
          }
        ],
        baseRate: 12,
        marginRange: { min: -2, max: 8 },
        fees: {
          originationFeePercent: 2,
          processingFeeFixed: 500,
          lateFeePercent: 5,
          prepaymentPenaltyPercent: 0
        },
        termAdjustments: [
          { minTerm: 6, maxTerm: 12, adjustment: 1 },
          { minTerm: 13, maxTerm: 24, adjustment: 0 },
          { minTerm: 25, maxTerm: 36, adjustment: -0.5 },
          { minTerm: 37, maxTerm: 60, adjustment: -1 }
        ],
        amountAdjustments: [
          { minAmount: 50000, maxAmount: 100000, adjustment: 1 },
          { minAmount: 100001, maxAmount: 250000, adjustment: 0.5 },
          { minAmount: 250001, maxAmount: 500000, adjustment: 0 },
          { minAmount: 500001, maxAmount: 1000000, adjustment: -0.5 }
        ],
        active: true
      },
      {
        ruleId: 'RULE-0002',
        ruleName: 'Equipment Financing - Standard Pricing',
        productId: 'PROD-0002',
        riskFactors: [
          {
            factor: 'CREDIT_SCORE',
            weight: 0.25,
            ranges: [
              { min: 300, max: 649, adjustment: 4 },
              { min: 650, max: 749, adjustment: 2 },
              { min: 750, max: 850, adjustment: 0 }
            ]
          },
          {
            factor: 'COLLATERAL_COVERAGE',
            weight: 0.35,
            ranges: [
              { min: 0, max: 0.8, adjustment: 3 },
              { min: 0.8, max: 1.2, adjustment: 0 },
              { min: 1.2, max: 2, adjustment: -2 }
            ]
          },
          {
            factor: 'TIME_IN_BUSINESS',
            weight: 0.2,
            ranges: [
              { min: 0, max: 3, adjustment: 2 },
              { min: 3, max: 5, adjustment: 1 },
              { min: 5, max: 100, adjustment: 0 }
            ]
          },
          {
            factor: 'CASH_FLOW_RATIO',
            weight: 0.2,
            ranges: [
              { min: 0, max: 1, adjustment: 4 },
              { min: 1, max: 1.5, adjustment: 1 },
              { min: 1.5, max: 10, adjustment: 0 }
            ]
          }
        ],
        baseRate: 10,
        marginRange: { min: -1, max: 6 },
        fees: {
          originationFeePercent: 1.5,
          processingFeeFixed: 750,
          lateFeePercent: 5,
          prepaymentPenaltyPercent: 2
        },
        termAdjustments: [
          { minTerm: 12, maxTerm: 36, adjustment: 0 },
          { minTerm: 37, maxTerm: 60, adjustment: -0.5 },
          { minTerm: 61, maxTerm: 84, adjustment: -1 }
        ],
        amountAdjustments: [
          { minAmount: 25000, maxAmount: 100000, adjustment: 0.5 },
          { minAmount: 100001, maxAmount: 500000, adjustment: 0 }
        ],
        active: true
      }
    ];

    // Insert pricing rules
    const createdRules = await PricingRule.insertMany(pricingRules);
    console.log(`Created ${createdRules.length} pricing rules`);

    console.log('Loan management data created successfully');

  } catch (error) {
    console.error('Error creating loan management data:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the migration
if (require.main === module) {
  createLoanManagementData();
}

module.exports = createLoanManagementData;