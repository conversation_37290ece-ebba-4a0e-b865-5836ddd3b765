const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/user');
const Role = require('./models/role');
const Permission = require('./models/permission');

// Default permissions
const defaultPermissions = [
  // User permissions
  { name: 'read:own_profile', description: 'Read own user profile', resource: 'user', action: 'read' },
  { name: 'update:own_profile', description: 'Update own user profile', resource: 'user', action: 'update' },
  
  // Application permissions
  { name: 'create:applications', description: 'Create applications', resource: 'application', action: 'create' },
  { name: 'read:applications', description: 'Read applications', resource: 'application', action: 'read' },
  { name: 'update:applications', description: 'Update applications', resource: 'application', action: 'update' },
  { name: 'delete:applications', description: 'Delete applications', resource: 'application', action: 'delete' },
  { name: 'manage:applications', description: 'Full application management', resource: 'application', action: 'manage' },
  
  // Entity-specific application permissions
  { name: 'read:entity_applications', description: 'Read applications for specific entity', resource: 'application', action: 'read' },
  { name: 'update:entity_applications', description: 'Update applications for specific entity', resource: 'application', action: 'update' },
  
  // User management permissions
  { name: 'create:users', description: 'Create users', resource: 'user', action: 'create' },
  { name: 'read:users', description: 'Read users', resource: 'user', action: 'read' },
  { name: 'update:users', description: 'Update users', resource: 'user', action: 'update' },
  { name: 'delete:users', description: 'Delete users', resource: 'user', action: 'delete' },
  { name: 'manage:users', description: 'Full user management', resource: 'user', action: 'manage' },
  
  // Role management permissions
  { name: 'create:roles', description: 'Create roles', resource: 'role', action: 'create' },
  { name: 'read:roles', description: 'Read roles', resource: 'role', action: 'read' },
  { name: 'update:roles', description: 'Update roles', resource: 'role', action: 'update' },
  { name: 'delete:roles', description: 'Delete roles', resource: 'role', action: 'delete' },
  { name: 'manage:roles', description: 'Full role management', resource: 'role', action: 'manage' },
  
  // Report permissions
  { name: 'read:reports', description: 'Read reports', resource: 'report', action: 'read' },
  { name: 'create:reports', description: 'Create reports', resource: 'report', action: 'create' },
  { name: 'manage:reports', description: 'Full report management', resource: 'report', action: 'manage' },
  
  // Entity-specific report permissions
  { name: 'read:entity_reports', description: 'Read reports for specific entity', resource: 'report', action: 'read' },
  
  // System permissions
  { name: 'manage:system', description: 'System administration', resource: 'system', action: 'manage' }
];

// Default roles
const defaultRoles = [
  {
    name: 'admin',
    description: 'System Administrator with full access',
    permissions: [
      'manage:system',
      'manage:users',
      'manage:roles',
      'manage:applications',
      'manage:reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'manager',
    description: 'Manager with application and user management access',
    permissions: [
      'manage:applications',
      'read:users',
      'create:users',
      'update:users',
      'read:reports',
      'create:reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'loan-officer',
    description: 'Loan Officer with application management access',
    permissions: [
      'create:applications',
      'read:applications',
      'update:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'reviewer',
    description: 'Reviewer with read and update access to applications',
    permissions: [
      'read:applications',
      'update:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'read-only-user',
    description: 'Read-only access to applications and reports',
    permissions: [
      'read:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'user',
    description: 'Basic user with profile access',
    permissions: [
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'corporate-sponsor-user',
    description: 'Corporate sponsor user with entity-specific access',
    permissions: [
      'read:entity_applications',
      'update:entity_applications',
      'read:entity_reports',
      'read:own_profile',
      'update:own_profile'
    ]
  },
  {
    name: 'programme-user',
    description: 'Programme user with entity-specific access',
    permissions: [
      'read:entity_applications',
      'update:entity_applications',
      'read:entity_reports',
      'read:own_profile',
      'update:own_profile'
    ]
  }
];

// Default sample users for all roles
const defaultUsers = [
  // Admin user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'SYSTEM_ADMINISTRATOR',
    roles: ['admin', 'user'],
    permissions: [
      'manage:system',
      'manage:users',
      'manage:roles',
      'manage:applications',
      'manage:reports',
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  },
  // Manager user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'Manager123!',
    firstName: 'Sarah',
    lastName: 'Johnson',
    role: 'MANAGER',
    roles: ['manager', 'user'],
    permissions: [
      'manage:applications',
      'read:users',
      'create:users',
      'update:users',
      'read:reports',
      'create:reports',
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  },
  // Loan Officer user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'LoanOfficer123!',
    firstName: 'Michael',
    lastName: 'Chen',
    role: 'LOAN_OFFICER',
    roles: ['loan-officer', 'user'],
    permissions: [
      'create:applications',
      'read:applications',
      'update:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  },
  // Reviewer user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'Reviewer123!',
    firstName: 'Emily',
    lastName: 'Davis',
    role: 'REVIEWER',
    roles: ['reviewer', 'user'],
    permissions: [
      'read:applications',
      'update:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  },
  // Read-only user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'ReadOnly123!',
    firstName: 'David',
    lastName: 'Wilson',
    role: 'READ_ONLY_USER',
    roles: ['read-only-user', 'user'],
    permissions: [
      'read:applications',
      'read:reports',
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  },
  // Basic user
  {
    username: '<EMAIL>',
    email: '<EMAIL>',
    password: 'User123!',
    firstName: 'Jessica',
    lastName: 'Brown',
    role: 'user',
    roles: ['user'],
    permissions: [
      'read:own_profile',
      'update:own_profile'
    ],
    organizationType: '20/20Insight',
    isActive: true,
    status: 'active'
  }
];

// Keep backward compatibility with existing admin user reference
const defaultAdminUser = defaultUsers[0];

async function seedAuthData() {
  try {
    console.log('Starting authentication data seeding...');

    // Check if permissions already exist
    const existingPermissionsCount = await Permission.countDocuments();
    if (existingPermissionsCount === 0) {
      console.log('Seeding permissions...');
      await Permission.insertMany(defaultPermissions);
      console.log(`Created ${defaultPermissions.length} permissions`);
    } else {
      console.log(`Permissions already exist (${existingPermissionsCount} found)`);
    }

    // Check if roles already exist
    const existingRolesCount = await Role.countDocuments();
    if (existingRolesCount === 0) {
      console.log('Seeding roles...');
      await Role.insertMany(defaultRoles);
      console.log(`Created ${defaultRoles.length} roles`);
    } else {
      console.log(`Roles already exist (${existingRolesCount} found)`);
    }

    // Create sample users for all roles
    console.log('Creating sample users for all roles...');
    let createdUsersCount = 0;
    let existingUsersCount = 0;

    for (const userData of defaultUsers) {
      const existingUser = await User.findOne({
        $or: [
          { username: userData.username },
          { email: userData.email }
        ]
      });

      if (!existingUser) {
        console.log(`Creating ${userData.roles[0]} user: ${userData.username}`);
        const user = new User(userData);
        await user.save();
        createdUsersCount++;
        
        // Log credentials for new users
        console.log(`  Username: ${userData.username}`);
        console.log(`  Email: ${userData.email}`);
        console.log(`  Password: ${userData.password}`);
        console.log(`  Role: ${userData.roles[0]}`);
        console.log(`  Name: ${userData.firstName} ${userData.lastName}`);
        console.log('  ---');
      } else {
        console.log(`User ${userData.username} already exists`);
        existingUsersCount++;
      }
    }

    console.log(`\nUser creation summary:`);
    console.log(`  Created: ${createdUsersCount} new users`);
    console.log(`  Existing: ${existingUsersCount} users already existed`);
    
    if (createdUsersCount > 0) {
      console.log('\n*** PLEASE CHANGE DEFAULT PASSWORDS AFTER FIRST LOGIN ***');
      console.log('\nAll sample users have been created with the following pattern:');
      console.log('  Username: [role-name]');
      console.log('  Password: [RoleName]123!');
      console.log('  Email: [role-name]@funding-screening-app.com');
    }

    console.log('Authentication data seeding completed successfully');
    return true;
  } catch (error) {
    console.error('Error seeding authentication data:', error);
    return false;
  }
}

// Export the seeding function
module.exports = { seedAuthData };

// If this script is run directly, execute the seeding
if (require.main === module) {
  const dbConfig = require('./config/database-config');
  
  async function runSeeding() {
    try {
      // Connect to database
      const dbConnection = await dbConfig.connectToDatabase();
      
      if (!dbConnection.success) {
        console.error('Failed to connect to database:', dbConnection.message);
        process.exit(1);
      }
      
      console.log('Connected to MongoDB database');
      
      // Run seeding
      const success = await seedAuthData();
      
      if (success) {
        console.log('Seeding completed successfully');
        process.exit(0);
      } else {
        console.error('Seeding failed');
        process.exit(1);
      }
    } catch (error) {
      console.error('Error running seeding script:', error);
      process.exit(1);
    }
  }
  
  runSeeding();
}
