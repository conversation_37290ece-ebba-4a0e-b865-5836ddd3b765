const mongoose = require('mongoose');

const loanProductSchema = new mongoose.Schema({
  productId: {
    type: String,
    required: true,
    unique: true
  },
  productName: {
    type: String,
    required: true
  },
  productType: {
    type: String,
    enum: ['TERM_LOAN', 'REVOLVING_CREDIT', 'LINE_OF_CREDIT', 'EQUIPMENT_FINANCING', 'INVOICE_FINANCING', 'MERCHANT_CASH_ADVANCE'],
    required: true
  },
  description: String,
  minAmount: {
    type: Number,
    required: true,
    min: 0
  },
  maxAmount: {
    type: Number,
    required: true,
    validate: {
      validator: function(value) {
        return value >= this.minAmount;
      },
      message: 'Maximum amount must be greater than or equal to minimum amount'
    }
  },
  minTerm: {
    type: Number,
    required: true,
    min: 1
  },
  maxTerm: {
    type: Number,
    required: true,
    validate: {
      validator: function(value) {
        return value >= this.minTerm;
      },
      message: 'Maximum term must be greater than or equal to minimum term'
    }
  },
  termUnit: {
    type: String,
    enum: ['DAYS', 'MONTHS', 'YEARS'],
    default: 'MONTHS'
  },
  baseInterestRate: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  features: [{
    type: String
  }],
  eligibilityCriteria: {
    minCreditScore: Number,
    minRevenue: Number,
    minTimeInBusiness: Number,
    requiredDocuments: [String],
    industryRestrictions: [String],
    geographicRestrictions: [String]
  },
  fees: {
    originationFee: {
      type: Number,
      default: 0
    },
    processingFee: {
      type: Number,
      default: 0
    },
    lateFee: {
      type: Number,
      default: 0
    },
    prepaymentPenalty: {
      type: Number,
      default: 0
    }
  },
  collateralRequired: {
    type: Boolean,
    default: false
  },
  collateralTypes: [String],
  active: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Generate product ID if not provided
loanProductSchema.pre('save', async function(next) {
  if (!this.productId) {
    const count = await this.constructor.countDocuments();
    this.productId = `PROD-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

const LoanProduct = mongoose.model('LoanProduct', loanProductSchema);

module.exports = LoanProduct;