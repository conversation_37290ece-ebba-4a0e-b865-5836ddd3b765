const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Application = require('../models/application');
const CorporateSponsor = require('../models/corporate-sponsor');
const FundingProgramme = require('../models/funding-programme');

// Constants for application stages and statuses
const STAGES = [
  'ONBOARDING',
  'PRE_SCREENING_PENDING',
  'PRE_SCREENING_COMPLETE',
  'DOCUMENT_COLLECTION',
  'DESKTOP_ANALYSIS',
  'DATA_VALIDATION',
  'SME_INTERVIEW_SCHEDULED',
  'SME_INTERVIEW_COMPLETED',
  'FINAL_REVIEW',
  'APPLICATION_APPROVAL',
  'APPROVED',
  'REJECTED',
  'WITHDRAWN',
  'ON_HOLD'
];

const SUBSTAGES = {
  'ONBOARDING': ['BENEFICIARY_REGISTRATION'],
  'PRE_SCREENING_PENDING': ['PRE_SCREENING'],
  'DOCUMENT_COLLECTION': ['DOCUMENT_COLLECTION'],
  'DESKTOP_ANALYSIS': ['DESKTOP_ANALYSIS'],
  'DATA_VALIDATION': ['DATA_VALIDATION'],
  'SME_INTERVIEW_SCHEDULED': ['SME_INTERVIEW'],
  'SME_INTERVIEW_COMPLETED': ['SITE_VISIT'],
  'FINAL_REVIEW': ['REPORT_COMPLETION', 'REPORT_QUALITY_CHECK', 'REPORT_REVIEW'],
  'APPLICATION_APPROVAL': ['COMMITTEE_REVIEW', 'FINAL_DECISION', 'APPROVAL_DOCUMENTATION', 
                          'CORPORATE_APPROVAL_1', 'CORPORATE_APPROVAL_2', 'CORPORATE_APPROVAL_3', 'CORPORATE_APPROVAL_4']
};

const STATUSES = {
  'ONBOARDING': ['pending', 'in-review'],
  'PRE_SCREENING_PENDING': ['pending', 'in-review'],
  'PRE_SCREENING_COMPLETE': ['in-review'],
  'DOCUMENT_COLLECTION': ['pending', 'in-review'],
  'DESKTOP_ANALYSIS': ['in-review'],
  'DATA_VALIDATION': ['in-review'],
  'SME_INTERVIEW_SCHEDULED': ['in-review'],
  'SME_INTERVIEW_COMPLETED': ['in-review'],
  'FINAL_REVIEW': ['in-review'],
  'APPLICATION_APPROVAL': ['in-review'],
  'APPROVED': ['approved'],
  'REJECTED': ['rejected'],
  'WITHDRAWN': ['withdrawn-by-applicant'],
  'ON_HOLD': ['on-hold']
};

const STAGE_STATUSES = {
  'pending': 'PENDING',
  'in-review': 'IN_PROGRESS',
  'approved': 'COMPLETED',
  'rejected': 'COMPLETED',
  'withdrawn-by-applicant': 'COMPLETED',
  'on-hold': 'IN_PROGRESS'
};

// Helper function to generate a random integer between min and max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Helper function to get a random item from an array
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Helper function to generate a random date within the last year
function getRandomDate(startDate, endDate) {
  const start = startDate ? new Date(startDate).getTime() : new Date(new Date().setFullYear(new Date().getFullYear() - 1)).getTime();
  const end = endDate ? new Date(endDate).getTime() : new Date().getTime();
  
  return new Date(start + Math.random() * (end - start));
}

// Helper function to generate a random South African phone number
function generateRandomPhone() {
  const prefixes = ['071', '072', '073', '074', '076', '078', '079', '082', '083', '084'];
  const prefix = getRandomItem(prefixes);
  const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
  return `+27 ${prefix.substring(1)} ${number.substring(0, 3)} ${number.substring(3)}`;
}

// Helper function to generate a random ID number
function generateRandomIdNumber() {
  return Math.floor(Math.random() * 10000000000).toString().padStart(10, '0');
}

// Helper function to generate a random address
function generateRandomAddress() {
  const streets = ['Main Street', 'Park Avenue', 'Oak Road', 'Cedar Lane', 'Maple Drive'];
  const cities = ['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth', 'Bloemfontein'];
  const provinces = ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Free State', 'Mpumalanga', 'Limpopo', 'North West', 'Northern Cape'];
  
  return {
    street: `${getRandomInt(1, 1000)} ${getRandomItem(streets)}`,
    city: getRandomItem(cities),
    province: getRandomItem(provinces),
    postalCode: getRandomInt(1000, 9999).toString(),
    country: 'South Africa'
  };
}

// Helper function to generate a random business name
function generateRandomBusinessName() {
  const prefixes = ['Global', 'African', 'South African', 'Premier', 'Elite', 'Advanced', 'Innovative', 'Dynamic', 'Strategic', 'Sustainable'];
  const industries = ['Tech', 'Solutions', 'Consulting', 'Services', 'Enterprises', 'Industries', 'Group', 'Holdings', 'Ventures', 'Partners'];
  const suffixes = ['Ltd', 'Pty Ltd', 'Inc', 'Corporation', 'Company', 'SA', 'International', 'Associates'];
  
  return `${getRandomItem(prefixes)} ${getRandomItem(industries)} ${getRandomItem(suffixes)}`;
}

// Helper function to generate a random funding amount
function generateRandomFundingAmount() {
  // Generate a random amount between R100,000 and R5,000,000
  return Math.round(getRandomInt(100000, 5000000) / 1000) * 1000;
}

// Helper function to generate a random stage history
function generateStageHistory(currentStage, submissionDate) {
  const stages = [];
  const stageIndex = STAGES.indexOf(currentStage);
  
  // Generate completed stages up to the current stage
  for (let i = 0; i <= stageIndex; i++) {
    const stage = STAGES[i];
    const isCurrentStage = i === stageIndex;
    const status = isCurrentStage ? 'IN_PROGRESS' : 'COMPLETED';
    
    // Calculate dates
    let startDate, completedDate;
    if (i === 0) {
      startDate = submissionDate;
      completedDate = isCurrentStage ? null : new Date(new Date(submissionDate).getTime() + getRandomInt(1, 5) * 24 * 60 * 60 * 1000);
    } else {
      startDate = new Date(new Date(stages[i-1].completedAt).getTime() + getRandomInt(1, 3) * 24 * 60 * 60 * 1000);
      completedDate = isCurrentStage ? null : new Date(new Date(startDate).getTime() + getRandomInt(1, 10) * 24 * 60 * 60 * 1000);
    }
    
    stages.push({
      type: stage,
      status: status,
      startedAt: startDate.toISOString(),
      completedAt: completedDate ? completedDate.toISOString() : null,
      notes: {
        strengths: ['Strong business concept', 'Clear market opportunity'],
        weaknesses: ['Limited track record', 'Funding amount concerns'],
        opportunities: ['Market expansion potential', 'Job creation'],
        threats: ['Competitive market', 'Regulatory challenges'],
        generalNotes: [`Stage ${stage} ${status === 'COMPLETED' ? 'completed' : 'in progress'}`]
      },
      requiredScore: getRandomInt(60, 85),
      score: status === 'COMPLETED' ? getRandomInt(60, 95) : null,
      assignedTo: getRandomItem(['Sarah Johnson', 'Michael Chen', 'David Moyo', 'Priya Patel', 'Thabo Molefe'])
    });
  }
  
  return stages;
}

// Generate a demo application
async function generateDemoApplication(options = {}) {
  try {
    // Get or create corporate sponsor
    let corporateSponsor;
    if (options.corporateSponsorId) {
      corporateSponsor = await CorporateSponsor.findById(options.corporateSponsorId);
      if (!corporateSponsor && options.createNewSponsor) {
        // Create a new corporate sponsor
        corporateSponsor = await CorporateSponsor.create({
          name: options.corporateSponsorName || 'New Corporate Sponsor',
          description: 'Auto-generated corporate sponsor',
          industry: getRandomItem(['Technology', 'Finance', 'Healthcare', 'Education', 'Manufacturing']),
          status: 'active',
          contactPerson: {
            name: 'Contact Person',
            email: '<EMAIL>',
            phone: generateRandomPhone()
          }
        });
      }
    } else {
      // Get a random corporate sponsor or create a new one if none exist
      const sponsorCount = await CorporateSponsor.countDocuments();
      if (sponsorCount > 0) {
        const randomSkip = Math.floor(Math.random() * sponsorCount);
        corporateSponsor = await CorporateSponsor.findOne().skip(randomSkip);
      } else if (options.createNewSponsor) {
        // Create a new corporate sponsor
        corporateSponsor = await CorporateSponsor.create({
          name: options.corporateSponsorName || 'Default Corporate Sponsor',
          description: 'Auto-generated corporate sponsor',
          industry: getRandomItem(['Technology', 'Finance', 'Healthcare', 'Education', 'Manufacturing']),
          status: 'active',
          contactPerson: {
            name: 'Contact Person',
            email: '<EMAIL>',
            phone: generateRandomPhone()
          }
        });
      }
    }

    // Get or create funding programme
    let fundingProgramme;
    if (options.fundingProgrammeId) {
      fundingProgramme = await FundingProgramme.findById(options.fundingProgrammeId);
      if (!fundingProgramme && options.createNewProgramme) {
        // Create a new funding programme
        fundingProgramme = await FundingProgramme.create({
          name: options.fundingProgrammeName || 'New Funding Programme',
          description: 'Auto-generated funding programme',
          corporateSponsorId: mongoose.Types.ObjectId(corporateSponsor._id),
          fundingTypes: ['Grant'],
          status: 'active',
          budget: {
            totalAmount: 10000000,
            allocated: 0,
            remaining: 10000000
          }
        });
      }
    } else {
      // Get a random funding programme or create a new one if none exist
      const programmeCount = await FundingProgramme.countDocuments();
      if (programmeCount > 0) {
        const randomSkip = Math.floor(Math.random() * programmeCount);
        fundingProgramme = await FundingProgramme.findOne().skip(randomSkip);
      } else if (options.createNewProgramme) {
        // Create a new funding programme
        fundingProgramme = await FundingProgramme.create({
          name: options.fundingProgrammeName || 'Default Funding Programme',
          description: 'Auto-generated funding programme',
          corporateSponsorId: mongoose.Types.ObjectId(corporateSponsor._id),
          fundingTypes: ['Grant'],
          status: 'active',
          budget: {
            totalAmount: 10000000,
            allocated: 0,
            remaining: 10000000
          }
        });
      }
    }

    // Determine stage and status
    const stage = options.stage || getRandomItem(STAGES);
    const substage = options.substage || (SUBSTAGES[stage] ? getRandomItem(SUBSTAGES[stage]) : null);
    const status = options.status || (STATUSES[stage] ? getRandomItem(STATUSES[stage]) : 'pending');
    
    // Generate random data for the application
    const businessName = options.businessName || generateRandomBusinessName();
    const firstName = options.firstName || `First${getRandomInt(1, 100)}`;
    const lastName = options.lastName || `Last${getRandomInt(1, 100)}`;
    const submissionDate = options.submissionDate || getRandomDate();
    const fundingAmount = options.fundingAmount || generateRandomFundingAmount();
    
    // Generate stage history
    const stages = generateStageHistory(stage, submissionDate);
    
    // Create the application
    const application = new Application({
      id: `APP-${new Date().getFullYear()}-${getRandomInt(100, 999)}`,
      registrationNumber: `REG${getRandomInt(100000, 999999)}`,
      fundingAmount: fundingAmount,
      submissionDate: submissionDate,
      currentStage: stage,
      substage: substage,
      status: status,
      owner: '',
      personalInfo: {
        firstName: firstName,
        lastName: lastName,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
        phone: generateRandomPhone(),
        idNumber: generateRandomIdNumber(),
        address: generateRandomAddress()
      },
      businessInfo: {
        legalName: businessName,
        tradingName: `Trading as ${businessName}`,
        registrationNumber: `REG${getRandomInt(100000, 999999)}`,
        cipcRegistrationNumber: `CIPC-${getRandomInt(100000, 999999)}`,
        entityType: 'Pty Ltd',
        startTradingDate: getRandomDate(new Date(new Date().setFullYear(new Date().getFullYear() - 5))),
        vatNumber: `VAT${getRandomInt(100000, 999999)}`,
        industry: getRandomItem(['Technology', 'Finance', 'Healthcare', 'Education', 'Manufacturing', 'Retail', 'Agriculture', 'Construction']),
        businessType: getRandomItem(['Private Company', 'Public Company', 'Sole Proprietorship', 'Partnership', 'Non-Profit Organization']),
        yearEstablished: getRandomInt(2010, new Date().getFullYear() - 1),
        employeeCount: getRandomInt(1, 50),
        address: generateRandomAddress(),
        website: `https://www.${businessName.toLowerCase().replace(/\s+/g, '')}.co.za`
      },
      financialInfo: {
        annualTurnover: fundingAmount * getRandomInt(2, 5),
        netProfit: fundingAmount * getRandomInt(10, 20) / 100,
        currentAssets: fundingAmount * getRandomInt(15, 25) / 10,
        currentLiabilities: fundingAmount * getRandomInt(5, 15) / 10,
        totalDebt: fundingAmount * getRandomInt(5, 10) / 10,
        fundingPurpose: `Expansion and growth for ${businessName}`,
        fundingAmount: fundingAmount
      },
      smeInterview: {
        interviewDate: stage === 'SME_INTERVIEW_SCHEDULED' || stage === 'SME_INTERVIEW_COMPLETED' ? 
          getRandomDate(new Date(new Date(submissionDate).getTime() + 7 * 24 * 60 * 60 * 1000)) : null,
        interviewer: getRandomItem(['Sarah Johnson', 'Michael Chen', 'David Moyo', 'Priya Patel', 'Thabo Molefe']),
        participants: stage === 'SME_INTERVIEW_SCHEDULED' || stage === 'SME_INTERVIEW_COMPLETED' ? 
          [`${businessName} CEO`, 'Financial Manager', 'Operations Director'] : [],
        notes: stage === 'SME_INTERVIEW_COMPLETED' ? 
          'Initial interview to assess business operations and management team' : '',
        recommendations: stage === 'SME_INTERVIEW_COMPLETED' ? 
          ['Proceed with application', 'Verify financial projections', 'Schedule site visit'] : [],
        followUpActions: stage === 'SME_INTERVIEW_COMPLETED' ? 
          ['Request additional financial documentation', 'Verify market claims'] : [],
        attachedDocuments: stage === 'SME_INTERVIEW_COMPLETED' ? 
          ['Business presentation', 'Management profiles'] : [],
        status: stage === 'SME_INTERVIEW_COMPLETED' ? 'COMPLETED' : 'SCHEDULED'
      },
      documents: [],
      notes: [],
      tags: [],
      stages: stages,
      score: getRandomInt(50, 95),
      assignedTo: getRandomItem(['Sarah Johnson', 'Michael Chen', 'David Moyo', 'Priya Patel', 'Thabo Molefe']),
      lastUpdated: new Date(),
      approvalWorkflow: {
        currentStep: 'ANALYST_REPORT',
        steps: [],
        committeeMeetings: []
      },
      corporateSponsorId: corporateSponsor ? corporateSponsor._id.toString() : null,
      programmeId: fundingProgramme ? fundingProgramme._id.toString() : null
    });
    
    // Add business name and applicant name for convenience
    application.businessName = businessName;
    application.applicantName = `${firstName} ${lastName}`;
    
    // Save the application
    await application.save();
    
    return application;
  } catch (error) {
    console.error('Error generating demo application:', error);
    throw error;
  }
}

// Generate multiple demo applications
async function generateMultipleDemoApplications(count, options = {}) {
  const applications = [];
  
  for (let i = 0; i < count; i++) {
    try {
      const application = await generateDemoApplication(options);
      applications.push(application);
    } catch (error) {
      console.error(`Error generating application ${i+1}:`, error);
    }
  }
  
  return applications;
}

// API endpoint to generate a demo application
router.post('/generate', async (req, res) => {
  try {
    const options = req.body;
    const count = options.count || 1;
    
    if (count > 1) {
      const applications = await generateMultipleDemoApplications(count, options);
      res.status(201).json({
        success: true,
        count: applications.length,
        message: `Generated ${applications.length} demo applications`,
        applications: applications.map(app => ({
          id: app.id,
          businessName: app.businessName,
          applicantName: app.applicantName,
          stage: app.currentStage,
          substage: app.substage,
          status: app.status
        }))
      });
    } else {
      const application = await generateDemoApplication(options);
      res.status(201).json({
        success: true,
        message: 'Demo application generated successfully',
        application: {
          id: application.id,
          businessName: application.businessName,
          applicantName: application.applicantName,
          stage: application.currentStage,
          substage: application.substage,
          status: application.status,
          _id: application._id
        }
      });
    }
  } catch (error) {
    console.error('Error generating demo application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate demo application',
      error: error.message
    });
  }
});

// API endpoint to get available stages and substages
router.get('/stages', (req, res) => {
  const stagesWithSubstages = Object.keys(SUBSTAGES).map(stage => ({
    stage,
    substages: SUBSTAGES[stage] || []
  }));
  
  res.json({
    success: true,
    stages: stagesWithSubstages,
    statuses: STATUSES
  });
});

module.exports = router;