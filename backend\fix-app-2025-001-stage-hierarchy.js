/**
 * Quick Fix Script for APP-2025-001 Stage Hierarchy Issue
 * 
 * This script initializes the missing stage hierarchy for application APP-2025-001
 * Run this script to resolve the "No stage information available" error
 * 
 * USAGE:
 * 1. Copy this file to the backend directory: cp fix-app-2025-001-stage-hierarchy.js backend/
 * 2. Run from backend directory: cd backend && node fix-app-2025-001-stage-hierarchy.js
 * 
 * OR use the MongoDB direct approach (see README_STAGE_WORKFLOW_DOCS.md)
 */

const mongoose = require('mongoose');

// Stage definitions (copied from the application)
const ApplicationMainStage = {
  ONBOARDING: 'ONBOARDING',
  BUSINESS_CASE_REVIEW: 'BUSINESS_CASE_REVIEW',
  DUE_DILIGENCE: 'DUE_DILIGENCE',
  ASSESSMENT_REPORT: 'ASSESSMENT_REPORT',
  APPLICATION_APPROVAL: 'APPLICATION_APPROVAL'
};

const ApplicationSubStage = {
  // Onboarding sub-stages
  BENEFICIARY_REGISTRATION: 'BENEFICIARY_REGISTRATION',
  PRE_SCREENING: 'PRE_SCREENING',
  
  // Business Case Review sub-stages
  DOCUMENT_COLLECTION: 'DOCUMENT_COLLECTION',
  DESKTOP_ANALYSIS: 'DESKTOP_ANALYSIS',
  
  // Due Diligence sub-stages
  DATA_VALIDATION: 'DATA_VALIDATION',
  SME_INTERVIEW: 'SME_INTERVIEW',
  SITE_VISIT: 'SITE_VISIT',
  
  // Assessment Report sub-stages
  REPORT_COMPLETION: 'REPORT_COMPLETION',
  REPORT_QUALITY_CHECK: 'REPORT_QUALITY_CHECK',
  REPORT_REVIEW: 'REPORT_REVIEW',
  
  // Application Approval sub-stages
  CORPORATE_APPROVAL_1: 'CORPORATE_APPROVAL_1',
  CORPORATE_APPROVAL_2: 'CORPORATE_APPROVAL_2',
  CORPORATE_APPROVAL_3: 'CORPORATE_APPROVAL_3',
  CORPORATE_APPROVAL_4: 'CORPORATE_APPROVAL_4'
};

const StageStatus = {
  NOT_STARTED: 'NOT_STARTED',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  SKIPPED: 'SKIPPED',
  NOT_APPLICABLE: 'NOT_APPLICABLE'
};

const MainStageToSubStagesMap = {
  [ApplicationMainStage.ONBOARDING]: [
    ApplicationSubStage.BENEFICIARY_REGISTRATION,
    ApplicationSubStage.PRE_SCREENING
  ],
  [ApplicationMainStage.BUSINESS_CASE_REVIEW]: [
    ApplicationSubStage.DOCUMENT_COLLECTION,
    ApplicationSubStage.DESKTOP_ANALYSIS
  ],
  [ApplicationMainStage.DUE_DILIGENCE]: [
    ApplicationSubStage.DATA_VALIDATION,
    ApplicationSubStage.SME_INTERVIEW,
    ApplicationSubStage.SITE_VISIT
  ],
  [ApplicationMainStage.ASSESSMENT_REPORT]: [
    ApplicationSubStage.REPORT_COMPLETION,
    ApplicationSubStage.REPORT_QUALITY_CHECK,
    ApplicationSubStage.REPORT_REVIEW
  ],
  [ApplicationMainStage.APPLICATION_APPROVAL]: [
    ApplicationSubStage.CORPORATE_APPROVAL_1,
    ApplicationSubStage.CORPORATE_APPROVAL_2,
    ApplicationSubStage.CORPORATE_APPROVAL_3,
    ApplicationSubStage.CORPORATE_APPROVAL_4
  ]
};

function initializeStageHierarchy() {
  const stageHierarchy = [];
  
  Object.values(ApplicationMainStage).forEach(mainStage => {
    const mainStageObj = {
      mainStage,
      status: StageStatus.NOT_STARTED,
      subStages: []
    };
    
    MainStageToSubStagesMap[mainStage].forEach(subStage => {
      mainStageObj.subStages.push({
        subStage,
        status: {
          status: StageStatus.NOT_STARTED,
          startedAt: null,
          completedAt: null,
          assignedTo: null,
          notes: null
        },
        history: []
      });
    });
    
    stageHierarchy.push(mainStageObj);
  });
  
  return stageHierarchy;
}

async function fixApplication() {
  try {
    console.log('🔧 Starting fix for APP-2025-001 stage hierarchy...');
    
    // Connect to MongoDB (adjust connection string as needed)
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/screening_portal';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    
    // Find the application
    const Application = mongoose.model('Application', new mongoose.Schema({}, { strict: false }));
    const application = await Application.findOne({ id: 'APP-2025-001' });
    
    if (!application) {
      console.error('❌ Application APP-2025-001 not found');
      process.exit(1);
    }
    
    console.log('📋 Found application:', application.id);
    console.log('📊 Current stage hierarchy status:', {
      hasStageHierarchy: !!application.stageHierarchy,
      stageHierarchyLength: application.stageHierarchy ? application.stageHierarchy.length : 0,
      currentMainStage: application.currentMainStage,
      currentSubStage: application.currentSubStage,
      currentStageStatus: application.currentStageStatus
    });
    
    // Check if stage hierarchy needs initialization
    if (!application.stageHierarchy || application.stageHierarchy.length === 0) {
      console.log('🚀 Initializing stage hierarchy...');
      
      // Initialize stage hierarchy
      application.stageHierarchy = initializeStageHierarchy();
      
      // Set default current stage if not set
      if (!application.currentMainStage) {
        application.currentMainStage = ApplicationMainStage.ONBOARDING;
        application.currentSubStage = ApplicationSubStage.BENEFICIARY_REGISTRATION;
        application.currentStageStatus = StageStatus.NOT_STARTED;
      }
      
      // Add initial audit log entry
      if (!application.stageStatusAuditLog) {
        application.stageStatusAuditLog = [];
      }
      
      application.stageStatusAuditLog.push({
        timestamp: new Date(),
        mainStage: application.currentMainStage,
        subStage: application.currentSubStage,
        fromStatus: null,
        toStatus: application.currentStageStatus,
        changedBy: 'system',
        reason: 'Stage hierarchy initialization - fixing missing stage data',
        isOverride: false
      });
      
      // Save the application
      await application.save();
      
      console.log('✅ Stage hierarchy initialized successfully!');
      console.log('📈 New stage hierarchy structure:');
      application.stageHierarchy.forEach(mainStage => {
        console.log(`  📁 ${mainStage.mainStage} (${mainStage.status})`);
        mainStage.subStages.forEach(subStage => {
          console.log(`    📄 ${subStage.subStage} (${subStage.status.status})`);
        });
      });
      
    } else {
      console.log('ℹ️  Stage hierarchy already exists');
      console.log('📈 Current stage hierarchy:');
      application.stageHierarchy.forEach(mainStage => {
        console.log(`  📁 ${mainStage.mainStage} (${mainStage.status})`);
        mainStage.subStages.forEach(subStage => {
          console.log(`    📄 ${subStage.subStage} (${subStage.status.status})`);
        });
      });
    }
    
    console.log('\n🎉 Fix completed successfully!');
    console.log('💡 You can now refresh the application page to see the Stage Manager tab working.');
    
  } catch (error) {
    console.error('❌ Error fixing application:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the fix
if (require.main === module) {
  fixApplication();
}

module.exports = { fixApplication, initializeStageHierarchy };
