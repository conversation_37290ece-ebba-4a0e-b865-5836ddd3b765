const Interview = require('../models/interview');
const SiteVisit = require('../models/site-visit');
const Application = require('../models/application');

/**
 * Scheduler Service
 * Handles scheduling, conflict checking, and notifications for interviews and site visits
 */
class SchedulerService {
  /**
   * Check for scheduling conflicts
   * @param {Date} startDate - The start date and time
   * @param {Number} duration - Duration in minutes
   * @param {Array} participants - Array of participant IDs
   * @returns {Promise<Array>} - Array of conflicts
   */
  async checkConflicts(startDate, duration, participants) {
    const endDate = new Date(startDate.getTime() + duration * 60000);
    
    // Find interviews that overlap with the proposed time
    const interviewConflicts = await Interview.find({
      status: { $ne: 'CANCELLED' },
      $or: [
        // Interview starts during the proposed time
        {
          scheduledDate: { $gte: startDate, $lt: endDate }
        },
        // Interview ends during the proposed time
        {
          $expr: {
            $and: [
              { $gte: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, startDate] },
              { $lt: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, endDate] }
            ]
          }
        },
        // Interview spans the entire proposed time
        {
          scheduledDate: { $lt: startDate },
          $expr: {
            $gte: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, endDate]
          }
        }
      ],
      'participants.name': { $in: participants }
    });

    // Find site visits that overlap with the proposed time
    const siteVisitConflicts = await SiteVisit.find({
      status: { $ne: 'CANCELLED' },
      $or: [
        // Site visit starts during the proposed time
        {
          scheduledDate: { $gte: startDate, $lt: endDate }
        },
        // Site visit ends during the proposed time
        {
          $expr: {
            $and: [
              { $gte: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, startDate] },
              { $lt: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, endDate] }
            ]
          }
        },
        // Site visit spans the entire proposed time
        {
          scheduledDate: { $lt: startDate },
          $expr: {
            $gte: [{ $add: ["$scheduledDate", { $multiply: ["$duration", 60000] }] }, endDate]
          }
        }
      ],
      conductedBy: { $in: participants }
    });

    return [...interviewConflicts, ...siteVisitConflicts];
  }

  /**
   * Schedule an interview
   * @param {Object} interviewData - Interview data
   * @returns {Promise<Object>} - Created interview
   */
  async scheduleInterview(interviewData) {
    // Check for conflicts
    const conflicts = await this.checkConflicts(
      interviewData.scheduledDate,
      interviewData.duration,
      interviewData.participants.map(p => p.name)
    );

    if (conflicts.length > 0) {
      throw new Error('Scheduling conflict detected');
    }

    // Create the interview
    const interview = new Interview(interviewData);
    await interview.save();

    // Update the application stage if needed
    if (interviewData.applicationId) {
      const application = await Application.findOne({ id: interviewData.applicationId });
      if (application) {
        // Update application stage if it's in the right stage
        if (application.currentMainStage === 'BUSINESS_CASE_REVIEW' ||
            application.currentMainStage === 'DUE_DILIGENCE') {
          application.currentMainStage = 'DUE_DILIGENCE';
          application.currentSubStage = 'SME_INTERVIEW';
          application.currentStageStatus = 'ACTIVE';
          await application.save();
        }
      }
    }

    return interview;
  }

  /**
   * Schedule a site visit
   * @param {Object} siteVisitData - Site visit data
   * @returns {Promise<Object>} - Created site visit
   */
  async scheduleSiteVisit(siteVisitData) {
    // Check for conflicts
    const conflicts = await this.checkConflicts(
      siteVisitData.scheduledDate,
      siteVisitData.duration,
      [siteVisitData.conductedBy]
    );

    if (conflicts.length > 0) {
      throw new Error('Scheduling conflict detected');
    }

    // Create the site visit
    const siteVisit = new SiteVisit(siteVisitData);
    await siteVisit.save();

    // Update the application stage if needed
    if (siteVisitData.applicationId) {
      const application = await Application.findOne({ id: siteVisitData.applicationId });
      if (application) {
        // Update application substage to SITE_VISIT
        application.currentMainStage = 'DUE_DILIGENCE';
        application.currentSubStage = 'SITE_VISIT';
        application.currentStageStatus = 'ACTIVE';
        await application.save();
      }
    }

    return siteVisit;
  }

  /**
   * Get all scheduled events (interviews and site visits)
   * @param {Date} startDate - Start date for range
   * @param {Date} endDate - End date for range
   * @returns {Promise<Array>} - Array of events
   */
  async getScheduledEvents(startDate, endDate) {
    const interviews = await Interview.find({
      scheduledDate: { $gte: startDate, $lte: endDate },
      status: { $ne: 'CANCELLED' }
    });

    const siteVisits = await SiteVisit.find({
      scheduledDate: { $gte: startDate, $lte: endDate },
      status: { $ne: 'CANCELLED' }
    });

    // Combine and format events for calendar display
    const events = [
      ...interviews.map(interview => ({
        id: interview.id,
        title: interview.title || `Interview for Application ${interview.applicationId}`,
        start: interview.scheduledDate,
        end: new Date(interview.scheduledDate.getTime() + interview.duration * 60000),
        type: 'interview',
        applicationId: interview.applicationId,
        status: interview.status
      })),
      ...siteVisits.map(siteVisit => ({
        id: siteVisit.id,
        title: `Site Visit for Application ${siteVisit.applicationId}`,
        start: siteVisit.scheduledDate,
        end: new Date(siteVisit.scheduledDate.getTime() + siteVisit.duration * 60000),
        type: 'siteVisit',
        applicationId: siteVisit.applicationId,
        status: siteVisit.status,
        location: siteVisit.location.address
      }))
    ];

    return events;
  }

  /**
   * Get events for a specific application
   * @param {String} applicationId - Application ID
   * @returns {Promise<Array>} - Array of events
   */
  async getApplicationEvents(applicationId) {
    const interviews = await Interview.find({
      applicationId: applicationId,
      status: { $ne: 'CANCELLED' }
    });

    const siteVisits = await SiteVisit.find({
      applicationId: applicationId,
      status: { $ne: 'CANCELLED' }
    });

    // Combine and format events
    const events = [
      ...interviews.map(interview => ({
        id: interview.id,
        title: interview.title || `Interview`,
        start: interview.scheduledDate,
        end: new Date(interview.scheduledDate.getTime() + interview.duration * 60000),
        type: 'interview',
        status: interview.status
      })),
      ...siteVisits.map(siteVisit => ({
        id: siteVisit.id,
        title: `Site Visit`,
        start: siteVisit.scheduledDate,
        end: new Date(siteVisit.scheduledDate.getTime() + siteVisit.duration * 60000),
        type: 'siteVisit',
        status: siteVisit.status,
        location: siteVisit.location.address
      }))
    ];

    return events;
  }

  /**
   * Get upcoming events for notifications
   * @param {Number} daysAhead - Number of days to look ahead
   * @returns {Promise<Array>} - Array of upcoming events
   */
  async getUpcomingEvents(daysAhead = 7) {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);

    return this.getScheduledEvents(now, futureDate);
  }

  /**
   * Reschedule an interview
   * @param {String} interviewId - Interview ID
   * @param {Date} newDate - New scheduled date
   * @param {Number} newDuration - New duration in minutes
   * @returns {Promise<Object>} - Updated interview
   */
  async rescheduleInterview(interviewId, newDate, newDuration) {
    const interview = await Interview.findOne({ id: interviewId });
    if (!interview) {
      throw new Error('Interview not found');
    }

    // Check for conflicts with new schedule
    const conflicts = await this.checkConflicts(
      newDate,
      newDuration,
      interview.participants.map(p => p.name)
    );

    // Filter out the current interview from conflicts
    const otherConflicts = conflicts.filter(c => c.id !== interviewId);
    if (otherConflicts.length > 0) {
      throw new Error('Scheduling conflict detected');
    }

    // Update the interview
    interview.scheduledDate = newDate;
    interview.duration = newDuration;
    await interview.save();

    return interview;
  }

  /**
   * Reschedule a site visit
   * @param {String} siteVisitId - Site visit ID
   * @param {Date} newDate - New scheduled date
   * @param {Number} newDuration - New duration in minutes
   * @returns {Promise<Object>} - Updated site visit
   */
  async rescheduleSiteVisit(siteVisitId, newDate, newDuration) {
    const siteVisit = await SiteVisit.findOne({ id: siteVisitId });
    if (!siteVisit) {
      throw new Error('Site visit not found');
    }

    // Check for conflicts with new schedule
    const conflicts = await this.checkConflicts(
      newDate,
      newDuration,
      [siteVisit.conductedBy]
    );

    // Filter out the current site visit from conflicts
    const otherConflicts = conflicts.filter(c => c.id !== siteVisitId);
    if (otherConflicts.length > 0) {
      throw new Error('Scheduling conflict detected');
    }

    // Update the site visit
    siteVisit.scheduledDate = newDate;
    siteVisit.duration = newDuration;
    await siteVisit.save();

    return siteVisit;
  }

  /**
   * Cancel an interview
   * @param {String} interviewId - Interview ID
   * @returns {Promise<Object>} - Updated interview
   */
  async cancelInterview(interviewId) {
    const interview = await Interview.findOne({ id: interviewId });
    if (!interview) {
      throw new Error('Interview not found');
    }

    interview.status = 'CANCELLED';
    await interview.save();

    return interview;
  }

  /**
   * Cancel a site visit
   * @param {String} siteVisitId - Site visit ID
   * @returns {Promise<Object>} - Updated site visit
   */
  async cancelSiteVisit(siteVisitId) {
    const siteVisit = await SiteVisit.findOne({ id: siteVisitId });
    if (!siteVisit) {
      throw new Error('Site visit not found');
    }

    siteVisit.status = 'CANCELLED';
    await siteVisit.save();

    return siteVisit;
  }
}

module.exports = new SchedulerService();
