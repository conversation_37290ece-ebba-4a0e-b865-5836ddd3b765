/**
 * <PERSON><PERSON><PERSON> to add 30 applications directly to MongoDB
 * This script is designed to be run inside the MongoDB Docker container
 */

db = db.getSiblingDB('funding-screening-app');

// Define statuses and stages
const statuses = ['pending', 'in-review', 'approved', 'rejected'];
const stages = {
  'pending': ['ONBOARDING', 'PRE_SCREENING_PENDING', 'DOCUMENT_COLLECTION'],
  'in-review': ['DESKTOP_ANALYSIS', 'DATA_VALIDATION', 'SME_INTERVIEW_SCHEDULED', 'SME_INTERVIEW_COMPLETED', 'FINAL_REVIEW'],
  'approved': ['APPROVED'],
  'rejected': ['REJECTED']
};

// Define industries for variety
const industries = [
  'Technology', 'Healthcare', 'Education', 'Food & Beverage', 'Construction',
  'Retail', 'Agriculture', 'Transportation', 'Tourism', 'Manufacturing'
];

// Define assignees
const assignees = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];

// Check if applications already exist
const existingCount = db.applications.countDocuments();
print(`MongoDB currently contains ${existingCount} applications`);

if (existingCount > 0) {
  print('Clearing existing applications...');
  db.applications.deleteMany({});
  print('Existing applications cleared');
}

// Create 30 applications
const applications = [];

for (let i = 0; i < 30; i++) {
  // Generate application ID
  const id = `APP-2025-${String(i + 1).padStart(3, '0')}`;
  
  // Select random industry
  const industry = industries[i % industries.length];
  
  // Select random status
  const status = statuses[i % 4];
  
  // Select random stage based on status
  const stageOptions = stages[status];
  const currentStage = stageOptions[i % stageOptions.length];
  
  // Generate random funding amount between 200,000 and 1,000,000
  const fundingAmount = Math.floor(Math.random() * 800000) + 200000;
  
  // Generate random score between 50 and 95
  const score = Math.floor(Math.random() * 46) + 50;
  
  // Generate business name
  const businessName = `${industry} Solutions ${i + 1}`;
  
  // Create application object
  const application = {
    id: id,
    registrationNumber: `REG-2025-${String(i + 1).padStart(3, '0')}`,
    fundingAmount: fundingAmount,
    submissionDate: new Date(2025, 2, i + 1), // March 2025
    currentMainStage: currentStage,
    currentSubStage: 'INITIAL',
    currentStageStatus: status === 'pending' ? 'ACTIVE' : (status === 'approved' || status === 'rejected' ? 'COMPLETED' : 'IN_PROGRESS'),
    status: status,
    personalInfo: {
      firstName: `First${i + 1}`,
      lastName: `Last${i + 1}`,
      email: `contact${i + 1}@example.co.za`,
      phone: `+27 8${i % 10} ${i + 1}${i + 1}${i + 1}`
    },
    businessInfo: {
      legalName: businessName,
      industry: industry,
      businessType: 'Private Company'
    },
    score: score,
    assignedTo: assignees[i % assignees.length],
    lastUpdated: new Date()
  };
  
  applications.push(application);
}

// Insert all applications
db.applications.insertMany(applications);
print(`Created ${applications.length} applications`);

// Verify creation
const finalCount = db.applications.countDocuments();
print(`\nCreation complete!`);
print(`MongoDB now contains ${finalCount} applications`);

// Check applications by status
const statusCounts = db.applications.aggregate([
  { $group: { _id: "$status", count: { $sum: 1 } } }
]).toArray();

print('\nApplications by status:');
statusCounts.forEach(item => {
  print(`  ${item._id || 'UNKNOWN'}: ${item.count}`);
});

// Check applications by stage
const stageCounts = db.applications.aggregate([
  { $group: { _id: "$currentMainStage", count: { $sum: 1 } } }
]).toArray();

print('\nApplications by stage:');
stageCounts.forEach(item => {
  print(`  ${item._id || 'UNKNOWN'}: ${item.count}`);
});

print('MongoDB script completed successfully');