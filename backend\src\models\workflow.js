const mongoose = require('mongoose');

const workflowSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['Sequential', 'Parallel', 'Conditional', 'Priority'],
    default: 'Sequential'
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Draft'],
    default: 'Draft'
  },
  stages: [{
    mainStage: {
      type: String,
      required: true
    },
    subStages: [{
      name: String,
      required: Boolean,
      order: Number,
      conditions: [{
        field: String,
        operator: String,
        value: mongoose.Schema.Types.Mixed
      }],
      parallelGroup: Number // For parallel workflows
    }]
  }],
  automationRules: [{
    name: String,
    description: String,
    trigger: {
      type: String,
      enum: ['StageComplete', 'DocumentUploaded', 'TimeElapsed', 'ConditionMet']
    },
    triggerDetails: mongoose.Schema.Types.Mixed,
    actions: [{
      type: {
        type: String,
        enum: ['AssignReviewer', 'SendNotification', 'UpdateStatus', 'CreateTask']
      },
      details: mongoose.Schema.Types.Mixed
    }],
    enabled: {
      type: Boolean,
      default: true
    }
  }],
  applicablePrograms: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FundingProgramme'
  }],
  applicableSectors: [String],
  priority: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
workflowSchema.index({ name: 1 });
workflowSchema.index({ status: 1 });
workflowSchema.index({ type: 1 });

module.exports = mongoose.model('Workflow', workflowSchema);