const mongoose = require('mongoose');

const chatMessageSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true
  },
  roomId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatRoom',
    required: true
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Message content
  content: {
    text: {
      type: String,
      trim: true,
      maxlength: 5000
    },
    type: {
      type: String,
      enum: ['TEXT', 'FILE', 'IMAGE', 'VOICE', 'SYSTEM', 'NOTIFICATION'],
      default: 'TEXT'
    }
  },
  // File attachments
  attachments: [{
    fileName: String,
    fileUrl: String,
    fileType: String,
    fileSize: Number,
    thumbnailUrl: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Voice note details
  voiceNote: {
    duration: Number, // in seconds
    fileUrl: String,
    transcription: String
  },
  // Message metadata
  metadata: {
    isEdited: {
      type: Boolean,
      default: false
    },
    editedAt: Date,
    editHistory: [{
      content: String,
      editedAt: Date
    }],
    isDeleted: {
      type: Boolean,
      default: false
    },
    deletedAt: Date,
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  // Reply/Thread
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatMessage'
  },
  threadMessages: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatMessage'
  }],
  // Mentions
  mentions: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    username: String
  }],
  // Read receipts
  readBy: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Delivery status
  deliveryStatus: {
    sent: {
      type: Boolean,
      default: true
    },
    delivered: {
      type: Boolean,
      default: false
    },
    deliveredTo: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      deliveredAt: Date
    }]
  },
  // Reactions
  reactions: [{
    emoji: String,
    users: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      reactedAt: {
        type: Date,
        default: Date.now
      }
    }]
  }],
  // System message details
  systemMessage: {
    type: {
      type: String,
      enum: ['USER_JOINED', 'USER_LEFT', 'USER_ADDED', 'USER_REMOVED', 'ROOM_CREATED', 'ROOM_NAME_CHANGED', 'SETTINGS_CHANGED']
    },
    data: mongoose.Schema.Types.Mixed
  },
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
chatMessageSchema.index({ roomId: 1, createdAt: -1 });
chatMessageSchema.index({ senderId: 1, createdAt: -1 });
chatMessageSchema.index({ 'mentions.userId': 1 });
chatMessageSchema.index({ replyTo: 1 });
chatMessageSchema.index({ 'metadata.isDeleted': 1 });

// Pre-save middleware to generate message ID
chatMessageSchema.pre('save', async function(next) {
  try {
    // Generate message ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(8, '0');
      this.id = `MSG-2025-${nextNumber}`;
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Post-save middleware to update room's last activity
chatMessageSchema.post('save', async function(doc) {
  try {
    const ChatRoom = mongoose.model('ChatRoom');
    await ChatRoom.findByIdAndUpdate(doc.roomId, {
      lastActivity: doc.createdAt,
      lastMessage: doc._id,
      $inc: { messageCount: 1 }
    });
    
    // Update unread counts for other participants
    const ChatParticipant = mongoose.model('ChatParticipant');
    await ChatParticipant.updateMany(
      {
        roomId: doc.roomId,
        userId: { $ne: doc.senderId },
        isActive: true
      },
      {
        $inc: { unreadCount: 1 }
      }
    );
  } catch (error) {
    console.error('Error updating room after message save:', error);
  }
});

// Virtual for formatted timestamp
chatMessageSchema.virtual('formattedTime').get(function() {
  const now = new Date();
  const messageDate = new Date(this.createdAt);
  const diffMs = now - messageDate;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  
  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return messageDate.toLocaleDateString();
});

// Method to mark as read by user
chatMessageSchema.methods.markAsReadBy = async function(userId) {
  const alreadyRead = this.readBy.some(r => r.userId.toString() === userId.toString());
  
  if (!alreadyRead) {
    this.readBy.push({
      userId: userId,
      readAt: new Date()
    });
    await this.save();
    
    // Update participant's unread count
    const ChatParticipant = mongoose.model('ChatParticipant');
    const participant = await ChatParticipant.findOne({
      roomId: this.roomId,
      userId: userId
    });
    
    if (participant) {
      await participant.markMessagesAsRead(this._id);
    }
  }
};

// Method to add reaction
chatMessageSchema.methods.addReaction = async function(userId, emoji) {
  let reactionGroup = this.reactions.find(r => r.emoji === emoji);
  
  if (!reactionGroup) {
    reactionGroup = {
      emoji: emoji,
      users: []
    };
    this.reactions.push(reactionGroup);
  }
  
  const alreadyReacted = reactionGroup.users.some(u => u.userId.toString() === userId.toString());
  
  if (!alreadyReacted) {
    reactionGroup.users.push({
      userId: userId,
      reactedAt: new Date()
    });
    await this.save();
  }
};

// Method to remove reaction
chatMessageSchema.methods.removeReaction = async function(userId, emoji) {
  const reactionGroup = this.reactions.find(r => r.emoji === emoji);
  
  if (reactionGroup) {
    reactionGroup.users = reactionGroup.users.filter(u => u.userId.toString() !== userId.toString());
    
    // Remove reaction group if no users left
    if (reactionGroup.users.length === 0) {
      this.reactions = this.reactions.filter(r => r.emoji !== emoji);
    }
    
    await this.save();
  }
};

// Method to edit message
chatMessageSchema.methods.editMessage = async function(newContent, editedBy) {
  if (this.senderId.toString() !== editedBy.toString()) {
    throw new Error('Only the sender can edit their message');
  }
  
  if (this.metadata.isDeleted) {
    throw new Error('Cannot edit deleted message');
  }
  
  // Save edit history
  if (!this.metadata.editHistory) {
    this.metadata.editHistory = [];
  }
  
  this.metadata.editHistory.push({
    content: this.content.text,
    editedAt: new Date()
  });
  
  // Update content
  this.content.text = newContent;
  this.metadata.isEdited = true;
  this.metadata.editedAt = new Date();
  
  await this.save();
};

// Method to soft delete message
chatMessageSchema.methods.deleteMessage = async function(deletedBy) {
  this.metadata.isDeleted = true;
  this.metadata.deletedAt = new Date();
  this.metadata.deletedBy = deletedBy;
  
  // Clear sensitive content
  this.content.text = '[Message deleted]';
  this.attachments = [];
  this.voiceNote = undefined;
  
  await this.save();
};

// Static method to get messages for a room
chatMessageSchema.statics.getRoomMessages = async function(roomId, options = {}) {
  const {
    limit = 50,
    before = null,
    after = null,
    includeDeleted = false
  } = options;
  
  const query = { roomId: roomId };
  
  if (!includeDeleted) {
    query['metadata.isDeleted'] = { $ne: true };
  }
  
  if (before) {
    query.createdAt = { $lt: before };
  }
  
  if (after) {
    query.createdAt = { $gt: after };
  }
  
  return await this.find(query)
    .populate('senderId', 'username firstName lastName profilePicture')
    .populate('replyTo', 'content.text senderId')
    .populate('mentions.userId', 'username')
    .sort('-createdAt')
    .limit(limit);
};

// Static method to search messages
chatMessageSchema.statics.searchMessages = async function(roomId, searchTerm, options = {}) {
  const {
    limit = 50,
    includeDeleted = false
  } = options;
  
  const query = {
    roomId: roomId,
    'content.text': { $regex: searchTerm, $options: 'i' }
  };
  
  if (!includeDeleted) {
    query['metadata.isDeleted'] = { $ne: true };
  }
  
  return await this.find(query)
    .populate('senderId', 'username firstName lastName profilePicture')
    .sort('-createdAt')
    .limit(limit);
};

const ChatMessage = mongoose.model('ChatMessage', chatMessageSchema);

module.exports = ChatMessage;