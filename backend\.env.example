# ===========================================
# ENVIRONMENT VARIABLES EXAMPLE
# ===========================================
# Copy this file to .env and update the values
# For production, copy .env.production to .env

# Application Environment
NODE_ENV=development
APP_NAME=Funding Screening App
APP_VERSION=1.0.0

# Server Configuration
PORT=3002
HOST=localhost
API_PREFIX=/api/v1

# Frontend Configuration
FRONTEND_URL=http://localhost:4200
CORS_ORIGIN=http://localhost:4200

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/funding-screening-app
DB_NAME=funding-screening-app
DB_HOST=localhost
DB_PORT=27017

# Authentication & Security (CHANGE THESE!)
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRATION=30m
REFRESH_TOKEN_SECRET=your-refresh-secret-key-here
REFRESH_TOKEN_EXPIRATION=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your-email-username
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpeg,jpg,png,gif,pdf,doc,docx,xls,xlsx,txt

# WebSocket Configuration
WEBSOCKET_PORT=3001
WEBSOCKET_URL=ws://localhost:3001

# Push Notifications (Generate your own VAPID keys)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# Notification Configuration
NOTIFICATION_RECONNECT_INTERVAL=5000
NOTIFICATION_MAX_RECONNECT_ATTEMPTS=10
NOTIFICATION_TOAST_DURATION=5000
NOTIFICATION_POLLING_INTERVAL=30000

# Security Configuration
ENABLE_CSP=true
SECURE_COOKIES=false
ENABLE_HELMET=true

# Logging Configuration
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
LOG_FILE_PATH=logs/app.log

# Feature Flags
ENABLE_STANDALONE_INTERVIEW=false
ENABLE_MONGODB=true
ENABLE_REAL_TIME_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=true

# Development Tools
ENABLE_DEBUG_LOGGING=true
ENABLE_CONSOLE_NOTIFICATIONS=true
ENABLE_HOT_RELOAD=true

# Cache Configuration
CACHE_TTL=3600
ENABLE_CACHE=true

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=86400000

# Third-party API Keys (Optional)
# GOOGLE_MAPS_API_KEY=your-google-maps-key
# STRIPE_SECRET_KEY=your-stripe-key
# SENDGRID_API_KEY=your-sendgrid-key
