const { authenticateToken, authorizeRole, checkPermission } = require('./auth');

/**
 * Notification-specific authentication and authorization middleware
 */

/**
 * Middleware to check if user can create notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canCreateNotifications = (req, res, next) => {
  // Check if user has admin role or specific notification permissions
  const userRoles = req.user.roles || [];
  const legacyRole = req.user.role;
  const userPermissions = req.user.permissions || [];
  
  const hasAdminRole = userRoles.includes('admin') || 
                      userRoles.includes('manager') ||
                      legacyRole === 'SYSTEM_ADMINISTRATOR' ||
                      legacyRole === 'MANAGER';
  
  const hasNotificationPermission = userPermissions.includes('create:notifications') ||
                                   userPermissions.includes('manage:notifications');
  
  if (hasAdminRole || hasNotificationPermission) {
    return next();
  }
  
  return res.status(403).json({
    error: {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: 'User does not have permission to create notifications'
    }
  });
};

/**
 * Middleware to check if user can manage notification templates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canManageTemplates = (req, res, next) => {
  const userRoles = req.user.roles || [];
  const legacyRole = req.user.role;
  const userPermissions = req.user.permissions || [];
  
  const hasAdminRole = userRoles.includes('admin') || 
                      legacyRole === 'SYSTEM_ADMINISTRATOR';
  
  const hasTemplatePermission = userPermissions.includes('manage:notification_templates');
  
  if (hasAdminRole || hasTemplatePermission) {
    return next();
  }
  
  return res.status(403).json({
    error: {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: 'User does not have permission to manage notification templates'
    }
  });
};

/**
 * Middleware to check if user can view notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canViewNotifications = (req, res, next) => {
  // All authenticated users can view their own notifications
  // Admins can view all notifications
  const userRoles = req.user.roles || [];
  const legacyRole = req.user.role;
  const userPermissions = req.user.permissions || [];
  
  const hasAdminRole = userRoles.includes('admin') || 
                      userRoles.includes('manager') ||
                      legacyRole === 'SYSTEM_ADMINISTRATOR' ||
                      legacyRole === 'MANAGER';
  
  const hasViewPermission = userPermissions.includes('read:notifications') ||
                           userPermissions.includes('read:own_notifications');
  
  // Store permission level for use in route handlers
  req.notificationPermissions = {
    canViewAll: hasAdminRole || userPermissions.includes('read:notifications'),
    canViewOwn: true // All authenticated users can view their own
  };
  
  if (hasAdminRole || hasViewPermission || true) { // All authenticated users can view notifications
    return next();
  }
  
  return res.status(403).json({
    error: {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: 'User does not have permission to view notifications'
    }
  });
};

/**
 * Middleware to check if user can manage notification settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canManageSettings = (req, res, next) => {
  const userRoles = req.user.roles || [];
  const legacyRole = req.user.role;
  const userPermissions = req.user.permissions || [];
  
  const hasAdminRole = userRoles.includes('admin') || 
                      legacyRole === 'SYSTEM_ADMINISTRATOR';
  
  const hasSettingsPermission = userPermissions.includes('manage:notification_settings');
  
  if (hasAdminRole || hasSettingsPermission) {
    return next();
  }
  
  return res.status(403).json({
    error: {
      code: 'INSUFFICIENT_PERMISSIONS',
      message: 'User does not have permission to manage notification settings'
    }
  });
};

/**
 * Middleware to check if user can send notifications to specific targets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canSendToTarget = (req, res, next) => {
  const { targetAudience, targetUsers, targetRoles, targetEntities } = req.body;
  const userRoles = req.user.roles || [];
  const legacyRole = req.user.role;
  const userPermissions = req.user.permissions || [];
  
  const hasAdminRole = userRoles.includes('admin') || 
                      legacyRole === 'SYSTEM_ADMINISTRATOR';
  
  // Admins can send to anyone
  if (hasAdminRole) {
    return next();
  }
  
  // Check specific targeting permissions
  switch (targetAudience) {
    case 'ALL_USERS':
      if (!userPermissions.includes('send:notifications_all_users')) {
        return res.status(403).json({
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User does not have permission to send notifications to all users'
          }
        });
      }
      break;
      
    case 'ROLE_BASED':
      if (!userPermissions.includes('send:notifications_role_based')) {
        return res.status(403).json({
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User does not have permission to send role-based notifications'
          }
        });
      }
      
      // Check if user can send to specific roles
      const restrictedRoles = ['admin', 'SYSTEM_ADMINISTRATOR'];
      const hasRestrictedRole = targetRoles && targetRoles.some(role => restrictedRoles.includes(role));
      
      if (hasRestrictedRole && !userPermissions.includes('send:notifications_admin_roles')) {
        return res.status(403).json({
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User does not have permission to send notifications to admin roles'
          }
        });
      }
      break;
      
    case 'ENTITY_BASED':
      if (!userPermissions.includes('send:notifications_entity_based')) {
        return res.status(403).json({
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User does not have permission to send entity-based notifications'
          }
        });
      }
      break;
      
    case 'SPECIFIC_USERS':
      if (!userPermissions.includes('send:notifications_specific_users')) {
        return res.status(403).json({
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'User does not have permission to send notifications to specific users'
          }
        });
      }
      break;
      
    default:
      return res.status(400).json({
        error: {
          code: 'INVALID_TARGET_AUDIENCE',
          message: 'Invalid target audience specified'
        }
      });
  }
  
  next();
};

/**
 * Middleware to validate notification data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateNotificationData = (req, res, next) => {
  const { title, message, type, category, targetAudience } = req.body;
  const errors = [];
  
  // Required fields validation
  if (!title || title.trim().length === 0) {
    errors.push('Title is required');
  } else if (title.length > 200) {
    errors.push('Title must be 200 characters or less');
  }
  
  if (!message || message.trim().length === 0) {
    errors.push('Message is required');
  } else if (message.length > 1000) {
    errors.push('Message must be 1000 characters or less');
  }
  
  if (!type) {
    errors.push('Type is required');
  } else {
    const validTypes = [
      'APPLICATION_STATUS_CHANGE',
      'INTERVIEW_SCHEDULED',
      'SITE_VISIT_SCHEDULED',
      'DOCUMENT_REQUIRED',
      'APPROVAL_REQUIRED',
      'DEADLINE_REMINDER',
      'SYSTEM_ANNOUNCEMENT',
      'MEETING_INVITATION',
      'REPORT_READY',
      'SCORECARD_COMPLETED',
      'CUSTOM'
    ];
    
    if (!validTypes.includes(type)) {
      errors.push('Invalid notification type');
    }
  }
  
  if (!category) {
    errors.push('Category is required');
  } else {
    const validCategories = [
      'APPLICATION', 'INTERVIEW', 'SITE_VISIT', 'DOCUMENT', 
      'APPROVAL', 'SYSTEM', 'MEETING', 'REPORT', 'SCORECARD', 'GENERAL'
    ];
    
    if (!validCategories.includes(category)) {
      errors.push('Invalid notification category');
    }
  }
  
  if (!targetAudience) {
    errors.push('Target audience is required');
  } else {
    const validAudiences = ['ALL_USERS', 'SPECIFIC_USERS', 'ROLE_BASED', 'ENTITY_BASED'];
    
    if (!validAudiences.includes(targetAudience)) {
      errors.push('Invalid target audience');
    }
  }
  
  // Validate target-specific requirements
  if (targetAudience === 'SPECIFIC_USERS') {
    const { targetUsers } = req.body;
    if (!targetUsers || !Array.isArray(targetUsers) || targetUsers.length === 0) {
      errors.push('Target users are required for specific user targeting');
    }
  }
  
  if (targetAudience === 'ROLE_BASED') {
    const { targetRoles } = req.body;
    if (!targetRoles || !Array.isArray(targetRoles) || targetRoles.length === 0) {
      errors.push('Target roles are required for role-based targeting');
    }
  }
  
  if (targetAudience === 'ENTITY_BASED') {
    const { targetEntities } = req.body;
    if (!targetEntities || typeof targetEntities !== 'object') {
      errors.push('Target entities are required for entity-based targeting');
    } else {
      const { corporateSponsors, fundingProgrammes, serviceProviders } = targetEntities;
      const hasAnyEntity = (corporateSponsors && corporateSponsors.length > 0) ||
                          (fundingProgrammes && fundingProgrammes.length > 0) ||
                          (serviceProviders && serviceProviders.length > 0);
      
      if (!hasAnyEntity) {
        errors.push('At least one target entity must be specified for entity-based targeting');
      }
    }
  }
  
  // Validate delivery methods
  const { deliveryMethods } = req.body;
  if (deliveryMethods && typeof deliveryMethods === 'object') {
    const validMethods = ['inApp', 'email', 'sms', 'push'];
    const hasAnyMethod = validMethods.some(method => deliveryMethods[method] === true);
    
    if (!hasAnyMethod) {
      errors.push('At least one delivery method must be enabled');
    }
  }
  
  // Validate priority
  const { priority } = req.body;
  if (priority) {
    const validPriorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
    if (!validPriorities.includes(priority)) {
      errors.push('Invalid priority level');
    }
  }
  
  // Validate scheduled date
  const { scheduledFor } = req.body;
  if (scheduledFor) {
    const scheduledDate = new Date(scheduledFor);
    if (isNaN(scheduledDate.getTime())) {
      errors.push('Invalid scheduled date format');
    }
  }
  
  if (errors.length > 0) {
    return res.status(400).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Notification data validation failed',
        details: errors
      }
    });
  }
  
  next();
};

/**
 * Middleware to check if user owns the notification or has admin access
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const canAccessNotification = async (req, res, next) => {
  try {
    const { notificationId } = req.params;
    const userRoles = req.user.roles || [];
    const legacyRole = req.user.role;
    
    const hasAdminRole = userRoles.includes('admin') || 
                        userRoles.includes('manager') ||
                        legacyRole === 'SYSTEM_ADMINISTRATOR' ||
                        legacyRole === 'MANAGER';
    
    // Admins can access any notification
    if (hasAdminRole) {
      return next();
    }
    
    // Check if user created the notification
    const Notification = require('../models/notification');
    const notification = await Notification.findById(notificationId);
    
    if (!notification) {
      return res.status(404).json({
        error: {
          code: 'NOTIFICATION_NOT_FOUND',
          message: 'Notification not found'
        }
      });
    }
    
    if (notification.createdBy.toString() === req.user.sub) {
      return next();
    }
    
    return res.status(403).json({
      error: {
        code: 'ACCESS_DENIED',
        message: 'You do not have access to this notification'
      }
    });
    
  } catch (error) {
    console.error('Error checking notification access:', error);
    return res.status(500).json({
      error: {
        code: 'SERVER_ERROR',
        message: 'Error checking notification access'
      }
    });
  }
};

module.exports = {
  authenticateToken,
  authorizeRole,
  checkPermission,
  canCreateNotifications,
  canManageTemplates,
  canViewNotifications,
  canManageSettings,
  canSendToTarget,
  validateNotificationData,
  canAccessNotification
};