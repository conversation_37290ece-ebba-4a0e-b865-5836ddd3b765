const express = require('express');
const router = express.Router();
const Profile = require('../models/profile');
const User = require('../models/user');
const SME = require('../models/sme');
const CorporateSponsor = require('../models/corporate-sponsor');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Get all profiles with filtering and pagination
router.get('/', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    let query = {};
    
    // Handle search
    if (req.query.search) {
      query.$or = [
        { firstName: { $regex: req.query.search, $options: 'i' } },
        { lastName: { $regex: req.query.search, $options: 'i' } },
        { idNumber: { $regex: req.query.search, $options: 'i' } },
        { 'contactDetails.email': { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    // Handle filters
    if (req.query.profileType) {
      query.profileType = req.query.profileType;
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.isVerified !== undefined) {
      query.isVerified = req.query.isVerified === 'true';
    }
    
    if (req.query.smeId) {
      query.smeId = req.query.smeId;
    }
    
    if (req.query.corporateSponsorId) {
      query.corporateSponsorId = req.query.corporateSponsorId;
    }
    
    const profiles = await Profile.find(query)
      .populate('userId', 'username email')
      .populate('smeId', 'businessName registrationNumber')
      .populate('corporateSponsorId', 'name industry')
      .populate('verifiedBy', 'username email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);
    
    const total = await Profile.countDocuments(query);
    
    res.json({
      profiles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    console.error('Error fetching profiles:', err);
    res.status(500).json({ message: 'Error fetching profiles', error: err.message });
  }
});

// Get a specific profile by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const profile = await Profile.findById(req.params.id)
      .populate('userId', 'username email')
      .populate('smeId', 'businessName registrationNumber industry')
      .populate('corporateSponsorId', 'name industry')
      .populate('verifiedBy', 'username email');
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    res.json(profile);
  } catch (err) {
    console.error('Error fetching profile:', err);
    res.status(500).json({ message: 'Error fetching profile', error: err.message });
  }
});

// Create a new profile
router.post('/', authenticateToken, async (req, res) => {
  try {
    // Validate required fields
    const { userId, profileType, firstName, lastName } = req.body;
    
    if (!userId || !profileType || !firstName || !lastName) {
      return res.status(400).json({ 
        message: 'User ID, profile type, first name, and last name are required' 
      });
    }
    
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(400).json({ message: 'User not found' });
    }
    
    // Check if user already has a profile
    const existingProfile = await Profile.findOne({ userId });
    if (existingProfile) {
      return res.status(400).json({ message: 'User already has a profile' });
    }
    
    // Validate SME or Corporate Sponsor association
    if (req.body.smeId && req.body.corporateSponsorId) {
      return res.status(400).json({ 
        message: 'Profile cannot be associated with both SME and Corporate Sponsor' 
      });
    }
    
    if (req.body.smeId) {
      const sme = await SME.findById(req.body.smeId);
      if (!sme) {
        return res.status(400).json({ message: 'SME not found' });
      }
    }
    
    if (req.body.corporateSponsorId) {
      const corporateSponsor = await CorporateSponsor.findById(req.body.corporateSponsorId);
      if (!corporateSponsor) {
        return res.status(400).json({ message: 'Corporate Sponsor not found' });
      }
    }
    
    // Generate unique profile ID
    const count = await Profile.countDocuments();
    const profileId = `PROF-${new Date().getFullYear()}-${(count + 1).toString().padStart(4, '0')}`;
    
    const profile = new Profile({
      ...req.body,
      id: profileId
    });
    
    const savedProfile = await profile.save();
    
    // Populate the response
    const populatedProfile = await Profile.findById(savedProfile._id)
      .populate('userId', 'username email')
      .populate('smeId', 'businessName registrationNumber')
      .populate('corporateSponsorId', 'name industry');
    
    res.status(201).json(populatedProfile);
  } catch (err) {
    console.error('Error creating profile:', err);
    res.status(400).json({ message: 'Error creating profile', error: err.message });
  }
});

// Update a profile
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const profile = await Profile.findById(req.params.id);
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    // Validate SME or Corporate Sponsor association
    if (req.body.smeId && req.body.corporateSponsorId) {
      return res.status(400).json({ 
        message: 'Profile cannot be associated with both SME and Corporate Sponsor' 
      });
    }
    
    if (req.body.smeId && req.body.smeId !== profile.smeId?.toString()) {
      const sme = await SME.findById(req.body.smeId);
      if (!sme) {
        return res.status(400).json({ message: 'SME not found' });
      }
    }
    
    if (req.body.corporateSponsorId && req.body.corporateSponsorId !== profile.corporateSponsorId?.toString()) {
      const corporateSponsor = await CorporateSponsor.findById(req.body.corporateSponsorId);
      if (!corporateSponsor) {
        return res.status(400).json({ message: 'Corporate Sponsor not found' });
      }
    }
    
    // Update profile
    Object.assign(profile, req.body);
    profile.updatedAt = new Date();
    
    const updatedProfile = await profile.save();
    
    // Populate the response
    const populatedProfile = await Profile.findById(updatedProfile._id)
      .populate('userId', 'username email')
      .populate('smeId', 'businessName registrationNumber')
      .populate('corporateSponsorId', 'name industry')
      .populate('verifiedBy', 'username email');
    
    res.json(populatedProfile);
  } catch (err) {
    console.error('Error updating profile:', err);
    res.status(400).json({ message: 'Error updating profile', error: err.message });
  }
});

// Delete a profile
router.delete('/:id', authenticateToken, authorizeRoles(['admin', 'manager']), async (req, res) => {
  try {
    const profile = await Profile.findById(req.params.id);
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    await Profile.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Profile deleted successfully' });
  } catch (err) {
    console.error('Error deleting profile:', err);
    res.status(500).json({ message: 'Error deleting profile', error: err.message });
  }
});

// Verify a profile
router.patch('/:id/verify', authenticateToken, authorizeRoles(['admin', 'manager', 'analyst']), async (req, res) => {
  try {
    const profile = await Profile.findById(req.params.id);
    
    if (!profile) {
      return res.status(404).json({ message: 'Profile not found' });
    }
    
    profile.isVerified = true;
    profile.verificationDate = new Date();
    profile.verifiedBy = req.user.id;
    profile.updatedAt = new Date();
    
    const updatedProfile = await profile.save();
    
    // Populate the response
    const populatedProfile = await Profile.findById(updatedProfile._id)
      .populate('userId', 'username email')
      .populate('smeId', 'businessName registrationNumber')
      .populate('corporateSponsorId', 'name industry')
      .populate('verifiedBy', 'username email');
    
    res.json(populatedProfile);
  } catch (err) {
    console.error('Error verifying profile:', err);
    res.status(500).json({ message: 'Error verifying profile', error: err.message });
  }
});

// Get profile statistics
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    const totalProfiles = await Profile.countDocuments();
    const verifiedProfiles = await Profile.countDocuments({ isVerified: true });
    const activeProfiles = await Profile.countDocuments({ status: 'active' });
    const profilesByType = await Profile.aggregate([
      { $group: { _id: '$profileType', count: { $sum: 1 } } }
    ]);
    
    res.json({
      totalProfiles,
      verifiedProfiles,
      activeProfiles,
      verificationRate: totalProfiles > 0 ? (verifiedProfiles / totalProfiles * 100).toFixed(2) : 0,
      profilesByType
    });
  } catch (err) {
    console.error('Error fetching profile statistics:', err);
    res.status(500).json({ message: 'Error fetching profile statistics', error: err.message });
  }
});

module.exports = router;
