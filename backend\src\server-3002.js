const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const mongoose = require('mongoose');

// Load models
require('./models/funding-programme');
require('./models/corporate-sponsor');

// Load environment variables
dotenv.config({ path: require('path').resolve(__dirname, '../.env') });

const app = express();
const PORT = 3002; // Changed back to 3002 to match environment.ts
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/funding-app';

// Debug logging
console.log('Environment variables:');
console.log('MONGODB_URI:', process.env.MONGODB_URI);

// Middleware
app.use(cors());
app.use(express.json());

async function initializeServer() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
    
    // Seed questionnaire data
    const seedQuestionnaireData = require('./seed-questionnaire-data');
    await seedQuestionnaireData();
    
    // Also seed in-memory questionnaire data
    const { seedQuestionnaireData: seedInMemoryQuestionnaireData } = require('./in-memory-questionnaire-store');
    await seedInMemoryQuestionnaireData();
    
    // Import routes
    const applicationRoutes = require('./routes/applications');
    const siteVisitRoutes = require('./routes/site-visits');
    const interviewRoutes = require('./routes/interviews');
    const schedulerRoutes = require('./routes/scheduler');
    const questionnaireRoutes = require('../../questionnaire-routes');
    
    // Initialize routes
    app.use('/api/applications', applicationRoutes);
    app.use('/api/site-visits', siteVisitRoutes);
    app.use('/api/interviews', interviewRoutes);
    app.use('/api/scheduler', schedulerRoutes);
    app.use('/api/questionnaires', questionnaireRoutes);
    
    // Error handling middleware
    app.use((err, req, res, next) => {
      console.error('API Error:', err);
      res.status(500).json({ 
        error: 'Server error', 
        message: err.message 
      });
    });
    
    // Start the server
    const server = app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
    
    // Handle server errors
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Please use a different port.`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
        process.exit(1);
      }
    });
  } catch (err) {
    console.error('Server initialization failed:', err);
    process.exit(1);
  }
}

// Start the server
initializeServer();
