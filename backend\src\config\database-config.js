/**
 * Database Configuration Module
 *
 * This module provides configuration for the persistent MongoDB database.
 * It handles connection to the database and configuration management.
 */

const mongoose = require('mongoose');
const debug = require('debug')('database:config');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env in the backend directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Default URI if MONGODB_URI is not in .env
const DEFAULT_URI = 'mongodb://localhost:27017/funding-screening-app';

// Determine the MongoDB URI to use
// Prioritize MONGODB_URI from .env, otherwise use the default.
const MONGODB_URI = process.env.MONGODB_URI || DEFAULT_URI;

/**
 * Get the database connection status
 * @returns {string} The current status ('connected' or 'disconnected')
 */
function getConnectionStatus() {
  return mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
}

/**
 * Connect to the MongoDB database
 * @returns {Promise<Object>} Connection result with status and info
 */
async function connectToDatabase() {
  // Close any existing connections
  if (mongoose.connection.readyState !== 0) {
    await mongoose.disconnect();
    console.log('Closed existing database connection');
  }

  console.log(`Attempting to connect to MongoDB with URI: ${MONGODB_URI}`);
  
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Keep or adjust as needed
    });
    console.log('Successfully connected to MongoDB.');
    debug('MongoDB connection successful with URI:', MONGODB_URI);
    return { success: true, uri: MONGODB_URI, status: getConnectionStatus() };
  } catch (error) {
    console.error('Error connecting to MongoDB:', error.message);
    debug('MongoDB connection error with URI:', MONGODB_URI, error);
    return { success: false, error: error.message, uri: MONGODB_URI, status: getConnectionStatus() };
  }
}

/**
 * Disconnect from the MongoDB database
 * @returns {Promise<void>}
 */
async function disconnectFromDatabase() {
  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('Successfully disconnected from MongoDB.');
      debug('Disconnected from MongoDB');
    } else {
      console.log('No active MongoDB connection to disconnect.');
      debug('No active MongoDB connection to disconnect.');
    }
  } catch (error) {
    console.error('Error disconnecting from MongoDB:', error.message);
    debug('Error disconnecting from MongoDB:', error);
  }
}

// Export the MONGODB_URI being used, along with connection functions
module.exports = {
  connectToDatabase,
  disconnectFromDatabase,
  getConnectionStatus,
  MONGODB_URI // Exporting for transparency or direct use if needed elsewhere
};
