const express = require('express');
const { check, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const NotificationService = require('../services/notification-service');
const UserNotification = require('../models/user-notification');
const Notification = require('../models/notification');

const {
  authenticateToken,
  canViewNotifications
} = require('../middleware/notification-auth');

const router = express.Router();

// Rate limiting for user notification endpoints
const userNotificationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // limit each IP to 500 requests per windowMs
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many notification requests, please try again later.'
    }
  }
});

router.use(userNotificationLimiter);

// GET /api/v1/notifications - Get user's notifications
router.get('/',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 50,
        isRead,
        status,
        type,
        category,
        priority,
        dateFrom,
        dateTo
      } = req.query;

      const filters = {};
      
      // Apply filters
      if (isRead !== undefined) filters.isRead = isRead === 'true';
      if (status) filters.status = status;
      
      if (dateFrom || dateTo) {
        filters.dateFrom = dateFrom;
        filters.dateTo = dateTo;
      }

      const notifications = await NotificationService.getUserNotifications(
        req.user.sub,
        filters,
        parseInt(limit),
        (parseInt(page) - 1) * parseInt(limit)
      );

      // Get total count for pagination
      const totalQuery = { userId: req.user.sub };
      if (filters.isRead !== undefined) totalQuery.isRead = filters.isRead;
      if (filters.status) totalQuery.status = filters.status;
      if (filters.dateFrom || filters.dateTo) {
        totalQuery.createdAt = {};
        if (filters.dateFrom) totalQuery.createdAt.$gte = new Date(filters.dateFrom);
        if (filters.dateTo) totalQuery.createdAt.$lte = new Date(filters.dateTo);
      }

      const total = await UserNotification.countDocuments(totalQuery);

      // Filter by type, category, priority if specified (these are on the notification object)
      let filteredNotifications = notifications;
      if (type || category || priority) {
        filteredNotifications = notifications.filter(notification => {
          const notif = notification.notificationId;
          if (type && notif.type !== type) return false;
          if (category && notif.category !== category) return false;
          if (priority && notif.priority !== priority) return false;
          return true;
        });
      }

      res.json({
        notifications: filteredNotifications.map(userNotification => ({
          id: userNotification._id,
          notification: {
            id: userNotification.notificationId._id,
            title: userNotification.notificationId.title,
            message: userNotification.notificationId.message,
            type: userNotification.notificationId.type,
            category: userNotification.notificationId.category,
            priority: userNotification.notificationId.priority,
            actions: userNotification.notificationId.actions,
            relatedEntity: userNotification.notificationId.relatedEntity
          },
          isRead: userNotification.isRead,
          readAt: userNotification.readAt,
          isClicked: userNotification.isClicked,
          clickedAt: userNotification.clickedAt,
          isDismissed: userNotification.isDismissed,
          dismissedAt: userNotification.dismissedAt,
          status: userNotification.status,
          deliveryStatus: userNotification.deliveryStatus,
          createdAt: userNotification.createdAt,
          expiresAt: userNotification.expiresAt
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Error getting user notifications:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notifications'
        }
      });
    }
  }
);

// GET /api/v1/notifications/unread - Get user's unread notifications
router.get('/unread',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const { limit = 50 } = req.query;
      
      const notifications = await NotificationService.getUnreadNotifications(
        req.user.sub,
        parseInt(limit)
      );

      // Get unread count
      const unreadCount = await UserNotification.countDocuments({
        userId: req.user.sub,
        isRead: false,
        status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
      });

      res.json({
        notifications: notifications.map(userNotification => ({
          id: userNotification._id,
          notification: {
            id: userNotification.notificationId._id,
            title: userNotification.notificationId.title,
            message: userNotification.notificationId.message,
            type: userNotification.notificationId.type,
            category: userNotification.notificationId.category,
            priority: userNotification.notificationId.priority,
            actions: userNotification.notificationId.actions,
            relatedEntity: userNotification.notificationId.relatedEntity
          },
          status: userNotification.status,
          createdAt: userNotification.createdAt,
          expiresAt: userNotification.expiresAt
        })),
        unreadCount
      });
    } catch (error) {
      console.error('Error getting unread notifications:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get unread notifications'
        }
      });
    }
  }
);

// GET /api/v1/notifications/count - Get notification counts
router.get('/count',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const userId = req.user.sub;
      
      const [
        totalCount,
        unreadCount,
        todayCount,
        urgentCount
      ] = await Promise.all([
        UserNotification.countDocuments({
          userId,
          status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
        }),
        UserNotification.countDocuments({
          userId,
          isRead: false,
          status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
        }),
        UserNotification.countDocuments({
          userId,
          createdAt: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0))
          },
          status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
        }),
        UserNotification.aggregate([
          {
            $match: {
              userId: req.user.sub,
              isRead: false,
              status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
            }
          },
          {
            $lookup: {
              from: 'notifications',
              localField: 'notificationId',
              foreignField: '_id',
              as: 'notification'
            }
          },
          {
            $unwind: '$notification'
          },
          {
            $match: {
              'notification.priority': 'URGENT'
            }
          },
          {
            $count: 'urgentCount'
          }
        ])
      ]);

      res.json({
        counts: {
          total: totalCount,
          unread: unreadCount,
          today: todayCount,
          urgent: urgentCount.length > 0 ? urgentCount[0].urgentCount : 0
        }
      });
    } catch (error) {
      console.error('Error getting notification counts:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notification counts'
        }
      });
    }
  }
);

// PUT /api/v1/notifications/:id/read - Mark notification as read
router.put('/:id/read',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const userNotification = await NotificationService.markAsRead(
        req.user.sub,
        req.params.id
      );

      res.json({
        message: 'Notification marked as read',
        notification: {
          id: userNotification._id,
          isRead: userNotification.isRead,
          readAt: userNotification.readAt
        }
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to mark notification as read'
        }
      });
    }
  }
);

// PUT /api/v1/notifications/read-multiple - Mark multiple notifications as read
router.put('/read-multiple',
  authenticateToken,
  canViewNotifications,
  [
    check('notificationIds', 'Notification IDs array is required').isArray({ min: 1 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation error',
            details: errors.array()
          }
        });
      }

      const { notificationIds } = req.body;
      
      const result = await NotificationService.markMultipleAsRead(
        req.user.sub,
        notificationIds
      );

      res.json({
        message: 'Notifications marked as read',
        result: {
          modifiedCount: result.modifiedCount,
          matchedCount: result.matchedCount
        }
      });
    } catch (error) {
      console.error('Error marking multiple notifications as read:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to mark notifications as read'
        }
      });
    }
  }
);

// PUT /api/v1/notifications/read-all - Mark all notifications as read
router.put('/read-all',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const result = await UserNotification.updateMany(
        {
          userId: req.user.sub,
          isRead: false,
          status: { $in: ['DELIVERED', 'PARTIALLY_DELIVERED'] }
        },
        {
          $set: {
            isRead: true,
            readAt: new Date(),
            updatedAt: new Date()
          }
        }
      );

      res.json({
        message: 'All notifications marked as read',
        result: {
          modifiedCount: result.modifiedCount,
          matchedCount: result.matchedCount
        }
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to mark all notifications as read'
        }
      });
    }
  }
);

// POST /api/v1/notifications/:id/click - Handle notification action click
router.post('/:id/click',
  authenticateToken,
  canViewNotifications,
  [
    check('actionLabel', 'Action label is required').notEmpty()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation error',
            details: errors.array()
          }
        });
      }

      const { actionLabel } = req.body;
      
      const userNotification = await NotificationService.handleActionClick(
        req.user.sub,
        req.params.id,
        actionLabel
      );

      res.json({
        message: 'Notification action clicked',
        notification: {
          id: userNotification._id,
          isClicked: userNotification.isClicked,
          clickedAt: userNotification.clickedAt,
          clickedAction: userNotification.clickedAction
        }
      });
    } catch (error) {
      console.error('Error handling notification click:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: error.message || 'Failed to handle notification click'
        }
      });
    }
  }
);

// PUT /api/v1/notifications/:id/dismiss - Dismiss notification
router.put('/:id/dismiss',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const userNotification = await UserNotification.findOne({
        _id: req.params.id,
        userId: req.user.sub
      });

      if (!userNotification) {
        return res.status(404).json({
          error: {
            code: 'NOTIFICATION_NOT_FOUND',
            message: 'Notification not found'
          }
        });
      }

      await userNotification.dismiss();

      res.json({
        message: 'Notification dismissed',
        notification: {
          id: userNotification._id,
          isDismissed: userNotification.isDismissed,
          dismissedAt: userNotification.dismissedAt
        }
      });
    } catch (error) {
      console.error('Error dismissing notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to dismiss notification'
        }
      });
    }
  }
);

// GET /api/v1/notifications/:id - Get specific notification
router.get('/:id',
  authenticateToken,
  canViewNotifications,
  async (req, res) => {
    try {
      const userNotification = await UserNotification.findOne({
        _id: req.params.id,
        userId: req.user.sub
      }).populate('notificationId');

      if (!userNotification) {
        return res.status(404).json({
          error: {
            code: 'NOTIFICATION_NOT_FOUND',
            message: 'Notification not found'
          }
        });
      }

      res.json({
        notification: {
          id: userNotification._id,
          notification: {
            id: userNotification.notificationId._id,
            title: userNotification.notificationId.title,
            message: userNotification.notificationId.message,
            type: userNotification.notificationId.type,
            category: userNotification.notificationId.category,
            priority: userNotification.notificationId.priority,
            actions: userNotification.notificationId.actions,
            relatedEntity: userNotification.notificationId.relatedEntity
          },
          isRead: userNotification.isRead,
          readAt: userNotification.readAt,
          isClicked: userNotification.isClicked,
          clickedAt: userNotification.clickedAt,
          clickedAction: userNotification.clickedAction,
          isDismissed: userNotification.isDismissed,
          dismissedAt: userNotification.dismissedAt,
          status: userNotification.status,
          deliveryStatus: userNotification.deliveryStatus,
          createdAt: userNotification.createdAt,
          expiresAt: userNotification.expiresAt
        }
      });
    } catch (error) {
      console.error('Error getting notification:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notification'
        }
      });
    }
  }
);

// GET /api/v1/notifications/preferences/current - Get user's notification preferences
router.get('/preferences/current',
  authenticateToken,
  async (req, res) => {
    try {
      const User = require('../models/user');
      const user = await User.findById(req.user.sub);
      
      if (!user) {
        return res.status(404).json({
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        });
      }

      // Default preferences if not set
      const preferences = user.notificationPreferences || {
        inApp: true,
        email: false,
        sms: false,
        push: false,
        categories: {
          APPLICATION: true,
          INTERVIEW: true,
          SITE_VISIT: true,
          DOCUMENT: true,
          APPROVAL: true,
          SYSTEM: true,
          MEETING: true,
          REPORT: false,
          SCORECARD: true,
          GENERAL: true
        },
        priorities: {
          LOW: true,
          MEDIUM: true,
          HIGH: true,
          URGENT: true
        }
      };

      res.json({
        preferences
      });
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to get notification preferences'
        }
      });
    }
  }
);

// PUT /api/v1/notifications/preferences - Update user's notification preferences
router.put('/preferences',
  authenticateToken,
  async (req, res) => {
    try {
      const { preferences } = req.body;
      
      if (!preferences || typeof preferences !== 'object') {
        return res.status(400).json({
          error: {
            code: 'INVALID_PREFERENCES',
            message: 'Valid preferences object is required'
          }
        });
      }

      const User = require('../models/user');
      const user = await User.findById(req.user.sub);
      
      if (!user) {
        return res.status(404).json({
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        });
      }

      // Update user's notification preferences
      user.notificationPreferences = {
        ...user.notificationPreferences,
        ...preferences
      };
      
      await user.save();

      res.json({
        message: 'Notification preferences updated successfully',
        preferences: user.notificationPreferences
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      res.status(500).json({
        error: {
          code: 'SERVER_ERROR',
          message: 'Failed to update notification preferences'
        }
      });
    }
  }
);

module.exports = router;