const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Schema for individual questions within a questionnaire section
 */
const questionSchema = new Schema({
  id: { type: String, required: true },
  text: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['OPEN_ENDED', 'MULTIPLE_CHOICE', 'YES_NO', 'RATING', 'TEXT'], 
    default: 'OPEN_ENDED' 
  },
  required: { type: Boolean, default: false },
  options: [String], // For multiple choice questions
  minRating: Number, // For rating questions
  maxRating: Number, // For rating questions
  helpText: String // Additional guidance for answering the question
});

/**
 * Schema for sections within a questionnaire template
 */
const sectionSchema = new Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String },
  order: { type: Number, required: true },
  questions: [questionSchema],
  conditionalLogic: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }, // For conditional display logic
  isRequired: { type: Boolean, default: false }
});

/**
 * Schema for questionnaire templates
 * Templates define the structure of questionnaires that can be used in interviews
 */
const questionnaireTemplateSchema = new Schema({
  name: { type: String, required: true },
  description: { type: String },
  version: {
    type: String,
    match: /^\d+\.\d+\.\d+$/,
    required: true
  },
  isActive: { type: Boolean, default: true },
  createdBy: { type: String, required: true },
  applicableIndustries: [String], // e.g., "Manufacturing", "Technology", "Agriculture"
  applicableFundingTypes: [String], // e.g., "Loan", "Grant", "Equity"
  applicableLifecycleStages: [String], // e.g., "Startup", "Growth", "Mature"
  sections: [sectionSchema],
  previousVersions: [{
    version: String,
    content: Schema.Types.Mixed,
    archivedAt: Date
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for efficient querying
questionnaireTemplateSchema.index({ name: 1, version: 1 }, { unique: true });
questionnaireTemplateSchema.index({ isActive: 1 });
questionnaireTemplateSchema.index({ applicableIndustries: 1 });
questionnaireTemplateSchema.index({ applicableFundingTypes: 1 });
questionnaireTemplateSchema.index({ applicableLifecycleStages: 1 });

/**
 * Schema for questionnaire responses
 * This stores the actual responses to a questionnaire template
 */
const questionnaireResponseSchema = new Schema({
  interviewId: { 
    type: String, 
    ref: 'Interview',
    required: true
  },
  templateId: { 
    type: Schema.Types.ObjectId, 
    ref: 'QuestionnaireTemplate',
    required: true
  },
  templateVersion: { 
    type: String,
    required: true
  },
  responses: {
    type: Map,
    of: Schema.Types.Mixed
  },
  status: {
    type: String,
    enum: ['draft', 'submitted', 'under_review', 'approved'],
    default: 'draft'
  },
  submittedBy: String,
  submittedAt: Date,
  reviewedBy: String,
  reviewedAt: Date,
  comments: [{
    text: String,
    createdBy: String,
    createdAt: { type: Date, default: Date.now }
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for efficient querying
questionnaireResponseSchema.index({ interviewId: 1 });
questionnaireResponseSchema.index({ templateId: 1 });
questionnaireResponseSchema.index({ status: 1 });

// Create models from the schemas
const QuestionnaireTemplate = mongoose.model('QuestionnaireTemplate', questionnaireTemplateSchema);
const QuestionnaireResponse = mongoose.model('QuestionnaireResponse', questionnaireResponseSchema);

module.exports = {
  QuestionnaireTemplate,
  QuestionnaireResponse
};
