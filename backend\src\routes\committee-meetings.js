const express = require('express');
const router = express.Router();
// Create a simple UUID generator function instead of using the uuid module
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
// Simple validation function to replace express-validator
function validateRequest(req, rules) {
  const errors = [];
  
  for (const field in rules) {
    if (rules[field].required && (!req.body[field] || req.body[field].trim() === '')) {
      errors.push({ field, message: `${field} is required` });
    }
  }
  
  return {
    isEmpty: () => errors.length === 0,
    array: () => errors
  };
}
const { authenticateToken, authorizeRole } = require('../middleware/auth');
const { getDatabase } = require('../database');
const { sendEmail } = require('../services/email-service');
const { createCalendarEvent } = require('../services/calendar-service');
const logger = require('../logger');

// Mock data for development
let committeeMeetings = [];

// Initialize with some mock data
const initializeMockData = () => {
  if (committeeMeetings.length === 0) {
    // Mock data will be loaded from the frontend service for now
    logger.info('Committee meetings mock data initialized');
  }
};

// Initialize mock data
initializeMockData();

/**
 * @route GET /api/committee-meetings
 * @desc Get all committee meetings with optional filtering
 * @access Private
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      corporateSponsorId,
      approvalLevel,
      status,
      fromDate,
      toDate,
      programmeId
    } = req.query;

    // Apply filters
    let filteredMeetings = [...committeeMeetings];

    if (corporateSponsorId && corporateSponsorId !== 'all') {
      filteredMeetings = filteredMeetings.filter(m => m.corporateSponsorId === corporateSponsorId);
    }

    if (approvalLevel && approvalLevel !== 'all') {
      filteredMeetings = filteredMeetings.filter(m => m.approvalLevel === Number(approvalLevel));
    }

    if (status && status !== 'all') {
      filteredMeetings = filteredMeetings.filter(m => m.status === status);
    }

    if (fromDate) {
      const fromDateObj = new Date(fromDate);
      filteredMeetings = filteredMeetings.filter(m => new Date(m.date) >= fromDateObj);
    }

    if (toDate) {
      const toDateObj = new Date(toDate);
      filteredMeetings = filteredMeetings.filter(m => new Date(m.date) <= toDateObj);
    }

    if (programmeId) {
      filteredMeetings = filteredMeetings.filter(m => m.programmeId === programmeId);
    }

    // Apply access control based on user role and organization
    const user = req.user;
    if (user.role !== 'admin') {
      if (user.organizationType === 'CorporateSponsor' && user.organizationId) {
        filteredMeetings = filteredMeetings.filter(m => m.corporateSponsorId === user.organizationId);
      } else if (user.organizationType === 'ServiceProvider' && user.programmeAssignments) {
        const assignedProgrammeIds = user.programmeAssignments.map(a => a.programmeId);
        filteredMeetings = filteredMeetings.filter(m => assignedProgrammeIds.includes(m.programmeId));
      }
    }

    res.json(filteredMeetings);
  } catch (error) {
    logger.error('Error getting committee meetings:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/committee-meetings/:id
 * @desc Get a committee meeting by ID
 * @access Private
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const meeting = committeeMeetings.find(m => m.id === req.params.id);

    if (!meeting) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    // Check if user has access to this meeting
    const user = req.user;
    if (user.role !== 'admin') {
      if (user.organizationType === 'CorporateSponsor' && user.organizationId !== meeting.corporateSponsorId) {
        return res.status(403).json({ message: 'Access denied' });
      } else if (user.organizationType === 'ServiceProvider' && user.programmeAssignments) {
        const assignedProgrammeIds = user.programmeAssignments.map(a => a.programmeId);
        if (!assignedProgrammeIds.includes(meeting.programmeId)) {
          return res.status(403).json({ message: 'Access denied' });
        }
      }
    }

    res.json(meeting);
  } catch (error) {
    logger.error('Error getting committee meeting:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/committee-meetings
 * @desc Create a new committee meeting
 * @access Private
 */
router.post('/', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Validate request
  const validationRules = {
    title: { required: true },
    date: { required: true },
    startTime: { required: true },
    endTime: { required: true },
    location: { required: true },
    corporateSponsorId: { required: true },
    approvalLevel: { required: true },
    status: { required: true }
  };
  
  const errors = validateRequest(req, validationRules);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const newMeeting = {
      id: generateUUID(),
      ...req.body,
      createdBy: req.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      notificationsSent: false,
      remindersSent: false,
      quorumMet: false
    };

    // Add to collection
    committeeMeetings.push(newMeeting);

    logger.info(`Committee meeting created: ${newMeeting.id}`);
    res.status(201).json(newMeeting);
  } catch (error) {
    logger.error('Error creating committee meeting:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/committee-meetings/:id
 * @desc Update a committee meeting
 * @access Private
 */
router.put('/:id', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    // Check if user has permission to update this meeting
    const user = req.user;
    const meeting = committeeMeetings[meetingIndex];
    
    if (user.role !== 'admin' && meeting.createdBy !== user.id) {
      if (user.organizationType === 'CorporateSponsor' && user.organizationId !== meeting.corporateSponsorId) {
        return res.status(403).json({ message: 'Access denied' });
      }
    }

    // Update meeting
    const updatedMeeting = {
      ...meeting,
      ...req.body,
      updatedAt: new Date()
    };

    committeeMeetings[meetingIndex] = updatedMeeting;

    logger.info(`Committee meeting updated: ${updatedMeeting.id}`);
    res.json(updatedMeeting);
  } catch (error) {
    logger.error('Error updating committee meeting:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route DELETE /api/committee-meetings/:id
 * @desc Delete a committee meeting
 * @access Private
 */
router.delete('/:id', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    // Check if user has permission to delete this meeting
    const user = req.user;
    const meeting = committeeMeetings[meetingIndex];
    
    if (user.role !== 'admin' && meeting.createdBy !== user.id) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove meeting
    committeeMeetings.splice(meetingIndex, 1);

    logger.info(`Committee meeting deleted: ${req.params.id}`);
    res.json({ message: 'Committee meeting deleted successfully' });
  } catch (error) {
    logger.error('Error deleting committee meeting:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/committee-meetings/:id/applications
 * @desc Add an application to a committee meeting
 * @access Private
 */
router.post('/:id/applications', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  
  // Validate request
  const validationRules = {
    applicationId: { required: true }
  };
  
  const errors = validateRequest(req, validationRules);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];
    const { applicationId, presentationTime } = req.body;

    // Check if application is already in the meeting
    if (meeting.applications && meeting.applications.some(a => a.applicationId === applicationId)) {
      return res.status(400).json({ message: 'Application is already in this meeting' });
    }

    // Add application to meeting
    if (!meeting.applications) {
      meeting.applications = [];
    }

    meeting.applications.push({
      applicationId,
      status: 'scheduled',
      presentationTime,
      fundingAmount: req.body.fundingAmount || 0,
      presenter: req.body.presenter || null,
      notes: req.body.notes || null
    });

    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    logger.info(`Application ${applicationId} added to meeting ${meeting.id}`);
    res.json(meeting);
  } catch (error) {
    logger.error('Error adding application to meeting:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/committee-meetings/:id/applications/:applicationId
 * @desc Update application status in a committee meeting
 * @access Private
 */
router.put('/:id/applications/:applicationId', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if IDs are provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  
  if (!req.params.applicationId) {
    return res.status(400).json({ errors: [{ message: 'Application ID is required' }] });
  }
  
  // Validate request
  const validationRules = {
    status: { required: true }
  };
  
  const errors = validateRequest(req, validationRules);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];
    const { applicationId } = req.params;
    const { status, notes } = req.body;

    // Find application in meeting
    if (!meeting.applications) {
      return res.status(404).json({ message: 'Application not found in meeting' });
    }

    const appIndex = meeting.applications.findIndex(a => a.applicationId === applicationId);
    if (appIndex === -1) {
      return res.status(404).json({ message: 'Application not found in meeting' });
    }

    // Update application status
    meeting.applications[appIndex] = {
      ...meeting.applications[appIndex],
      status,
      notes: notes || meeting.applications[appIndex].notes,
      decisionDate: status === 'approved' || status === 'rejected' || status === 'deferred' || status === 'escalated' ? new Date() : meeting.applications[appIndex].decisionDate,
      decisionMaker: status === 'approved' || status === 'rejected' || status === 'deferred' || status === 'escalated' ? req.user.id : meeting.applications[appIndex].decisionMaker
    };

    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    logger.info(`Application ${applicationId} status updated to ${status} in meeting ${meeting.id}`);
    res.json(meeting);
  } catch (error) {
    logger.error('Error updating application status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route PUT /api/committee-meetings/:id/minutes
 * @desc Update meeting minutes
 * @access Private
 */
router.put('/:id/minutes', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  
  // Validate request
  const validationRules = {
    minutes: { required: true }
  };
  
  const errors = validateRequest(req, validationRules);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];
    const { minutes } = req.body;

    // Update minutes
    meeting.minutes = minutes;
    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    logger.info(`Minutes updated for meeting ${meeting.id}`);
    res.json(meeting);
  } catch (error) {
    logger.error('Error updating meeting minutes:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/committee-meetings/:id/notifications
 * @desc Send meeting notifications
 * @access Private
 */
router.post('/:id/notifications', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];

    // Check if notifications have already been sent
    if (meeting.notificationsSent) {
      return res.status(400).json({ message: 'Notifications have already been sent for this meeting' });
    }

    // In a real implementation, this would send emails to attendees
    // For now, we'll just update the meeting status
    meeting.notificationsSent = true;
    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    // Mock sending notifications
    if (meeting.attendees && meeting.attendees.length > 0) {
      logger.info(`Sending notifications to ${meeting.attendees.length} attendees for meeting ${meeting.id}`);
      
      // In a real implementation, this would use the email service
      // sendEmail(to, subject, body);
    }

    logger.info(`Notifications sent for meeting ${meeting.id}`);
    res.json({ success: true, meeting });
  } catch (error) {
    logger.error('Error sending meeting notifications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/committee-meetings/:id/reminders
 * @desc Send meeting reminders
 * @access Private
 */
router.post('/:id/reminders', [
  authenticateToken,
  authorizeRole(['admin', 'manager'])
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];

    // Check if notifications have been sent
    if (!meeting.notificationsSent) {
      return res.status(400).json({ message: 'Initial notifications must be sent before reminders' });
    }

    // Check if reminders have already been sent
    if (meeting.remindersSent) {
      return res.status(400).json({ message: 'Reminders have already been sent for this meeting' });
    }

    // In a real implementation, this would send emails to attendees
    // For now, we'll just update the meeting status
    meeting.remindersSent = true;
    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    // Mock sending reminders
    if (meeting.attendees && meeting.attendees.length > 0) {
      logger.info(`Sending reminders to ${meeting.attendees.length} attendees for meeting ${meeting.id}`);
      
      // In a real implementation, this would use the email service
      // sendEmail(to, subject, body);
    }

    logger.info(`Reminders sent for meeting ${meeting.id}`);
    res.json({ success: true, meeting });
  } catch (error) {
    logger.error('Error sending meeting reminders:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route POST /api/committee-meetings/:id/calendar
 * @desc Export meeting to calendar
 * @access Private
 */
router.post('/:id/calendar', [
  authenticateToken
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];

    // In a real implementation, this would create a calendar event
    // For now, we'll just generate a mock calendar event ID
    const calendarEventId = `cal-${meeting.id}-${Date.now()}`;
    meeting.calendarEventId = calendarEventId;
    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    // Mock creating calendar event
    logger.info(`Calendar event created for meeting ${meeting.id}: ${calendarEventId}`);
    
    // In a real implementation, this would use the calendar service
    // createCalendarEvent(meeting);

    res.json({ success: true, calendarEventId, meeting });
  } catch (error) {
    logger.error('Error exporting meeting to calendar:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/committee-meetings/:id/applications/eligible
 * @desc Get applications eligible for a meeting
 * @access Private
 */
router.get('/:id/applications/eligible', [
  authenticateToken
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];

    // In a real implementation, this would query the applications collection
    // For now, we'll return mock data
    const eligibleApplications = [
      { id: '101', businessName: 'Tech Innovators', fundingAmount: 450000, status: 'UNDER_REVIEW' },
      { id: '102', businessName: 'Green Solutions', fundingAmount: 750000, status: 'UNDER_REVIEW' },
      { id: '103', businessName: 'Health Connect', fundingAmount: 350000, status: 'UNDER_REVIEW' },
      { id: '104', businessName: 'Education First', fundingAmount: 550000, status: 'UNDER_REVIEW' },
      { id: '105', businessName: 'Manufacturing Plus', fundingAmount: 1200000, status: 'UNDER_REVIEW' }
    ];

    // Filter out applications already in the meeting
    const existingApplicationIds = meeting.applications ? meeting.applications.map(a => a.applicationId) : [];
    const filteredApplications = eligibleApplications.filter(app => !existingApplicationIds.includes(app.id));

    res.json(filteredApplications);
  } catch (error) {
    logger.error('Error getting eligible applications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route GET /api/committee-meetings/:id/quorum
 * @desc Check if a meeting has quorum
 * @access Private
 */
router.get('/:id/quorum', [
  authenticateToken
], async (req, res) => {
  // Check if ID is provided
  if (!req.params.id) {
    return res.status(400).json({ errors: [{ message: 'Meeting ID is required' }] });
  }
  try {
    const meetingIndex = committeeMeetings.findIndex(m => m.id === req.params.id);

    if (meetingIndex === -1) {
      return res.status(404).json({ message: 'Committee meeting not found' });
    }

    const meeting = committeeMeetings[meetingIndex];

    // Check if meeting has attendees
    if (!meeting.attendees || meeting.attendees.length === 0) {
      return res.json({ quorumMet: false });
    }

    // In a real implementation, this would check against required roles
    // For now, we'll use a simple check
    const confirmedAttendees = meeting.attendees.filter(a => a.confirmed);
    const hasChair = confirmedAttendees.some(a => a.role === 'Chair');
    const hasSecretary = confirmedAttendees.some(a => a.role === 'Secretary');
    const hasMember = confirmedAttendees.some(a => a.role === 'Member');
    
    const quorumMet = hasChair && hasSecretary && hasMember;
    
    // Update meeting with quorum status
    meeting.quorumMet = quorumMet;
    meeting.updatedAt = new Date();
    committeeMeetings[meetingIndex] = meeting;

    res.json({ quorumMet });
  } catch (error) {
    logger.error('Error checking meeting quorum:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
