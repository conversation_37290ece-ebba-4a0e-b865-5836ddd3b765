const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const Application = require('./models/application');
const CorporateSponsor = require('./models/corporate-sponsor');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });
console.log('Using MongoDB URI:', process.env.MONGODB_URI);

// Sample application data - 20 entries with enhanced details
const sampleApplications = [];

// Define industries for variety
const industries = [
  'Technology', 'Healthcare', 'Education', 'Food & Beverage', 'Construction',
  'Retail', 'Agriculture', 'Transportation', 'Tourism', 'Manufacturing',
  'Renewable Energy', 'Logistics', 'Hospitality', 'IT Services', 'Pharmaceuticals',
  'Textiles', 'Waste Management', 'Telecommunications', 'Financial Services', 'Media'
];

// Define statuses and stages
const statuses = ['pending', 'in-review', 'approved', 'rejected'];
const mainStages = [
  'ONBOARDING', 'PRE_SCREENING_PENDING', 'DOCUMENT_COLLECTION', 
  'DESKTOP_ANALYSIS', 'DATA_VALIDATION', 'SME_INTERVIEW_SCHEDULED', 
  'SME_INTERVIEW_COMPLETED', 'FINAL_REVIEW', 
  'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'
];

// Define a special mapping for the site visit stage
// Since SITE_VISIT is not a valid enum value for the type field, we'll use SME_INTERVIEW_COMPLETED with substage SITE_VISIT
const SITE_VISIT_STAGE = 'SME_INTERVIEW_COMPLETED';

// Define substages
const substages = {
  'ONBOARDING': ['BENEFICIARY_REGISTRATION', 'PRE_SCREENING'],
  'PRE_SCREENING_PENDING': ['PRE_SCREENING'],
  'DOCUMENT_COLLECTION': ['DOCUMENT_COLLECTION'],
  'DESKTOP_ANALYSIS': ['DESKTOP_ANALYSIS'],
  'DATA_VALIDATION': ['DATA_VALIDATION'],
  'SME_INTERVIEW_SCHEDULED': ['SME_INTERVIEW'],
  'SME_INTERVIEW_COMPLETED': ['SME_INTERVIEW'],
  'SITE_VISIT': ['SITE_VISIT'],
  'FINAL_REVIEW': ['REPORT_COMPLETION', 'REPORT_QUALITY_CHECK', 'REPORT_REVIEW'],
  'APPLICATION_APPROVAL': ['COMMITTEE_REVIEW', 'FINAL_DECISION', 'APPROVAL_DOCUMENTATION', 
                          'CORPORATE_APPROVAL_1', 'CORPORATE_APPROVAL_2', 
                          'CORPORATE_APPROVAL_3', 'CORPORATE_APPROVAL_4']
};

// Define assignees
const assignees = ['Sarah Johnson', 'Michael Chen', 'David Moyo', 'Priya Patel', 'Thabo Molefe'];

// Generate 20 sample applications
for (let i = 0; i < 20; i++) {
  // Select random status
  const status = statuses[Math.floor(Math.random() * statuses.length)];
  
  // Select random stage based on status
  let stageOptions;
  if (status === 'pending') {
    stageOptions = mainStages.slice(0, 2); // ONBOARDING, PRE_SCREENING_PENDING
  } else if (status === 'in-review') {
    stageOptions = mainStages.slice(2, 9); // DOCUMENT_COLLECTION to FINAL_REVIEW
  } else if (status === 'approved') {
    stageOptions = ['APPROVED'];
  } else {
    stageOptions = ['REJECTED'];
  }
  
  const currentStage = stageOptions[Math.floor(Math.random() * stageOptions.length)];
  
  // Select random substage based on current stage
  const substageOptions = substages[currentStage] || ['BENEFICIARY_REGISTRATION'];
  const substage = substageOptions[Math.floor(Math.random() * substageOptions.length)];
  
  // Generate random funding amount between 200,000 and 1,000,000
  const fundingAmount = Math.floor(Math.random() * 800000) + 200000;
  
  // Generate random score between 50 and 95
  const score = Math.floor(Math.random() * 46) + 50;
  
  // Generate business name
  const industry = industries[i % industries.length];
  const businessName = `${industry} Solutions ${i + 1}`;
  
  // Generate dates
  const submissionDate = new Date(2025, 0, 15 + i);
  const lastUpdated = new Date(2025, 0, 20 + i);
  
  // Create stages array with evaluations and scoring
  const stages = [];
  
  // Add ONBOARDING stage for all applications
  stages.push({
    type: 'ONBOARDING',
    status: currentStage === 'ONBOARDING' ? 'IN_PROGRESS' : 'COMPLETED',
    startedAt: new Date(submissionDate).toISOString(),
    completedAt: currentStage === 'ONBOARDING' ? null : new Date(submissionDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    notes: {
      strengths: ['Complete application form', 'Clear business description'],
      weaknesses: ['Limited financial history', 'New business entity'],
      opportunities: ['Growing market segment', 'Innovative business model'],
      threats: ['Competitive market', 'Regulatory challenges'],
      generalNotes: ['Initial application review completed', 'Applicant meets basic criteria']
    },
    requiredScore: 70,
    score: currentStage === 'ONBOARDING' ? null : Math.floor(Math.random() * 20) + 70,
    assignedTo: assignees[i % assignees.length]
  });
  
  // Add additional stages based on current stage
  if (currentStage !== 'ONBOARDING') {
    stages.push({
      type: 'PRE_SCREENING_PENDING',
      status: currentStage === 'PRE_SCREENING_PENDING' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'PRE_SCREENING_PENDING' ? null : new Date(submissionDate.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Meets funding criteria', 'Strong business concept'],
        weaknesses: ['Limited track record', 'Funding amount concerns'],
        opportunities: ['Potential for job creation', 'Export opportunities'],
        threats: ['Market volatility', 'Supply chain risks'],
        generalNotes: ['Pre-screening assessment completed', 'Recommended for document collection']
      },
      requiredScore: 60,
      score: currentStage === 'PRE_SCREENING_PENDING' ? null : Math.floor(Math.random() * 20) + 65,
      assignedTo: assignees[(i + 1) % assignees.length]
    });
  }
  
  // Add document collection stage if applicable
  if (['DOCUMENT_COLLECTION', 'DESKTOP_ANALYSIS', 'DATA_VALIDATION', 'SME_INTERVIEW_SCHEDULED', 
       'SME_INTERVIEW_COMPLETED', 'FINAL_REVIEW', 'APPLICATION_APPROVAL', 
       'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    stages.push({
      type: 'DOCUMENT_COLLECTION',
      status: currentStage === 'DOCUMENT_COLLECTION' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 6 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'DOCUMENT_COLLECTION' ? null : new Date(submissionDate.getTime() + 10 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Complete financial statements', 'Well-structured business plan'],
        weaknesses: ['Missing tax clearance', 'Incomplete ownership documentation'],
        opportunities: ['Clear growth strategy', 'Innovative product/service'],
        threats: ['Pending legal matters', 'Regulatory compliance issues'],
        generalNotes: ['Document collection phase completed', 'Ready for desktop analysis']
      },
      requiredScore: 75,
      score: currentStage === 'DOCUMENT_COLLECTION' ? null : Math.floor(Math.random() * 20) + 70,
      assignedTo: assignees[(i + 2) % assignees.length]
    });
  }
  
  // Add desktop analysis stage if applicable
  if (['DESKTOP_ANALYSIS', 'DATA_VALIDATION', 'SME_INTERVIEW_SCHEDULED', 
       'SME_INTERVIEW_COMPLETED', 'FINAL_REVIEW', 'APPLICATION_APPROVAL', 
       'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    stages.push({
      type: 'DESKTOP_ANALYSIS',
      status: currentStage === 'DESKTOP_ANALYSIS' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 11 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'DESKTOP_ANALYSIS' ? null : new Date(submissionDate.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Strong financial ratios', 'Realistic projections'],
        weaknesses: ['High debt-to-equity ratio', 'Cash flow concerns'],
        opportunities: ['Market expansion potential', 'New product development'],
        threats: ['Competitive pressure', 'Economic uncertainty'],
        generalNotes: ['Financial analysis completed', 'Business model assessment positive']
      },
      requiredScore: 70,
      score: currentStage === 'DESKTOP_ANALYSIS' ? null : Math.floor(Math.random() * 20) + 65,
      assignedTo: assignees[(i + 3) % assignees.length]
    });
  }
  
  // Add data validation stage if applicable
  if (['DATA_VALIDATION', 'SME_INTERVIEW_SCHEDULED', 'SME_INTERVIEW_COMPLETED', 
       'FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    stages.push({
      type: 'DATA_VALIDATION',
      status: currentStage === 'DATA_VALIDATION' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 16 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'DATA_VALIDATION' ? null : new Date(submissionDate.getTime() + 18 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Verified business registration', 'Confirmed industry credentials'],
        weaknesses: ['Discrepancies in financial data', 'Incomplete reference checks'],
        opportunities: ['Positive industry outlook', 'Strong market position'],
        threats: ['Pending legal issues', 'Regulatory compliance concerns'],
        generalNotes: ['Data validation completed', 'Ready for interview phase']
      },
      requiredScore: 65,
      score: currentStage === 'DATA_VALIDATION' ? null : Math.floor(Math.random() * 20) + 60,
      assignedTo: assignees[(i + 4) % assignees.length]
    });
  }
  
  // Add interview stages if applicable
  if (['SME_INTERVIEW_SCHEDULED', 'SME_INTERVIEW_COMPLETED', 
       'FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    stages.push({
      type: 'SME_INTERVIEW_SCHEDULED',
      status: currentStage === 'SME_INTERVIEW_SCHEDULED' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 19 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'SME_INTERVIEW_SCHEDULED' ? null : new Date(submissionDate.getTime() + 21 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Prompt scheduling', 'Well-prepared applicant'],
        weaknesses: ['Limited availability', 'Communication challenges'],
        opportunities: ['Detailed business understanding', 'Clarify application details'],
        threats: ['Potential no-show', 'Incomplete information'],
        generalNotes: ['Interview scheduled successfully', 'Prepared interview questions']
      },
      requiredScore: 60,
      score: currentStage === 'SME_INTERVIEW_SCHEDULED' ? null : Math.floor(Math.random() * 20) + 60,
      assignedTo: assignees[i % assignees.length],
      scheduledDate: new Date(submissionDate.getTime() + 21 * 24 * 60 * 60 * 1000).toISOString(),
      interviewer: assignees[i % assignees.length]
    });
  }
  
  if (['SME_INTERVIEW_COMPLETED', 'FINAL_REVIEW', 
       'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    stages.push({
      type: 'SME_INTERVIEW_COMPLETED',
      status: currentStage === 'SME_INTERVIEW_COMPLETED' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 22 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'SME_INTERVIEW_COMPLETED' ? null : new Date(submissionDate.getTime() + 23 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Strong business knowledge', 'Clear growth strategy'],
        weaknesses: ['Limited financial expertise', 'Operational challenges'],
        opportunities: ['Market expansion plans', 'Innovation potential'],
        threats: ['Competitive pressures', 'Resource constraints'],
        generalNotes: ['Interview conducted successfully', 'Positive impression of management team']
      },
      requiredScore: 70,
      score: currentStage === 'SME_INTERVIEW_COMPLETED' ? null : Math.floor(Math.random() * 20) + 65,
      assignedTo: assignees[(i + 1) % assignees.length],
      completedDate: new Date(submissionDate.getTime() + 23 * 24 * 60 * 60 * 1000).toISOString(),
      recommendations: ['Proceed to site visit', 'Verify production capacity']
    });
  }
  
  // Add site visit stage if applicable
  if (['SITE_VISIT', 'FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage)) {
    stages.push({
      type: 'SME_INTERVIEW_COMPLETED', // Changed from 'SITE_VISIT' to a valid enum value
      substage: 'SITE_VISIT', // Use substage to indicate it's a site visit
      status: currentStage === 'SITE_VISIT' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 24 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'SITE_VISIT' ? null : new Date(submissionDate.getTime() + 26 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Well-established facilities', 'Organized operations'],
        weaknesses: ['Limited capacity', 'Equipment maintenance issues'],
        opportunities: ['Expansion potential', 'Process optimization'],
        threats: ['Location challenges', 'Infrastructure limitations'],
        generalNotes: ['Site visit completed', 'Verified business operations']
      },
      requiredScore: 75,
      score: currentStage === 'SITE_VISIT' ? null : Math.floor(Math.random() * 20) + 70,
      assignedTo: assignees[(i + 2) % assignees.length]
    });
  }
  
  // Add final review stage if applicable
  if (['FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage)) {
    stages.push({
      type: 'FINAL_REVIEW',
      status: currentStage === 'FINAL_REVIEW' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 27 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'FINAL_REVIEW' ? null : new Date(submissionDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Strong overall application', 'Meets funding criteria'],
        weaknesses: ['Minor documentation gaps', 'Financial projections concerns'],
        opportunities: ['High growth potential', 'Job creation impact'],
        threats: ['Market risks', 'Implementation challenges'],
        generalNotes: ['Final review completed', 'Recommendation prepared for approval committee']
      },
      requiredScore: 80,
      score: currentStage === 'FINAL_REVIEW' ? null : Math.floor(Math.random() * 15) + 75,
      assignedTo: assignees[(i + 3) % assignees.length],
      followUpActions: ['Prepare approval documentation', 'Schedule committee review']
    });
  }
  
  // Add approval stage if applicable
  if (['APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage)) {
    stages.push({
      type: 'APPLICATION_APPROVAL',
      status: currentStage === 'APPLICATION_APPROVAL' ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 31 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: currentStage === 'APPLICATION_APPROVAL' ? null : new Date(submissionDate.getTime() + 35 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: ['Strong business case', 'Alignment with funding objectives'],
        weaknesses: ['Funding amount concerns', 'Implementation timeline'],
        opportunities: ['Economic impact', 'Sector development'],
        threats: ['Disbursement risks', 'Monitoring challenges'],
        generalNotes: ['Approval process completed', 'Final decision documented']
      },
      requiredScore: 85,
      score: currentStage === 'APPLICATION_APPROVAL' ? null : Math.floor(Math.random() * 15) + 80,
      assignedTo: assignees[(i + 4) % assignees.length]
    });
  }
  
  // Add final decision stage if applicable
  if (['APPROVED', 'REJECTED'].includes(currentStage)) {
    stages.push({
      type: currentStage,
      status: 'COMPLETED',
      startedAt: new Date(submissionDate.getTime() + 36 * 24 * 60 * 60 * 1000).toISOString(),
      completedAt: new Date(submissionDate.getTime() + 37 * 24 * 60 * 60 * 1000).toISOString(),
      notes: {
        strengths: currentStage === 'APPROVED' ? ['Strong overall application', 'High impact potential'] : ['Some positive aspects', 'Potential for future applications'],
        weaknesses: currentStage === 'APPROVED' ? ['Minor concerns addressed', 'Implementation monitoring needed'] : ['Major financial concerns', 'Business model weaknesses'],
        opportunities: currentStage === 'APPROVED' ? ['Growth and expansion', 'Job creation'] : ['Reapplication with improvements', 'Alternative funding sources'],
        threats: currentStage === 'APPROVED' ? ['Implementation risks', 'Market changes'] : ['Business sustainability', 'Competitive disadvantages'],
        generalNotes: currentStage === 'APPROVED' ? ['Application approved', 'Funding disbursement scheduled'] : ['Application rejected', 'Feedback provided to applicant']
      },
      requiredScore: 85,
      score: currentStage === 'APPROVED' ? Math.floor(Math.random() * 10) + 85 : Math.floor(Math.random() * 20) + 50,
      assignedTo: assignees[i % assignees.length]
    });
  }
  
  // Create interviews array if applicable
  const interviews = [];
  if (['SME_INTERVIEW_SCHEDULED', 'SME_INTERVIEW_COMPLETED', 
       'FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    // Add initial interview
    interviews.push({
      interviewDate: new Date(submissionDate.getTime() + 21 * 24 * 60 * 60 * 1000),
      interviewer: assignees[i % assignees.length],
      participants: [`${businessName} CEO`, 'Financial Manager', 'Operations Director'],
      notes: 'Initial interview to assess business operations and management team',
      recommendations: ['Proceed with application', 'Verify financial projections', 'Schedule site visit'],
      followUpActions: ['Request additional financial documentation', 'Verify market claims'],
      attachedDocuments: ['Business presentation', 'Management profiles'],
      status: 'COMPLETED'
    });
    
    // Add follow-up interview for some applications
    if (i % 3 === 0) {
      interviews.push({
        interviewDate: new Date(submissionDate.getTime() + 25 * 24 * 60 * 60 * 1000),
        interviewer: assignees[(i + 2) % assignees.length],
        participants: [`${businessName} CEO`, 'Technical Director'],
        notes: 'Follow-up interview to clarify technical aspects of the business',
        recommendations: ['Technical capacity confirmed', 'Production capabilities verified'],
        followUpActions: ['Request technical specifications', 'Verify supplier relationships'],
        attachedDocuments: ['Technical specifications', 'Production capacity report'],
        status: 'COMPLETED'
      });
    }
  }
  
  // Create site visits array if applicable
  const siteVisits = [];
  if (['FINAL_REVIEW', 'APPLICATION_APPROVAL', 'APPROVED', 'REJECTED'].includes(currentStage) || currentStage === SITE_VISIT_STAGE) {
    siteVisits.push({
      id: `SV-${i + 1}`,
      scheduledDate: new Date(submissionDate.getTime() + 24 * 24 * 60 * 60 * 1000),
      status: currentStage === 'SITE_VISIT' ? 'IN_PROGRESS' : 'COMPLETED',
      location: {
        address: `${businessName} Headquarters, ${['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth'][i % 5]}, South Africa`,
        latitude: -26.2041 + (Math.random() * 10 - 5),
        longitude: 28.0473 + (Math.random() * 10 - 5)
      },
      conductedBy: assignees[(i + 2) % assignees.length],
      findings: currentStage === 'SITE_VISIT' ? '' : 'Business premises verified. Operations align with application details. Equipment and facilities in good condition.',
      checklist: [
        {
          id: `checklist_${i}_1`,
          category: 'Facility',
          description: 'Verify business location',
          verified: currentStage === 'SITE_VISIT' ? null : true,
          notes: currentStage === 'SITE_VISIT' ? '' : 'Location matches application details',
          createdDate: new Date(submissionDate.getTime() + 24 * 24 * 60 * 60 * 1000),
          updatedDate: new Date(submissionDate.getTime() + 26 * 24 * 60 * 60 * 1000)
        },
        {
          id: `checklist_${i}_2`,
          category: 'Operations',
          description: 'Verify production capacity',
          verified: currentStage === 'SITE_VISIT' ? null : true,
          notes: currentStage === 'SITE_VISIT' ? '' : 'Production capacity aligns with business plan',
          createdDate: new Date(submissionDate.getTime() + 24 * 24 * 60 * 60 * 1000),
          updatedDate: new Date(submissionDate.getTime() + 26 * 24 * 60 * 60 * 1000)
        },
        {
          id: `checklist_${i}_3`,
          category: 'Documentation',
          description: 'Verify business licenses and permits',
          verified: currentStage === 'SITE_VISIT' ? null : true,
          notes: currentStage === 'SITE_VISIT' ? '' : 'All licenses and permits valid and current',
          createdDate: new Date(submissionDate.getTime() + 24 * 24 * 60 * 60 * 1000),
          updatedDate: new Date(submissionDate.getTime() + 26 * 24 * 60 * 60 * 1000)
        }
      ],
      duration: 120, // 2 hours
      createdDate: new Date(submissionDate.getTime() + 23 * 24 * 60 * 60 * 1000),
      updatedDate: new Date(submissionDate.getTime() + 26 * 24 * 60 * 60 * 1000)
    });
    
    // Add follow-up site visit for some applications
    if (i % 4 === 0) {
      siteVisits.push({
        id: `SV-${i + 1}-Follow`,
        scheduledDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000),
        status: 'COMPLETED',
        location: {
          address: `${businessName} Production Facility, Industrial Area, ${['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth'][i % 5]}, South Africa`,
          latitude: -26.2041 + (Math.random() * 10 - 5),
          longitude: 28.0473 + (Math.random() * 10 - 5)
        },
        conductedBy: assignees[(i + 3) % assignees.length],
        findings: 'Follow-up visit to verify production capacity and quality control processes. All systems operational and meet industry standards.',
        checklist: [
          {
            id: `checklist_${i}_4`,
            category: 'Production',
            description: 'Verify production processes',
            verified: true,
            notes: 'Production processes align with industry standards',
            createdDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000),
            updatedDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000)
          },
          {
            id: `checklist_${i}_5`,
            category: 'Quality Control',
            description: 'Verify quality control systems',
            verified: true,
            notes: 'Quality control systems in place and operational',
            createdDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000),
            updatedDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000)
          }
        ],
        duration: 90, // 1.5 hours
        createdDate: new Date(submissionDate.getTime() + 27 * 24 * 60 * 60 * 1000),
        updatedDate: new Date(submissionDate.getTime() + 28 * 24 * 60 * 60 * 1000)
      });
    }
  }
  
  // Create the application object
  sampleApplications.push({
    id: `APP-2025-${String(i + 1).padStart(3, '0')}`,
    registrationNumber: `REG${100000 + i}`,
    fundingAmount: fundingAmount,
    submissionDate: submissionDate,
    currentStage: currentStage,
    substage: substage,
    status: status,
    owner: '',
    personalInfo: {
      firstName: `First${i + 1}`,
      lastName: `Last${i + 1}`,
      email: `email${i + 1}@example.com`,
      phone: `+27 8${i % 10} ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 9000) + 1000}`,
      idNumber: `${Math.floor(Math.random() * 9000) + 1000}${Math.floor(Math.random() * 900000) + 100000}`,
      address: {
        street: `${Math.floor(Math.random() * 1000) + 1} Main Street`,
        city: ['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth'][i % 5],
        province: ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Free State'][i % 5],
        postalCode: `${Math.floor(Math.random() * 9000) + 1000}`,
        country: 'South Africa'
      }
    },
    businessInfo: {
      legalName: businessName,
      tradingName: `Trading as ${businessName}`,
      registrationNumber: `REG${100000 + i}`,
      vatNumber: `VAT${200000 + i}`,
      industry: industry,
      businessType: ['Sole Proprietorship', 'Partnership', 'Private Company', 'Public Company'][i % 4],
      yearEstablished: 2015 + (i % 10),
      employeeCount: 5 + (i % 50),
      address: {
        street: `${Math.floor(Math.random() * 1000) + 1} Business Park`,
        city: ['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth'][i % 5],
        province: ['Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape', 'Free State'][i % 5],
        postalCode: `${Math.floor(Math.random() * 9000) + 1000}`,
        country: 'South Africa'
      },
      website: `https://www.${businessName.toLowerCase().replace(/\s+/g, '')}.co.za`
    },
    financialInfo: {
      annualTurnover: fundingAmount * 2,
      netProfit: fundingAmount * 0.2,
      currentAssets: fundingAmount * 1.5,
      currentLiabilities: fundingAmount * 0.8,
      totalDebt: fundingAmount * 0.5,
      fundingPurpose: `Expansion and growth for ${businessName}`,
      fundingAmount: fundingAmount
    },
    documents: [],
    notes: [],
    tags: [],
    stages: stages,
    smeInterview: interviews.length > 0 ? {
      interviewDate: interviews[0].interviewDate,
      interviewer: interviews[0].interviewer,
      participants: interviews[0].participants,
      notes: interviews[0].notes,
      recommendations: interviews[0].recommendations,
      followUpActions: interviews[0].followUpActions,
      attachedDocuments: interviews[0].attachedDocuments,
      status: interviews[0].status
    } : undefined,
    siteVisits: siteVisits,
    score: score,
    assignedTo: assignees[i % assignees.length],
    lastUpdated: lastUpdated
  });
}

// Validate application data before insertion
const validateApplicationData = (application) => {
  const errors = [];
  
  // Check required fields
  if (!application.id) errors.push('Application ID is required');
  if (!application.registrationNumber) errors.push('Registration number is required');
  if (!application.submissionDate) errors.push('Submission date is required');
  if (!application.currentStage) errors.push('Current stage is required');
  if (!application.status) errors.push('Status is required');
  
  // Validate relationships between status and stage
  if (application.status === 'approved' && application.currentStage !== 'APPROVED') {
    errors.push(`Status 'approved' must have currentStage 'APPROVED', got '${application.currentStage}'`);
  }
  if (application.status === 'rejected' && application.currentStage !== 'REJECTED') {
    errors.push(`Status 'rejected' must have currentStage 'REJECTED', got '${application.currentStage}'`);
  }
  
  // Validate stages array
  if (!application.stages || !Array.isArray(application.stages) || application.stages.length === 0) {
    errors.push('Application must have at least one stage');
  } else {
    // Check if current stage exists in stages array
    const stageTypes = application.stages.map(stage => stage.type);
    if (!stageTypes.includes(application.currentStage)) {
      errors.push(`Current stage '${application.currentStage}' not found in stages array`);
    }
    
    // Check stage progression logic
    for (let i = 0; i < application.stages.length; i++) {
      const stage = application.stages[i];
      
      // Check required stage fields
      if (!stage.type) errors.push(`Stage ${i} is missing type`);
      if (!stage.status) errors.push(`Stage ${i} is missing status`);
      if (!stage.startedAt) errors.push(`Stage ${i} is missing startedAt`);
      
      // Check stage completion logic
      if (stage.status === 'COMPLETED' && !stage.completedAt) {
        errors.push(`Stage ${stage.type} has status COMPLETED but no completedAt date`);
      }
      if (stage.status === 'IN_PROGRESS' && stage.completedAt) {
        errors.push(`Stage ${stage.type} has status IN_PROGRESS but has a completedAt date`);
      }
      
      // Check score logic
      if (stage.status === 'COMPLETED' && stage.requiredScore && !stage.score) {
        errors.push(`Completed stage ${stage.type} has requiredScore but no actual score`);
      }
    }
  }
  
  // Validate site visits if present
  if (application.siteVisits && application.siteVisits.length > 0) {
    application.siteVisits.forEach((visit, index) => {
      if (!visit.id) errors.push(`Site visit ${index} is missing id`);
      if (!visit.scheduledDate) errors.push(`Site visit ${index} is missing scheduledDate`);
      if (!visit.status) errors.push(`Site visit ${index} is missing status`);
    });
  }
  
  return errors;
};

// Sample corporate sponsors
const sampleCorporateSponsors = [
  {
    name: 'ABC Corporation',
    industry: 'Technology',
    description: 'A leading technology company focused on innovation and sustainable development.',
    contactPerson: {
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+27 11 123 4567',
      position: 'Corporate Relations Manager'
    },
    email: '<EMAIL>',
    phone: '+27 11 123 4567',
    address: {
      street: '123 Tech Park',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196',
      country: 'South Africa'
    },
    website: 'https://www.abccorp.com',
    status: 'active',
    totalFunding: 5000000,
    activePrograms: 3,
    totalBeneficiaries: 45,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'XYZ Enterprises',
    industry: 'Manufacturing',
    description: 'A manufacturing company specializing in sustainable production methods.',
    contactPerson: {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+27 21 987 6543',
      position: 'Funding Director'
    },
    email: '<EMAIL>',
    phone: '+27 21 987 6543',
    address: {
      street: '456 Industrial Drive',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001',
      country: 'South Africa'
    },
    website: 'https://www.xyzenterprises.com',
    status: 'active',
    totalFunding: 3500000,
    activePrograms: 2,
    totalBeneficiaries: 28,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Global Innovations',
    industry: 'Healthcare',
    description: 'A healthcare innovation company focused on improving access to medical services.',
    contactPerson: {
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+27 31 456 7890',
      position: 'CSR Manager'
    },
    email: '<EMAIL>',
    phone: '+27 31 456 7890',
    address: {
      street: '789 Health Avenue',
      city: 'Durban',
      province: 'KwaZulu-Natal',
      postalCode: '4001',
      country: 'South Africa'
    },
    website: 'https://www.globalinnovations.com',
    status: 'active',
    totalFunding: 7200000,
    activePrograms: 5,
    totalBeneficiaries: 72,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: '20/20 Insight',
    industry: 'Financial Services',
    description: 'A financial services company providing funding for innovative projects and businesses.',
    contactPerson: {
      name: 'David Nkosi',
      email: '<EMAIL>',
      phone: '+27 11 555 7890',
      position: 'Managing Director'
    },
    email: '<EMAIL>',
    phone: '+27 11 555 7890',
    address: {
      street: '20 Finance Tower',
      city: 'Sandton',
      province: 'Gauteng',
      postalCode: '2196',
      country: 'South Africa'
    },
    website: 'https://www.2020insight.co.za',
    status: 'active',
    totalFunding: 10000000,
    activePrograms: 6,
    totalBeneficiaries: 120,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and seed data
const seedDatabase = async () => {
  try {
    // Validate all applications before attempting to insert
    console.log('Validating application data...');
    let validationErrors = [];
    
    sampleApplications.forEach((app, index) => {
      const errors = validateApplicationData(app);
      if (errors.length > 0) {
        validationErrors.push({
          applicationId: app.id || `Application at index ${index}`,
          errors
        });
      }
    });
    
    if (validationErrors.length > 0) {
      console.error('Validation errors found:');
      validationErrors.forEach(error => {
        console.error(`Application ${error.applicationId}:`);
        error.errors.forEach(err => console.error(`  - ${err}`));
      });
      throw new Error('Validation failed. Please fix the errors and try again.');
    }
    
    console.log('All applications validated successfully.');
    
    // Connect to MongoDB
    if (!process.env.MONGODB_URI) {
      throw new Error('MONGODB_URI environment variable is not set');
    }
    
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Seed Applications
    const existingCount = await Application.countDocuments();
    
    if (existingCount > 0) {
      console.log(`Found ${existingCount} existing applications in the database. Skipping application seed to preserve data.`);
    } else {
      console.log('No existing applications found. Seeding the database with 20 sample applications...');
      
      // Insert sample data with validation
      const result = await Application.insertMany(sampleApplications);
      console.log(`Inserted ${result.length} applications successfully`);
    }
    
    // Seed Corporate Sponsors
    const existingSponsorsCount = await CorporateSponsor.countDocuments();
    
    if (existingSponsorsCount > 0) {
      console.log(`Found ${existingSponsorsCount} existing corporate sponsors. Skipping sponsor seed to preserve data.`);
    } else {
      console.log('No existing corporate sponsors found. Seeding the database with sample sponsors...');
      
      // Insert sample sponsors
      const sponsorResult = await CorporateSponsor.insertMany(sampleCorporateSponsors);
      console.log(`Inserted ${sponsorResult.length} corporate sponsors successfully`);
      
      // Display created sponsors
      console.log('Created sponsors:');
      sponsorResult.forEach(sponsor => {
        console.log(`- ${sponsor.name} (ID: ${sponsor._id})`);
      });
    }
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (err) {
    console.error('Error seeding database:', err);
    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
