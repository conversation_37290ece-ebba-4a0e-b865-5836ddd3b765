#!/usr/bin/env node

/**
 * Environment Switcher Script
 * 
 * This script helps switch between different environment configurations
 * Usage: node switch-env.js [development|production]
 */

const fs = require('fs');
const path = require('path');

const ENVIRONMENTS = {
  development: '.env.development',
  production: '.env.production'
};

function switchEnvironment(env) {
  if (!ENVIRONMENTS[env]) {
    console.error(`❌ Invalid environment: ${env}`);
    console.log('Available environments:', Object.keys(ENVIRONMENTS).join(', '));
    process.exit(1);
  }

  const sourceFile = path.join(__dirname, ENVIRONMENTS[env]);
  const targetFile = path.join(__dirname, '.env');

  // Check if source file exists
  if (!fs.existsSync(sourceFile)) {
    console.error(`❌ Environment file not found: ${sourceFile}`);
    process.exit(1);
  }

  try {
    // Create backup of current .env if it exists
    if (fs.existsSync(targetFile)) {
      const backupFile = path.join(__dirname, '.env.backup');
      fs.copyFileSync(targetFile, backupFile);
      console.log(`📋 Backed up current .env to .env.backup`);
    }

    // Copy environment file
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`✅ Switched to ${env} environment`);
    console.log(`📁 Copied ${ENVIRONMENTS[env]} to .env`);

    // Show current environment info
    showEnvironmentInfo(env);

  } catch (error) {
    console.error(`❌ Error switching environment:`, error.message);
    process.exit(1);
  }
}

function showEnvironmentInfo(env) {
  console.log('\n📊 Current Environment Configuration:');
  console.log('=====================================');
  
  try {
    const envContent = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
    
    // Extract key information
    const extractValue = (key) => {
      const match = envContent.match(new RegExp(`^${key}=(.*)$`, 'm'));
      return match ? match[1] : 'Not set';
    };

    console.log(`Environment: ${extractValue('NODE_ENV')}`);
    console.log(`Port: ${extractValue('PORT')}`);
    console.log(`Database: ${extractValue('MONGODB_URI')}`);
    console.log(`Frontend URL: ${extractValue('FRONTEND_URL')}`);
    console.log(`WebSocket URL: ${extractValue('WEBSOCKET_URL')}`);
    console.log(`Log Level: ${extractValue('LOG_LEVEL')}`);
    console.log(`Secure Cookies: ${extractValue('SECURE_COOKIES')}`);
    
  } catch (error) {
    console.log('Could not read environment details');
  }
}

function showUsage() {
  console.log('🔧 Environment Switcher');
  console.log('=======================');
  console.log('Usage: node switch-env.js [environment]');
  console.log('');
  console.log('Available environments:');
  Object.keys(ENVIRONMENTS).forEach(env => {
    console.log(`  ${env.padEnd(12)} - ${ENVIRONMENTS[env]}`);
  });
  console.log('');
  console.log('Examples:');
  console.log('  node switch-env.js development');
  console.log('  node switch-env.js production');
}

function getCurrentEnvironment() {
  const envFile = path.join(__dirname, '.env');
  
  if (!fs.existsSync(envFile)) {
    console.log('❌ No .env file found');
    return;
  }

  try {
    const content = fs.readFileSync(envFile, 'utf8');
    const nodeEnvMatch = content.match(/^NODE_ENV=(.*)$/m);
    const currentEnv = nodeEnvMatch ? nodeEnvMatch[1] : 'unknown';
    
    console.log(`📍 Current environment: ${currentEnv}`);
    showEnvironmentInfo(currentEnv);
  } catch (error) {
    console.error('❌ Error reading current environment:', error.message);
  }
}

// Main execution
const args = process.argv.slice(2);

if (args.length === 0) {
  showUsage();
  console.log('\n');
  getCurrentEnvironment();
} else if (args[0] === '--current' || args[0] === '-c') {
  getCurrentEnvironment();
} else if (args[0] === '--help' || args[0] === '-h') {
  showUsage();
} else {
  switchEnvironment(args[0]);
}
