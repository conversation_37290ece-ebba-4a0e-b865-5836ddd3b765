const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  id: {
    type: String,
    unique: true,
    sparse: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    required: true,
    enum: [
      'APPLICATION_STATUS_CHANGE',
      'INTERVIEW_SCHEDULED',
      'SITE_VISIT_SCHEDULED',
      'DOCUMENT_REQUIRED',
      'APPROVAL_REQUIRED',
      'DEADLINE_REMINDER',
      'SYSTEM_ANNOUNCEMENT',
      'MEETING_INVITATION',
      'REPORT_READY',
      'SCORECARD_COMPLETED',
      'CHAT_MESSAGE',
      'CHAT_MENTION',
      'CUSTOM'
    ]
  },
  category: {
    type: String,
    required: true,
    enum: ['APPLICATION', 'INTERVIEW', 'SITE_VISIT', 'DOCUMENT', 'APPROVAL', 'SYSTEM', 'MEETING', 'REPORT', 'SCORECARD', 'CHAT', 'GENERAL']
  },
  priority: {
    type: String,
    required: true,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'URGENT'],
    default: 'MEDIUM'
  },
  // Delivery methods
  deliveryMethods: {
    inApp: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: false
    },
    sms: {
      type: Boolean,
      default: false
    },
    push: {
      type: Boolean,
      default: false
    }
  },
  // Scheduling
  scheduledFor: {
    type: Date,
    default: Date.now
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'],
      required: function() { return this.isRecurring; }
    },
    interval: {
      type: Number,
      min: 1,
      default: 1
    },
    daysOfWeek: [{
      type: Number,
      min: 0,
      max: 6
    }],
    dayOfMonth: {
      type: Number,
      min: 1,
      max: 31
    },
    endDate: Date
  },
  // Targeting
  targetAudience: {
    type: String,
    required: true,
    enum: ['ALL_USERS', 'SPECIFIC_USERS', 'ROLE_BASED', 'ENTITY_BASED']
  },
  targetUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  targetRoles: [{
    type: String
  }],
  targetEntities: {
    corporateSponsors: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'CorporateSponsor'
    }],
    fundingProgrammes: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'FundingProgramme'
    }],
    serviceProviders: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ServiceProvider'
    }]
  },
  // Related entities
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['APPLICATION', 'INTERVIEW', 'SITE_VISIT', 'DOCUMENT', 'MEETING', 'REPORT', 'SCORECARD', 'USER', 'CHAT_MESSAGE', 'CHAT_ROOM']
    },
    entityId: String,
    entityData: mongoose.Schema.Types.Mixed
  },
  // Action buttons
  actions: [{
    label: {
      type: String,
      required: true,
      maxlength: 50
    },
    action: {
      type: String,
      required: true,
      enum: ['NAVIGATE', 'API_CALL', 'DOWNLOAD', 'EXTERNAL_LINK']
    },
    url: String,
    method: {
      type: String,
      enum: ['GET', 'POST', 'PUT', 'DELETE'],
      default: 'GET'
    },
    payload: mongoose.Schema.Types.Mixed,
    style: {
      type: String,
      enum: ['PRIMARY', 'SECONDARY', 'SUCCESS', 'WARNING', 'DANGER'],
      default: 'PRIMARY'
    }
  }],
  // Template reference
  templateId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NotificationTemplate'
  },
  // Status and tracking
  status: {
    type: String,
    required: true,
    enum: ['DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'FAILED', 'CANCELLED'],
    default: 'DRAFT'
  },
  deliveryStatus: {
    inApp: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String
    },
    email: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String
    },
    sms: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String
    },
    push: {
      status: {
        type: String,
        enum: ['PENDING', 'DELIVERED', 'FAILED'],
        default: 'PENDING'
      },
      deliveredAt: Date,
      error: String
    }
  },
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  sentAt: Date,
  // Analytics
  analytics: {
    totalRecipients: {
      type: Number,
      default: 0
    },
    deliveredCount: {
      type: Number,
      default: 0
    },
    readCount: {
      type: Number,
      default: 0
    },
    clickCount: {
      type: Number,
      default: 0
    },
    lastAnalyticsUpdate: Date
  }
});

// Indexes for performance
notificationSchema.index({ status: 1, scheduledFor: 1 });
notificationSchema.index({ type: 1, category: 1 });
notificationSchema.index({ createdBy: 1, createdAt: -1 });
notificationSchema.index({ targetUsers: 1 });
notificationSchema.index({ targetRoles: 1 });
notificationSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });
notificationSchema.index({ templateId: 1 });

// Pre-save middleware to generate notification ID
notificationSchema.pre('save', async function(next) {
  try {
    // Generate notification ID if not present
    if (!this.id) {
      const count = await this.constructor.countDocuments();
      const nextNumber = (count + 1).toString().padStart(6, '0');
      this.id = `NOTIF-2025-${nextNumber}`;
    }
    
    this.updatedAt = Date.now();
    next();
  } catch (error) {
    next(error);
  }
});

// Virtual for formatted scheduling info
notificationSchema.virtual('schedulingInfo').get(function() {
  if (!this.isRecurring) {
    return {
      type: 'one-time',
      scheduledFor: this.scheduledFor
    };
  }
  
  return {
    type: 'recurring',
    pattern: this.recurringPattern,
    nextScheduled: this.scheduledFor
  };
});

// Method to check if notification should be sent now
notificationSchema.methods.shouldSendNow = function() {
  const now = new Date();
  return this.status === 'SCHEDULED' && this.scheduledFor <= now;
};

// Method to get target user count
notificationSchema.methods.getTargetUserCount = async function() {
  const User = mongoose.model('User');
  
  switch (this.targetAudience) {
    case 'ALL_USERS':
      return await User.countDocuments({ isActive: true });
    
    case 'SPECIFIC_USERS':
      return this.targetUsers.length;
    
    case 'ROLE_BASED':
      return await User.countDocuments({
        isActive: true,
        $or: [
          { roles: { $in: this.targetRoles } },
          { role: { $in: this.targetRoles } }
        ]
      });
    
    case 'ENTITY_BASED':
      const entityFilters = [];
      
      if (this.targetEntities.corporateSponsors.length > 0) {
        entityFilters.push({
          corporateSponsorId: { $in: this.targetEntities.corporateSponsors }
        });
      }
      
      if (this.targetEntities.fundingProgrammes.length > 0) {
        entityFilters.push({
          fundingProgrammeId: { $in: this.targetEntities.fundingProgrammes }
        });
      }
      
      if (entityFilters.length === 0) return 0;
      
      return await User.countDocuments({
        isActive: true,
        $or: entityFilters
      });
    
    default:
      return 0;
  }
};

// Method to get target users
notificationSchema.methods.getTargetUsers = async function() {
  const User = mongoose.model('User');
  
  switch (this.targetAudience) {
    case 'ALL_USERS':
      return await User.find({ isActive: true }).select('_id username email firstName lastName roles role organizationType corporateSponsorId fundingProgrammeId');
    
    case 'SPECIFIC_USERS':
      return await User.find({
        _id: { $in: this.targetUsers },
        isActive: true
      }).select('_id username email firstName lastName roles role organizationType corporateSponsorId fundingProgrammeId');
    
    case 'ROLE_BASED':
      return await User.find({
        isActive: true,
        $or: [
          { roles: { $in: this.targetRoles } },
          { role: { $in: this.targetRoles } }
        ]
      }).select('_id username email firstName lastName roles role organizationType corporateSponsorId fundingProgrammeId');
    
    case 'ENTITY_BASED':
      const entityFilters = [];
      
      if (this.targetEntities.corporateSponsors.length > 0) {
        entityFilters.push({
          corporateSponsorId: { $in: this.targetEntities.corporateSponsors }
        });
      }
      
      if (this.targetEntities.fundingProgrammes.length > 0) {
        entityFilters.push({
          fundingProgrammeId: { $in: this.targetEntities.fundingProgrammes }
        });
      }
      
      if (entityFilters.length === 0) return [];
      
      return await User.find({
        isActive: true,
        $or: entityFilters
      }).select('_id username email firstName lastName roles role organizationType corporateSponsorId fundingProgrammeId');
    
    default:
      return [];
  }
};

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;