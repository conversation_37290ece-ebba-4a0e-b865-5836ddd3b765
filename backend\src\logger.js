/**
 * Simple logger module
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
};

// Simple console logger
const logger = {
  error: (message, error) => {
    console.error(`[${LOG_LEVELS.ERROR}] ${message}`, error || '');
  },
  
  warn: (message) => {
    console.warn(`[${LOG_LEVELS.WARN}] ${message}`);
  },
  
  info: (message) => {
    console.info(`[${LOG_LEVELS.INFO}] ${message}`);
  },
  
  debug: (message) => {
    console.debug(`[${LOG_LEVELS.DEBUG}] ${message}`);
  }
};

module.exports = logger;